#ifndef __AFE_TEST_H
#define __AFE_TEST_H

#include "stm32f10x.h"

// AFE板测试相关定义
#define AFE_ADC_CHANNELS    4       // AFE板输出通道数
#define AFE_SAMPLE_RATE     1000    // 采样率 (Hz)
#define AFE_BUFFER_SIZE     256     // 数据缓冲区大小

// ADC通道定义 (根据实际硬件连接修改)
#define AFE_CH1_ADC_CHANNEL    ADC_Channel_0    // PA0
#define AFE_CH2_ADC_CHANNEL    ADC_Channel_1    // PA1  
#define AFE_CH3_ADC_CHANNEL    ADC_Channel_2    // PA2
#define AFE_CH4_ADC_CHANNEL    ADC_Channel_3    // PA3

// AFE测试模式
typedef enum {
    AFE_TEST_SINGLE_CHANNEL = 0,    // 单通道测试
    AFE_TEST_MULTI_CHANNEL,         // 多通道测试
    AFE_TEST_CONTINUOUS,            // 连续采集测试
    AFE_TEST_PERFORMANCE,           // 性能测试
    AFE_TEST_CALIBRATION            // 校准测试
} AFE_TestMode_t;

// AFE数据结构
typedef struct {
    uint16_t raw_value;             // 原始ADC值
    float voltage;                  // 电压值 (V)
    float current;                  // 电流值 (mA) - 如果AFE是电流输出
    uint8_t channel;                // 通道号
    uint32_t timestamp;             // 时间戳
    uint8_t valid;                  // 数据有效标志
} AFE_Data_t;

// AFE测试结果
typedef struct {
    float min_voltage;              // 最小电压
    float max_voltage;              // 最大电压
    float avg_voltage;              // 平均电压
    float rms_voltage;              // RMS电压
    float noise_level;              // 噪声水平
    uint16_t sample_count;          // 采样计数
} AFE_TestResult_t;

// AFE配置参数
typedef struct {
    AFE_TestMode_t test_mode;       // 测试模式
    uint8_t active_channels;        // 激活的通道掩码
    uint16_t sample_rate;           // 采样率
    uint8_t gain_setting;           // 增益设置
    float reference_voltage;        // 参考电压
} AFE_Config_t;

// 函数声明
void AFE_Init(void);
void AFE_ADC_Init(void);
void AFE_Start_Test(AFE_TestMode_t mode);
void AFE_Stop_Test(void);
uint8_t AFE_Read_Channel(uint8_t channel, AFE_Data_t* data);
uint8_t AFE_Read_All_Channels(AFE_Data_t* data_array);
void AFE_Process_Data(AFE_Data_t* data, AFE_TestResult_t* result);
void AFE_Display_Results(AFE_TestResult_t* results);
void AFE_Calibrate(void);
float AFE_Convert_To_Voltage(uint16_t adc_value);
void AFE_Test_Task(void);

// 全局变量声明
extern AFE_Config_t g_afe_config;
extern AFE_Data_t g_afe_data[AFE_ADC_CHANNELS];
extern AFE_TestResult_t g_afe_results[AFE_ADC_CHANNELS];

#endif /* __AFE_TEST_H */
