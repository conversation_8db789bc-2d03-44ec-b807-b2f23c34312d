#ifndef __AFE_TEST_H
#define __AFE_TEST_H

#include "stm32f10x.h"

//==============================================================================
// AFE板硬件连接定义 - STM32F103C8T6
//==============================================================================

// AFE板12位并行数据线连接 (PC0-PC11)
#define AFE_DATA_GPIO_PORT          GPIOC
#define AFE_DATA_GPIO_CLK           RCC_APB2Periph_GPIOC
#define AFE_DATA_PINS               (GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | \
                                     GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7 | \
                                     GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10 | GPIO_Pin_11)

// AFE板ADC时钟线连接 (PA0)
#define AFE_CLK_PIN                 GPIO_Pin_0
#define AFE_CLK_GPIO_PORT           GPIOA
#define AFE_CLK_GPIO_CLK            RCC_APB2Periph_GPIOA
#define AFE_CLK_EXTI_LINE           EXTI_Line0
#define AFE_CLK_EXTI_PORT_SOURCE    GPIO_PortSourceGPIOA
#define AFE_CLK_EXTI_PIN_SOURCE     GPIO_PinSource0
#define AFE_CLK_EXTI_IRQ            EXTI0_IRQn

//==============================================================================
// 数据缓存定义
//==============================================================================

#define AFE_BUFFER_SIZE             256             // 缓存大小
#define AFE_DATA_MASK               0x0FFF          // 12位数据掩码

//==============================================================================
// 全局变量声明
//==============================================================================

extern uint16_t g_adc_buffer[AFE_BUFFER_SIZE];      // ADC数据缓存
extern volatile uint16_t g_buffer_index;            // 缓存索引
extern volatile uint8_t g_data_ready_flag;          // 数据准备就绪标志

//==============================================================================
// 数据结构定义
//==============================================================================

// ADC统计数据结构
typedef struct {
    uint16_t current_value;         // 当前采样值
    uint16_t max_value;             // 最大值
    uint16_t min_value;             // 最小值
    uint32_t average_value;         // 平均值
    uint32_t sample_count;          // 采样计数
} ADC_Stats_t;

//==============================================================================
// 函数声明
//==============================================================================

// 初始化函数
void AFE_GPIO_Init(void);
void AFE_EXTI_Init(void);
void AFE_Test_Init(void);

// 数据处理函数
void AFE_ProcessData(ADC_Stats_t *stats);
void AFE_CalculateStats(uint16_t *buffer, uint16_t length, ADC_Stats_t *stats);

// 中断控制函数
void AFE_EnableInterrupt(void);
void AFE_DisableInterrupt(void);

// 工具函数
uint16_t AFE_ReadADCData(void);
void AFE_ClearBuffer(void);

// 显示函数
void Display_ADC_Results(ADC_Stats_t *stats);

#endif /* __AFE_TEST_H */
