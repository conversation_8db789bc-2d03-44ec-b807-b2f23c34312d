Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    main.o(i.Delay) refers to main.o(i.GetTick) for GetTick
    main.o(i.Display_ADC_Results) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.Display_ADC_Results) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.Display_ADC_Results) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.Display_ADC_Results) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.Display_ADC_Results) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Display_ADC_Results) refers to __2sprintf.o(.text) for __2sprintf
    main.o(i.Display_ADC_Results) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.Display_ADC_Results) refers to main.o(.bss) for display_buffer
    main.o(i.GetTick) refers to main.o(.data) for uwTick
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.main) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.main) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    main.o(i.main) refers to afe_test.o(i.AFE_Test_Init) for AFE_Test_Init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to main.o(i.Delay) for Delay
    main.o(i.main) refers to afe_test.o(i.AFE_ProcessData) for AFE_ProcessData
    main.o(i.main) refers to main.o(i.Display_ADC_Results) for Display_ADC_Results
    main.o(i.main) refers to main.o(i.GetTick) for GetTick
    main.o(i.main) refers to __2sprintf.o(.text) for __2sprintf
    main.o(i.main) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    main.o(i.main) refers to main.o(.bss) for adc_stats
    main.o(i.main) refers to afe_test.o(.data) for g_data_ready_flag
    main.o(i.main) refers to main.o(.data) for last_update
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to stm32f4xx_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to afe_test.o(i.AFE_ReadADCData) for AFE_ReadADCData
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to stm32f4xx_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to afe_test.o(.bss) for g_adc_buffer
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to afe_test.o(.data) for g_buffer_index
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(i.TimingDelay_Decrement) for TimingDelay_Decrement
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(.data) for uwTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    misc.o(i.NVIC_Init) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_PriorityGroupConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SetVectorTable) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SystemLPConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.SysTick_CLKSourceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_CommonInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ContinuousModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_DiscModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetConversionValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_RegularChannelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SetInjectedOffset) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SoftwareStartConv) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_adc.o(i.ADC_VBATCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_CancelTransmit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DBGFreeze) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_FIFORelease) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_FilterInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetLastErrorCode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_MessagePending) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_OperatingModeRequest) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Receive) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_SlaveStartBank) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Sleep) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_TTComModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_Transmit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_TransmitStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_can.o(i.CAN_WakeUp) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp.o(i.CRYP_PhaseConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_dac.o(i.DAC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetDataOutputValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetChannel1Data) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetChannel2Data) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SetDualChannelData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dac.o(i.DAC_WaveGenerationCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dbgmcu.o(i.DBGMCU_Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_CROPCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_CaptureCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dcmi.o(i.DCMI_JPEGCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_FlowControllerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFIFOStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_MemoryTargetConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_BGConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_BGStart) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_FGConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_FGStart) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma2d.o(i.DMA2D_Suspend) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_exti.o(i.EXTI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_DataCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BORConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BootConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_SetLatency) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_fsmc.o(i.FSMC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDDeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NANDInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_fsmc.o(i.FSMC_PCCARDInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_gpio.o(i.GPIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinAFConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinLockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ResetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_SetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ToggleBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_Write) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_WriteBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_AutoStartDigest) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash.o(i.HASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_i2c.o(i.I2C_ARPCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_CalculatePEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_CheckEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_DualAddressCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GeneralCallCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GenerateSTART) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GenerateSTOP) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetLastEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_GetPEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_i2c.o(i.I2C_NACKPositionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_OwnAddress2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_PECPositionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ReadRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_Send7bitAddress) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_StretchClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_i2c.o(i.I2C_TransmitPEC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_SetReload) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_CLUTCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_CLUTInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_ltdc.o(i.LTDC_DitherCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetCDStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LIPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LayerCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_LayerInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_ltdc.o(i.LTDC_ReloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_BackupAccessCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_pwr.o(i.PWR_EnterSTOPMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_OverDriveCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_PVDCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_PVDLevelConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_UnderDriveCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_BackupResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_I2SCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_rng.o(i.RNG_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_rng.o(i.RNG_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rng.o(i.RNG_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_AlarmCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_BypassShadowCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CalibOutputCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CalibOutputConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_OutputConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_OutputTypeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_ReadBackupRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_TamperCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperFilterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPinSelection) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WakeUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WriteBackupRegister) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_CompandingModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sai.o(i.SAI_FlushFIFO) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_FrameInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetFIFOStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MonoModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_MuteValueConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_SlotInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sai.o(i.SAI_TRIStateConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_CEATAITCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DataConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(i.SDIO_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_GetResponse) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendCEATACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendCommand) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetPowerState) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_FullDuplexConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.I2S_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_CalculateCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_DataSizeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_GetCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_GetCRCPolynomial) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_I2S_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_SSOutputCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_TIModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_spi.o(i.SPI_TransmitCRC) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_BDTRConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCPreloadControl) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCxCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CCxNCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC1Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC2Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC3Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ClearOC4Ref) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CounterModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DMAConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC3Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ForcedOC4Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GenerateEvent) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture1) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture2) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture3) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCapture4) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_GetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ICInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_InternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC3PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4FastConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4PolarityConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_OC4PreloadConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_PrescalerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_RemapConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectCCDMA) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectCOM) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectHallSensor) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectInputTrigger) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOCxM) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOnePulseMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SelectSlaveMode) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetAutoreload) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetClockDivision) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare1) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare2) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare3) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCompare4) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_TimeBaseInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_UpdateDisableConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_tim.o(i.TIM_UpdateRequestConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClockInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_HalfDuplexCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_usart.o(i.USART_IrDACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_IrDAConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OneBitMethodCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OverSampling8Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendBreak) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetAddress) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetGuardTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardNACKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_WakeUpConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_wwdg.o(i.WWDG_Enable) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_wwdg.o(i.WWDG_SetWindowValue) refers to main.o(i.assert_failed) for assert_failed
    afe_test.o(i.AFE_ClearBuffer) refers to afe_test.o(.bss) for g_adc_buffer
    afe_test.o(i.AFE_ClearBuffer) refers to afe_test.o(.data) for g_buffer_index
    afe_test.o(i.AFE_EXTI_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    afe_test.o(i.AFE_EXTI_Init) refers to stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig) for SYSCFG_EXTILineConfig
    afe_test.o(i.AFE_EXTI_Init) refers to stm32f4xx_exti.o(i.EXTI_Init) for EXTI_Init
    afe_test.o(i.AFE_EXTI_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    afe_test.o(i.AFE_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    afe_test.o(i.AFE_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    afe_test.o(i.AFE_ProcessData) refers to afe_test.o(i.AFE_DisableInterrupt) for AFE_DisableInterrupt
    afe_test.o(i.AFE_ProcessData) refers to afe_test.o(i.AFE_CalculateStats) for AFE_CalculateStats
    afe_test.o(i.AFE_ProcessData) refers to afe_test.o(i.AFE_EnableInterrupt) for AFE_EnableInterrupt
    afe_test.o(i.AFE_ProcessData) refers to afe_test.o(.data) for g_data_ready_flag
    afe_test.o(i.AFE_ProcessData) refers to afe_test.o(.bss) for g_adc_buffer
    afe_test.o(i.AFE_Test_Init) refers to afe_test.o(i.AFE_GPIO_Init) for AFE_GPIO_Init
    afe_test.o(i.AFE_Test_Init) refers to afe_test.o(i.AFE_EXTI_Init) for AFE_EXTI_Init
    afe_test.o(i.AFE_Test_Init) refers to afe_test.o(i.AFE_ClearBuffer) for AFE_ClearBuffer
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i.OLED_DrawEllipse) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    oled.o(i.OLED_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_SendByte) refers to delay.o(i.Delay_us) for Delay_us
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Start) refers to delay.o(i.Delay_us) for Delay_us
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Stop) refers to delay.o(i.Delay_us) for Delay_us
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_GPIO_Init) for OLED_GPIO_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i.OLED_KillArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    oled.o(i.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.__hardfp_round) for __hardfp_round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to strcmpv7m.o(.text) for strcmp
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowString) refers to oled_data.o(.constdata) for OLED_CF16x16
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_W_SCL) refers to stm32f4xx_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_W_SDA) refers to stm32f4xx_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    atan2.o(i.__hardfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    round.o(i.__hardfp_round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.__hardfp_round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.__hardfp_round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.__hardfp_round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.__hardfp_round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.__hardfp_round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.SystemClock_Config), (2 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (80 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (88 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (92 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (80 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (152 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (172 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (100 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (124 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Cmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonInit), (380 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMACmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (192 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (120 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (76 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (128 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (160 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (124 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (92 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (92 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (168 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Init), (448 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (368 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (112 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (88 bytes).
    Removing stm32f4xx_adc.o(i.ADC_RegularChannelConfig), (444 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (140 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartConv), (80 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (80 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (88 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (88 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (120 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (220 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (304 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (104 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (104 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (96 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (396 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (328 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (428 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (72 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (184 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (580 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (112 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (248 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (312 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (100 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (92 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (184 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (464 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (232 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (108 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f4xx_cec.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cec.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cec.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Cmd), (84 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DMACmd), (108 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataIn), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataOut), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DeInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_FIFOFlush), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetCmdStatus), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetFlagStatus), (112 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetITStatus), (76 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_ITConfig), (108 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVInit), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVStructInit), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Init), (312 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyInit), (40 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyStructInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_PhaseConfig), (84 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_RestoreContext), (148 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_SaveContext), (264 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_StructInit), (12 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC), (540 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM), (1778 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR), (466 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB), (494 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM), (1308 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_CBC), (250 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_ECB), (226 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC), (282 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB), (258 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Cmd), (104 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DMACmd), (108 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (80 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (96 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (116 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (124 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Init), (388 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel1Data), (100 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel2Data), (100 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (136 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (108 bytes).
    Removing stm32f4xx_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (128 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig), (104 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig), (104 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_Config), (100 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPConfig), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CaptureCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearFlag), (64 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Cmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_DeInit), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus), (172 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetITStatus), (92 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ITConfig), (108 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Init), (264 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_JPEGCmd), (84 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ReadData), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_SetEmbeddedSynchroCodes), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_StructInit), (18 bytes).
    Removing stm32f4xx_dfsdm.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (256 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearITPendingBit), (256 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Cmd), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DeInit), (508 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (220 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (180 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (184 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (580 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetITStatus), (684 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ITConfig), (272 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Init), (696 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (208 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (180 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_AbortTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGConfig), (424 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGStart), (84 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearFlag), (80 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit), (92 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeInit), (22 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig), (120 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGConfig), (424 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGStart), (84 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus), (92 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetITStatus), (124 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ITConfig), (132 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Init), (452 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig), (60 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StartTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StructInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Suspend), (84 bytes).
    Removing stm32f4xx_dsi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (60 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (68 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (196 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (68 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (300 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (140 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (148 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (160 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_SetLatency), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashInterfaceCmd), (36 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashSleepModeCmd), (36 bytes).
    Removing stm32f4xx_fmpi2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearFlag), (148 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit), (152 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus), (148 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetITStatus), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ITConfig), (228 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDCmd), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDDeInit), (120 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDInit), (592 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd), (128 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit), (112 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit), (904 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit), (52 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd), (96 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDInit), (532 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (416 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinAFConfig), (424 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (204 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit), (268 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (268 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ResetBits), (176 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_SetBits), (176 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (164 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (160 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash.o(i.HASH_AutoStartDigest), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearFlag), (64 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DMACmd), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DataIn), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DeInit), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetDigest), (72 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetFlagStatus), (108 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetITStatus), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetInFIFOWordsNbr), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ITConfig), (104 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Init), (232 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Reset), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_RestoreContext), (68 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SaveContext), (60 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr), (76 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StartDigest), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StructInit), (12 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_md5.o(i.HASH_MD5), (180 bytes).
    Removing stm32f4xx_hash_md5.o(i.HMAC_MD5), (354 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HASH_SHA1), (186 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HMAC_SHA1), (362 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ARPCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CalculatePEC), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CheckEvent), (316 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearFlag), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearITPendingBit), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Cmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMACmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DeInit), (148 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DualAddressCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GeneralCallCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTART), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTOP), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetFlagStatus), (300 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetITStatus), (244 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetLastEvent), (100 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetPEC), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ITConfig), (132 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Init), (440 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_NACKPositionConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_OwnAddress2Config), (92 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_PECPositionConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReadRegister), (136 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReceiveData), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig), (128 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Send7bitAddress), (108 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SendData), (76 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd), (112 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StretchClockCmd), (116 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_TransmitPEC), (116 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus), (76 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetPrescaler), (80 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetReload), (60 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd), (64 bytes).
    Removing stm32f4xx_lptim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTCmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTInit), (144 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTStructInit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearFlag), (72 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Cmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig), (180 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DeInit), (22 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DitherCmd), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetCDStatus), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus), (84 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetITStatus), (96 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetPosStatus), (44 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetRGBWidth), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ITConfig), (104 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Init), (572 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LIPConfig), (60 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAddress), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAlpha), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerCmd), (76 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerInit), (540 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPixelFormat), (106 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPosition), (160 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerSize), (116 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerStructInit), (48 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_PosStructInit), (8 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_RGBStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ReloadConfig), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_StructInit), (34 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupAccessCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_ClearFlag), (72 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTOPMode), (132 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode), (140 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig), (80 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDCmd), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDLevelConfig), (88 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_UnderDriveCmd), (80 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd), (56 bytes).
    Removing stm32f4xx_qspi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (68 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (52 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetClocksFreq), (232 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (164 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (68 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (80 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (184 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (116 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (412 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (72 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (72 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f4xx_rcc.o(.data), (16 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearFlag), (64 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearITPendingBit), (64 bytes).
    Removing stm32f4xx_rng.o(i.RNG_Cmd), (80 bytes).
    Removing stm32f4xx_rng.o(i.RNG_DeInit), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetFlagStatus), (76 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetITStatus), (72 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetRandomNumber), (12 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ITConfig), (80 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmCmd), (180 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmStructInit), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig), (232 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Bcd2ToByte), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_BypassShadowCmd), (104 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ByteToBcd2), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputCmd), (104 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputConfig), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearFlag), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearITPendingBit), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig), (116 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DateStructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig), (128 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DeInit), (212 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_EnterInitMode), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ExitInitMode), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarm), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarmSubSecond), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetDate), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetFlagStatus), (152 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetITStatus), (148 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetStoreOperation), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetSubSecond), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTime), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStamp), (192 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStampSubSecond), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetWakeUpCounter), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ITConfig), (172 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Init), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputConfig), (136 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputTypeConfig), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ReadBackupRegister), (144 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_RefClockCmd), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetAlarm), (684 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetDate), (420 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetTime), (452 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter), (72 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig), (200 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_StructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig), (188 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperCmd), (100 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperFilterConfig), (88 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinSelection), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration), (88 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig), (112 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampCmd), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStructInit), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WaitForSynchro), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpCmd), (164 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteBackupRegister), (148 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd), (72 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearFlag), (108 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearITPendingBit), (108 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Cmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_CompandingModeConfig), (116 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DMACmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DeInit), (76 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FlushFIFO), (68 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameInit), (224 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameStructInit), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetCmdStatus), (80 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFIFOStatus), (72 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFlagStatus), (120 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetITStatus), (132 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ITConfig), (140 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Init), (432 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MonoModeConfig), (80 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig), (96 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteModeCmd), (100 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteValueConfig), (96 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ReceiveData), (64 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SendData), (64 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotInit), (180 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotStructInit), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_StructInit), (30 bytes).
    Removing stm32f4xx_sai.o(i.SAI_TRIStateConfig), (96 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CEATAITCmd), (64 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearFlag), (68 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit), (68 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClockCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DMACmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataConfig), (268 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DeInit), (22 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFlagStatus), (196 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetITStatus), (196 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetResponse), (80 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ITConfig), (104 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_Init), (208 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCEATACmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCommand), (180 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetPowerState), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait), (60 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f4xx_spdifrx.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Cmd), (120 bytes).
    Removing stm32f4xx_spi.o(i.I2S_FullDuplexConfig), (276 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Init), (608 bytes).
    Removing stm32f4xx_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig), (156 bytes).
    Removing stm32f4xx_spi.o(i.SPI_CalculateCRC), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Cmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_DataSizeConfig), (140 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRC), (136 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRCPolynomial), (104 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearFlag), (140 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit), (152 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DMACmd), (180 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DeInit), (248 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus), (188 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetITStatus), (220 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ITConfig), (204 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ReceiveData), (120 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_SendData), (124 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Init), (416 bytes).
    Removing stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig), (160 bytes).
    Removing stm32f4xx_spi.o(i.SPI_SSOutputCmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TIModeCmd), (144 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TransmitCRC), (112 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (60 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (60 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (68 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (60 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (58 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (256 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearITPendingBit), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_Cmd), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (112 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (316 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (516 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (196 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (208 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (280 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (196 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (120 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (264 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetITStatus), (264 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (528 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (128 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (468 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (96 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (192 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (468 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (444 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (336 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (236 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (128 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (140 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (156 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (320 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (216 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOutputTrigger), (180 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (184 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (204 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (172 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (144 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (152 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (252 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseInit), (356 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (220 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (220 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (220 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (256 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (212 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_Cmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (208 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (344 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetFlagStatus), (264 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetITStatus), (372 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_ITConfig), (352 bytes).
    Removing stm32f4xx_usart.o(i.USART_Init), (556 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiveData), (152 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (156 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendData), (172 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (180 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (104 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (164 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (184 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_Enable), (64 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetCounter), (64 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetPrescaler), (84 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetWindowValue), (84 bytes).
    Removing afe_test.o(.rev16_text), (4 bytes).
    Removing afe_test.o(.revsh_text), (4 bytes).
    Removing afe_test.o(.rrx_text), (6 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing delay.o(i.Delay_ms), (24 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_DrawArc), (618 bytes).
    Removing oled.o(i.OLED_DrawCircle), (352 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (868 bytes).
    Removing oled.o(i.OLED_DrawLine), (374 bytes).
    Removing oled.o(i.OLED_DrawPoint), (80 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (142 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (232 bytes).
    Removing oled.o(i.OLED_GetPoint), (68 bytes).
    Removing oled.o(i.OLED_IsInAngle), (160 bytes).
    Removing oled.o(i.OLED_KillArea), (144 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_Printf), (50 bytes).
    Removing oled.o(i.OLED_Reverse), (52 bytes).
    Removing oled.o(i.OLED_ReverseArea), (144 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (70 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (256 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (96 bytes).
    Removing oled.o(i.OLED_ShowNum), (76 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (112 bytes).
    Removing oled.o(i.OLED_UpdateArea), (124 bytes).
    Removing oled.o(i.OLED_pnpoly), (140 bytes).

865 unused section(s) (total 100988 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    HardWare\Delay.c                         0x00000000   Number         0  delay.o ABSOLUTE
    HardWare\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    HardWare\OLED_Data.c                     0x00000000   Number         0  oled_data.o ABSOLUTE
    HardWare\\Delay.c                        0x00000000   Number         0  delay.o ABSOLUTE
    HardWare\\OLED.c                         0x00000000   Number         0  oled.o ABSOLUTE
    HardWare\\afe_test.c                     0x00000000   Number         0  afe_test.o ABSOLUTE
    HardWare\afe_test.c                      0x00000000   Number         0  afe_test.o ABSOLUTE
    Library\\misc.c                          0x00000000   Number         0  misc.o ABSOLUTE
    Library\\stm32f4xx_adc.c                 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Library\\stm32f4xx_can.c                 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Library\\stm32f4xx_cec.c                 0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    Library\\stm32f4xx_crc.c                 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    Library\\stm32f4xx_cryp.c                0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    Library\\stm32f4xx_cryp_aes.c            0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    Library\\stm32f4xx_cryp_des.c            0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    Library\\stm32f4xx_cryp_tdes.c           0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    Library\\stm32f4xx_dac.c                 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Library\\stm32f4xx_dbgmcu.c              0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    Library\\stm32f4xx_dcmi.c                0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    Library\\stm32f4xx_dfsdm.c               0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    Library\\stm32f4xx_dma.c                 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\\stm32f4xx_dma2d.c               0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    Library\\stm32f4xx_dsi.c                 0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    Library\\stm32f4xx_exti.c                0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Library\\stm32f4xx_flash.c               0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\\stm32f4xx_flash_ramfunc.c       0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    Library\\stm32f4xx_fmpi2c.c              0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    Library\\stm32f4xx_fsmc.c                0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Library\\stm32f4xx_gpio.c                0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\\stm32f4xx_hash.c                0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    Library\\stm32f4xx_hash_md5.c            0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    Library\\stm32f4xx_hash_sha1.c           0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    Library\\stm32f4xx_i2c.c                 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    Library\\stm32f4xx_iwdg.c                0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Library\\stm32f4xx_lptim.c               0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    Library\\stm32f4xx_ltdc.c                0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    Library\\stm32f4xx_pwr.c                 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Library\\stm32f4xx_qspi.c                0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    Library\\stm32f4xx_rcc.c                 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\\stm32f4xx_rng.c                 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Library\\stm32f4xx_rtc.c                 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Library\\stm32f4xx_sai.c                 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    Library\\stm32f4xx_sdio.c                0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    Library\\stm32f4xx_spdifrx.c             0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    Library\\stm32f4xx_spi.c                 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    Library\\stm32f4xx_syscfg.c              0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Library\\stm32f4xx_tim.c                 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Library\\stm32f4xx_usart.c               0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Library\\stm32f4xx_wwdg.c                0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f4xx_adc.c                  0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    Library\stm32f4xx_can.c                  0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    Library\stm32f4xx_cec.c                  0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    Library\stm32f4xx_crc.c                  0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    Library\stm32f4xx_cryp.c                 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    Library\stm32f4xx_cryp_aes.c             0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    Library\stm32f4xx_cryp_des.c             0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    Library\stm32f4xx_cryp_tdes.c            0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    Library\stm32f4xx_dac.c                  0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    Library\stm32f4xx_dbgmcu.c               0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    Library\stm32f4xx_dcmi.c                 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    Library\stm32f4xx_dfsdm.c                0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    Library\stm32f4xx_dma.c                  0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\stm32f4xx_dma2d.c                0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    Library\stm32f4xx_dsi.c                  0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    Library\stm32f4xx_exti.c                 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    Library\stm32f4xx_flash.c                0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\stm32f4xx_flash_ramfunc.c        0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    Library\stm32f4xx_fmpi2c.c               0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    Library\stm32f4xx_fsmc.c                 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    Library\stm32f4xx_gpio.c                 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\stm32f4xx_hash.c                 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    Library\stm32f4xx_hash_md5.c             0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    Library\stm32f4xx_hash_sha1.c            0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    Library\stm32f4xx_i2c.c                  0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    Library\stm32f4xx_iwdg.c                 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    Library\stm32f4xx_lptim.c                0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    Library\stm32f4xx_ltdc.c                 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    Library\stm32f4xx_pwr.c                  0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    Library\stm32f4xx_qspi.c                 0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    Library\stm32f4xx_rcc.c                  0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\stm32f4xx_rng.c                  0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    Library\stm32f4xx_rtc.c                  0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    Library\stm32f4xx_sai.c                  0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    Library\stm32f4xx_sdio.c                 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    Library\stm32f4xx_spdifrx.c              0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    Library\stm32f4xx_spi.c                  0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    Library\stm32f4xx_syscfg.c               0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    Library\stm32f4xx_tim.c                  0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    Library\stm32f4xx_usart.c                0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Library\stm32f4xx_wwdg.c                 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    Start\startup_stm32f40_41xxx.s           0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    User\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    User\\stm32f4xx_it.c                     0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\\system_stm32f4xx.c                 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f4xx_it.c                      0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\system_stm32f4xx.c                  0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x080001fc   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x08000202   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000206   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000208   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800020c   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800020e   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000210   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000212   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000212   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000218   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000218   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800021c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800021c   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000224   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000226   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000226   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800022a   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000230   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000230   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000270   Section        0  __2sprintf.o(.text)
    .text                                    0x0800029c   Section        0  _printf_pad.o(.text)
    .text                                    0x080002ec   Section        0  _printf_dec.o(.text)
    .text                                    0x08000364   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080004ec   Section      128  strcmpv7m.o(.text)
    .text                                    0x0800056c   Section        0  heapauxi.o(.text)
    .text                                    0x08000572   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000624   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000625   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000654   Section        0  _sputc.o(.text)
    .text                                    0x08000660   Section        8  libspace.o(.text)
    .text                                    0x08000668   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080006b2   Section        0  exit.o(.text)
    .text                                    0x080006c4   Section        0  sys_exit.o(.text)
    .text                                    0x080006d0   Section        2  use_no_semi.o(.text)
    .text                                    0x080006d2   Section        0  indicate_semi.o(.text)
    i.AFE_CalculateStats                     0x080006d2   Section        0  afe_test.o(i.AFE_CalculateStats)
    i.AFE_ClearBuffer                        0x08000724   Section        0  afe_test.o(i.AFE_ClearBuffer)
    i.AFE_DisableInterrupt                   0x08000750   Section        0  afe_test.o(i.AFE_DisableInterrupt)
    i.AFE_EXTI_Init                          0x08000768   Section        0  afe_test.o(i.AFE_EXTI_Init)
    i.AFE_EnableInterrupt                    0x080007b6   Section        0  afe_test.o(i.AFE_EnableInterrupt)
    i.AFE_GPIO_Init                          0x080007cc   Section        0  afe_test.o(i.AFE_GPIO_Init)
    i.AFE_ProcessData                        0x08000824   Section        0  afe_test.o(i.AFE_ProcessData)
    i.AFE_ReadADCData                        0x0800085c   Section        0  afe_test.o(i.AFE_ReadADCData)
    i.AFE_Test_Init                          0x0800086c   Section        0  afe_test.o(i.AFE_Test_Init)
    i.BusFault_Handler                       0x0800087c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000880   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay                                  0x08000882   Section        0  main.o(i.Delay)
    i.Delay_us                               0x0800089e   Section        0  delay.o(i.Delay_us)
    i.Display_ADC_Results                    0x080008cc   Section        0  main.o(i.Display_ADC_Results)
    i.EXTI0_IRQHandler                       0x0800095c   Section        0  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    i.EXTI_ClearITPendingBit                 0x080009a8   Section        0  stm32f4xx_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x080009e8   Section        0  stm32f4xx_exti.o(i.EXTI_GetITStatus)
    i.EXTI_Init                              0x08000aac   Section        0  stm32f4xx_exti.o(i.EXTI_Init)
    i.GPIO_Init                              0x08000bb8   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_WriteBit                          0x08000d60   Section        0  stm32f4xx_gpio.o(i.GPIO_WriteBit)
    i.GetTick                                0x08000e78   Section        0  main.o(i.GetTick)
    i.HardFault_Handler                      0x08000e84   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08000e88   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08000e8c   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000e90   Section        0  misc.o(i.NVIC_Init)
    i.OLED_Clear                             0x08000f50   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x08000f78   Section        0  oled.o(i.OLED_ClearArea)
    i.OLED_GPIO_Init                         0x08001008   Section        0  oled.o(i.OLED_GPIO_Init)
    i.OLED_I2C_SendByte                      0x0800105c   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x080010b0   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x080010d8   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x080010fa   Section        0  oled.o(i.OLED_Init)
    i.OLED_SetCursor                         0x08001194   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x080011bc   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x08001210   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x0800130c   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x0800141c   Section        0  oled.o(i.OLED_Update)
    i.OLED_W_SCL                             0x08001450   Section        0  oled.o(i.OLED_W_SCL)
    i.OLED_W_SDA                             0x08001464   Section        0  oled.o(i.OLED_W_SDA)
    i.OLED_WriteCommand                      0x08001478   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08001498   Section        0  oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x080014c6   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x080014c8   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001530   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.SVC_Handler                            0x08001598   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SYSCFG_EXTILineConfig                  0x0800159c   Section        0  stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig)
    i.SetSysClock                            0x08001678   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x08001679   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_Handler                        0x08001764   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x0800177c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TimingDelay_Decrement                  0x080017e4   Section        0  main.o(i.TimingDelay_Decrement)
    i.UsageFault_Handler                     0x080017e6   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i._is_digit                              0x080017ea   Section        0  __printf_wp.o(i._is_digit)
    i.assert_failed                          0x080017f8   Section        0  main.o(i.assert_failed)
    i.main                                   0x080017fc   Section        0  main.o(i.main)
    x$fpl$fpinit                             0x0800197c   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800197c   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x08001986   Section     3067  oled_data.o(.constdata)
    .constdata                               0x08002581   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08002581   Data          17  __printf_flags_ss_wp.o(.constdata)
    .data                                    0x20000000   Section        8  main.o(.data)
    last_update                              0x20000004   Data           4  main.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x2000001c   Section        4  afe_test.o(.data)
    .bss                                     0x20000020   Section       48  main.o(.bss)
    .bss                                     0x20000050   Section      512  afe_test.o(.bss)
    .bss                                     0x20000250   Section     1024  oled.o(.bss)
    .bss                                     0x20000650   Section       96  libspace.o(.bss)
    HEAP                                     0x200006b0   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200006b0   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x200008b0   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x200008b0   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20000cb0   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x080001fd   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x08000203   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000207   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000209   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x0800020f   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000213   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000213   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000219   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000219   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800021d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800021d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000225   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000227   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000227   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800022b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000231   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART1_IRQHandler                        0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x0800024b   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x0800024d   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __2sprintf                               0x08000271   Thumb Code    38  __2sprintf.o(.text)
    _printf_pre_padding                      0x0800029d   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080002c9   Thumb Code    34  _printf_pad.o(.text)
    _printf_int_dec                          0x080002ed   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x08000365   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strcmp                                   0x080004ed   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x0800056d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800056f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000571   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x08000573   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x0800062f   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000655   Thumb Code    10  _sputc.o(.text)
    __user_libspace                          0x08000661   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000661   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000661   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000669   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080006b3   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x080006c5   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080006d1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080006d1   Thumb Code     2  use_no_semi.o(.text)
    AFE_CalculateStats                       0x080006d3   Thumb Code    80  afe_test.o(i.AFE_CalculateStats)
    __semihosting_library_function           0x080006d3   Thumb Code     0  indicate_semi.o(.text)
    AFE_ClearBuffer                          0x08000725   Thumb Code    32  afe_test.o(i.AFE_ClearBuffer)
    AFE_DisableInterrupt                     0x08000751   Thumb Code    18  afe_test.o(i.AFE_DisableInterrupt)
    AFE_EXTI_Init                            0x08000769   Thumb Code    78  afe_test.o(i.AFE_EXTI_Init)
    AFE_EnableInterrupt                      0x080007b7   Thumb Code    22  afe_test.o(i.AFE_EnableInterrupt)
    AFE_GPIO_Init                            0x080007cd   Thumb Code    78  afe_test.o(i.AFE_GPIO_Init)
    AFE_ProcessData                          0x08000825   Thumb Code    44  afe_test.o(i.AFE_ProcessData)
    AFE_ReadADCData                          0x0800085d   Thumb Code    10  afe_test.o(i.AFE_ReadADCData)
    AFE_Test_Init                            0x0800086d   Thumb Code    16  afe_test.o(i.AFE_Test_Init)
    BusFault_Handler                         0x0800087d   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08000881   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay                                    0x08000883   Thumb Code    28  main.o(i.Delay)
    Delay_us                                 0x0800089f   Thumb Code    44  delay.o(i.Delay_us)
    Display_ADC_Results                      0x080008cd   Thumb Code    88  main.o(i.Display_ADC_Results)
    EXTI0_IRQHandler                         0x0800095d   Thumb Code    64  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    EXTI_ClearITPendingBit                   0x080009a9   Thumb Code    30  stm32f4xx_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x080009e9   Thumb Code   164  stm32f4xx_exti.o(i.EXTI_GetITStatus)
    EXTI_Init                                0x08000aad   Thumb Code   236  stm32f4xx_exti.o(i.EXTI_Init)
    GPIO_Init                                0x08000bb9   Thumb Code   352  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_WriteBit                            0x08000d61   Thumb Code   206  stm32f4xx_gpio.o(i.GPIO_WriteBit)
    GetTick                                  0x08000e79   Thumb Code     6  main.o(i.GetTick)
    HardFault_Handler                        0x08000e85   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08000e89   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08000e8d   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000e91   Thumb Code   164  misc.o(i.NVIC_Init)
    OLED_Clear                               0x08000f51   Thumb Code    36  oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x08000f79   Thumb Code   140  oled.o(i.OLED_ClearArea)
    OLED_GPIO_Init                           0x08001009   Thumb Code    80  oled.o(i.OLED_GPIO_Init)
    OLED_I2C_SendByte                        0x0800105d   Thumb Code    84  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x080010b1   Thumb Code    40  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x080010d9   Thumb Code    34  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x080010fb   Thumb Code   154  oled.o(i.OLED_Init)
    OLED_SetCursor                           0x08001195   Thumb Code    38  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x080011bd   Thumb Code    74  oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x08001211   Thumb Code   248  oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x0800130d   Thumb Code   262  oled.o(i.OLED_ShowString)
    OLED_Update                              0x0800141d   Thumb Code    48  oled.o(i.OLED_Update)
    OLED_W_SCL                               0x08001451   Thumb Code    16  oled.o(i.OLED_W_SCL)
    OLED_W_SDA                               0x08001465   Thumb Code    16  oled.o(i.OLED_W_SDA)
    OLED_WriteCommand                        0x08001479   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08001499   Thumb Code    46  oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x080014c7   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x080014c9   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001531   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    SVC_Handler                              0x08001599   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SYSCFG_EXTILineConfig                    0x0800159d   Thumb Code   188  stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig)
    SysTick_Handler                          0x08001765   Thumb Code    18  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x0800177d   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    TimingDelay_Decrement                    0x080017e5   Thumb Code     2  main.o(i.TimingDelay_Decrement)
    UsageFault_Handler                       0x080017e7   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    _is_digit                                0x080017eb   Thumb Code    14  __printf_wp.o(i._is_digit)
    assert_failed                            0x080017f9   Thumb Code     4  main.o(i.assert_failed)
    main                                     0x080017fd   Thumb Code   280  main.o(i.main)
    _fp_init                                 0x0800197d   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08001985   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08001985   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    OLED_F8x16                               0x08001986   Data        1520  oled_data.o(.constdata)
    OLED_F6x8                                0x08001f76   Data         570  oled_data.o(.constdata)
    OLED_CF16x16                             0x080021b0   Data         945  oled_data.o(.constdata)
    Diode                                    0x08002561   Data          32  oled_data.o(.constdata)
    Region$$Table$$Base                      0x08002594   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080025b4   Number         0  anon$$obj.o(Region$$Table)
    uwTick                                   0x20000000   Data           4  main.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f4xx.o(.data)
    g_data_ready_flag                        0x2000001c   Data           1  afe_test.o(.data)
    g_buffer_index                           0x2000001e   Data           2  afe_test.o(.data)
    adc_stats                                0x20000020   Data          16  main.o(.bss)
    display_buffer                           0x20000030   Data          32  main.o(.bss)
    g_adc_buffer                             0x20000050   Data         512  afe_test.o(.bss)
    OLED_DisplayBuf                          0x20000250   Data        1024  oled.o(.bss)
    __libspace_start                         0x20000650   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200006b0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000025d4, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000025b4, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5880  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         6275    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         6277    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         6279    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         5875    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         5874    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000202   0x08000202   0x00000004   Code   RO         5991    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000206   0x08000206   0x00000002   Code   RO         6143    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000208   0x08000208   0x00000004   Code   RO         6151    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6154    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6157    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6159    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6161    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6164    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6166    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6168    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6170    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6172    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6174    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6176    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6178    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6180    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6182    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6184    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6188    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6190    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6192    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         6194    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000002   Code   RO         6195    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000002   Code   RO         6215    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000210   0x08000210   0x00000000   Code   RO         6228    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         6230    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         6233    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         6236    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         6238    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         6241    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000002   Code   RO         6242    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         5932    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000212   0x08000212   0x00000000   Code   RO         6056    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000006   Code   RO         6068    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000218   0x08000218   0x00000000   Code   RO         6058    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000218   0x08000218   0x00000004   Code   RO         6059    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800021c   0x0800021c   0x00000000   Code   RO         6061    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800021c   0x0800021c   0x00000008   Code   RO         6062    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000224   0x08000224   0x00000002   Code   RO         6146    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000226   0x08000226   0x00000000   Code   RO         6197    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000226   0x08000226   0x00000004   Code   RO         6198    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         6199    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000230   0x08000230   0x00000040   Code   RO            4    .text               startup_stm32f40_41xxx.o
    0x08000270   0x08000270   0x0000002c   Code   RO         5844    .text               c_w.l(__2sprintf.o)
    0x0800029c   0x0800029c   0x0000004e   Code   RO         5850    .text               c_w.l(_printf_pad.o)
    0x080002ea   0x080002ea   0x00000002   PAD
    0x080002ec   0x080002ec   0x00000078   Code   RO         5852    .text               c_w.l(_printf_dec.o)
    0x08000364   0x08000364   0x00000188   Code   RO         5871    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080004ec   0x080004ec   0x00000080   Code   RO         5876    .text               c_w.l(strcmpv7m.o)
    0x0800056c   0x0800056c   0x00000006   Code   RO         5878    .text               c_w.l(heapauxi.o)
    0x08000572   0x08000572   0x000000b2   Code   RO         5939    .text               c_w.l(_printf_intcommon.o)
    0x08000624   0x08000624   0x00000030   Code   RO         5943    .text               c_w.l(_printf_char_common.o)
    0x08000654   0x08000654   0x0000000a   Code   RO         5945    .text               c_w.l(_sputc.o)
    0x0800065e   0x0800065e   0x00000002   PAD
    0x08000660   0x08000660   0x00000008   Code   RO         6094    .text               c_w.l(libspace.o)
    0x08000668   0x08000668   0x0000004a   Code   RO         6097    .text               c_w.l(sys_stackheap_outer.o)
    0x080006b2   0x080006b2   0x00000012   Code   RO         6136    .text               c_w.l(exit.o)
    0x080006c4   0x080006c4   0x0000000c   Code   RO         6207    .text               c_w.l(sys_exit.o)
    0x080006d0   0x080006d0   0x00000002   Code   RO         6218    .text               c_w.l(use_no_semi.o)
    0x080006d2   0x080006d2   0x00000000   Code   RO         6220    .text               c_w.l(indicate_semi.o)
    0x080006d2   0x080006d2   0x00000050   Code   RO         5442    i.AFE_CalculateStats  afe_test.o
    0x08000722   0x08000722   0x00000002   PAD
    0x08000724   0x08000724   0x0000002c   Code   RO         5443    i.AFE_ClearBuffer   afe_test.o
    0x08000750   0x08000750   0x00000018   Code   RO         5444    i.AFE_DisableInterrupt  afe_test.o
    0x08000768   0x08000768   0x0000004e   Code   RO         5445    i.AFE_EXTI_Init     afe_test.o
    0x080007b6   0x080007b6   0x00000016   Code   RO         5446    i.AFE_EnableInterrupt  afe_test.o
    0x080007cc   0x080007cc   0x00000058   Code   RO         5447    i.AFE_GPIO_Init     afe_test.o
    0x08000824   0x08000824   0x00000038   Code   RO         5448    i.AFE_ProcessData   afe_test.o
    0x0800085c   0x0800085c   0x00000010   Code   RO         5449    i.AFE_ReadADCData   afe_test.o
    0x0800086c   0x0800086c   0x00000010   Code   RO         5450    i.AFE_Test_Init     afe_test.o
    0x0800087c   0x0800087c   0x00000004   Code   RO          203    i.BusFault_Handler  stm32f4xx_it.o
    0x08000880   0x08000880   0x00000002   Code   RO          204    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000882   0x08000882   0x0000001c   Code   RO           13    i.Delay             main.o
    0x0800089e   0x0800089e   0x0000002c   Code   RO         5530    i.Delay_us          delay.o
    0x080008ca   0x080008ca   0x00000002   PAD
    0x080008cc   0x080008cc   0x00000090   Code   RO           14    i.Display_ADC_Results  main.o
    0x0800095c   0x0800095c   0x0000004c   Code   RO          205    i.EXTI0_IRQHandler  stm32f4xx_it.o
    0x080009a8   0x080009a8   0x00000040   Code   RO         1751    i.EXTI_ClearITPendingBit  stm32f4xx_exti.o
    0x080009e8   0x080009e8   0x000000c4   Code   RO         1755    i.EXTI_GetITStatus  stm32f4xx_exti.o
    0x08000aac   0x08000aac   0x0000010c   Code   RO         1756    i.EXTI_Init         stm32f4xx_exti.o
    0x08000bb8   0x08000bb8   0x000001a8   Code   RO         2273    i.GPIO_Init         stm32f4xx_gpio.o
    0x08000d60   0x08000d60   0x00000118   Code   RO         2285    i.GPIO_WriteBit     stm32f4xx_gpio.o
    0x08000e78   0x08000e78   0x0000000c   Code   RO           15    i.GetTick           main.o
    0x08000e84   0x08000e84   0x00000004   Code   RO          206    i.HardFault_Handler  stm32f4xx_it.o
    0x08000e88   0x08000e88   0x00000004   Code   RO          207    i.MemManage_Handler  stm32f4xx_it.o
    0x08000e8c   0x08000e8c   0x00000002   Code   RO          208    i.NMI_Handler       stm32f4xx_it.o
    0x08000e8e   0x08000e8e   0x00000002   PAD
    0x08000e90   0x08000e90   0x000000c0   Code   RO          333    i.NVIC_Init         misc.o
    0x08000f50   0x08000f50   0x00000028   Code   RO         5564    i.OLED_Clear        oled.o
    0x08000f78   0x08000f78   0x00000090   Code   RO         5565    i.OLED_ClearArea    oled.o
    0x08001008   0x08001008   0x00000054   Code   RO         5573    i.OLED_GPIO_Init    oled.o
    0x0800105c   0x0800105c   0x00000054   Code   RO         5575    i.OLED_I2C_SendByte  oled.o
    0x080010b0   0x080010b0   0x00000028   Code   RO         5576    i.OLED_I2C_Start    oled.o
    0x080010d8   0x080010d8   0x00000022   Code   RO         5577    i.OLED_I2C_Stop     oled.o
    0x080010fa   0x080010fa   0x0000009a   Code   RO         5578    i.OLED_Init         oled.o
    0x08001194   0x08001194   0x00000026   Code   RO         5585    i.OLED_SetCursor    oled.o
    0x080011ba   0x080011ba   0x00000002   PAD
    0x080011bc   0x080011bc   0x00000054   Code   RO         5587    i.OLED_ShowChar     oled.o
    0x08001210   0x08001210   0x000000fc   Code   RO         5590    i.OLED_ShowImage    oled.o
    0x0800130c   0x0800130c   0x00000110   Code   RO         5593    i.OLED_ShowString   oled.o
    0x0800141c   0x0800141c   0x00000034   Code   RO         5594    i.OLED_Update       oled.o
    0x08001450   0x08001450   0x00000014   Code   RO         5596    i.OLED_W_SCL        oled.o
    0x08001464   0x08001464   0x00000014   Code   RO         5597    i.OLED_W_SDA        oled.o
    0x08001478   0x08001478   0x00000020   Code   RO         5598    i.OLED_WriteCommand  oled.o
    0x08001498   0x08001498   0x0000002e   Code   RO         5599    i.OLED_WriteData    oled.o
    0x080014c6   0x080014c6   0x00000002   Code   RO          209    i.PendSV_Handler    stm32f4xx_it.o
    0x080014c8   0x080014c8   0x00000068   Code   RO         3214    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08001530   0x08001530   0x00000068   Code   RO         3226    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x08001598   0x08001598   0x00000002   Code   RO          210    i.SVC_Handler       stm32f4xx_it.o
    0x0800159a   0x0800159a   0x00000002   PAD
    0x0800159c   0x0800159c   0x000000dc   Code   RO         4549    i.SYSCFG_EXTILineConfig  stm32f4xx_syscfg.o
    0x08001678   0x08001678   0x000000ec   Code   RO          292    i.SetSysClock       system_stm32f4xx.o
    0x08001764   0x08001764   0x00000018   Code   RO          211    i.SysTick_Handler   stm32f4xx_it.o
    0x0800177c   0x0800177c   0x00000068   Code   RO          294    i.SystemInit        system_stm32f4xx.o
    0x080017e4   0x080017e4   0x00000002   Code   RO           17    i.TimingDelay_Decrement  main.o
    0x080017e6   0x080017e6   0x00000004   Code   RO          212    i.UsageFault_Handler  stm32f4xx_it.o
    0x080017ea   0x080017ea   0x0000000e   Code   RO         5864    i._is_digit         c_w.l(__printf_wp.o)
    0x080017f8   0x080017f8   0x00000004   Code   RO           18    i.assert_failed     main.o
    0x080017fc   0x080017fc   0x00000180   Code   RO           19    i.main              main.o
    0x0800197c   0x0800197c   0x0000000a   Code   RO         6205    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08001986   0x08001986   0x00000bfb   Data   RO         5828    .constdata          oled_data.o
    0x08002581   0x08002581   0x00000011   Data   RO         5872    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08002592   0x08002592   0x00000002   PAD
    0x08002594   0x08002594   0x00000020   Data   RO         6273    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x080025d4, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080025b4, Size: 0x00000cb0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080025b4   0x00000008   Data   RW           21    .data               main.o
    0x20000008   0x080025bc   0x00000014   Data   RW          295    .data               system_stm32f4xx.o
    0x2000001c   0x080025d0   0x00000004   Data   RW         5452    .data               afe_test.o
    0x20000020        -       0x00000030   Zero   RW           20    .bss                main.o
    0x20000050        -       0x00000200   Zero   RW         5451    .bss                afe_test.o
    0x20000250        -       0x00000400   Zero   RW         5601    .bss                oled.o
    0x20000650        -       0x00000060   Zero   RW         6095    .bss                c_w.l(libspace.o)
    0x200006b0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f40_41xxx.o
    0x200008b0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       424         46          0          4        512      32467   afe_test.o
        44          0          0          0          0        463   delay.o
       574        166          0          8         48     279092   main.o
       192         28          0          0          0     241588   misc.o
      1396         48          0          0       1024      10027   oled.o
         0          0       3067          0          0        970   oled_data.o
        64         26        392          0       1536        836   startup_stm32f40_41xxx.o
         0          0          0          0          0       1600   stm32f4xx_adc.o
       528         98          0          0          0       4520   stm32f4xx_exti.o
       704        146          0          0          0       8852   stm32f4xx_gpio.o
       124         18          0          0          0       4381   stm32f4xx_it.o
       208         68          0          0          0       1218   stm32f4xx_rcc.o
       220         32          0          0          0        736   stm32f4xx_syscfg.o
       340         32          0         20          0       1641   system_stm32f4xx.o

    ----------------------------------------------------------------------
      4828        <USER>       <GROUP>         32       3120     588391   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0         68   _sputc.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
      1314         <USER>         <GROUP>          0         96       1464   Library Totals
         6          0          2          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1298         48         17          0         96       1348   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
      1314         <USER>         <GROUP>          0         96       1464   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      6142        756       3510         32       3216     584531   Grand Totals
      6142        756       3510         32       3216     584531   ELF Image Totals
      6142        756       3510         32          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 9652 (   9.43kB)
    Total RW  Size (RW Data + ZI Data)              3248 (   3.17kB)
    Total ROM Size (Code + RO Data + RW Data)       9684 (   9.46kB)

==============================================================================

