#ifndef __SENSOR_FUSION_H
#define __SENSOR_FUSION_H

#include "stm32f10x.h"
#include "ir_sensor.h"

// IMU输出模式
typedef enum {
    IMU_MODE_EULER = 0,        // 欧拉角输出
    IMU_MODE_QUATERNION,       // 四元数输出
    IMU_MODE_EARTH_ACC,        // 世界坐标系加速度
    IMU_MODE_RAW_ACC,          // 原始加速度
    IMU_MODE_RAW_GYRO,         // 原始陀螺仪
    IMU_MODE_RAW_MAG,          // 原始磁力计
    IMU_MODE_ALL_DATA          // 所有数据
} IMU_Output_Mode_t;

// IMU数据结构
typedef struct {
    float acc_x;        // 加速度X轴
    float acc_y;        // 加速度Y轴
    float acc_z;        // 加速度Z轴
    float gyro_x;       // 陀螺仪X轴
    float gyro_y;       // 陀螺仪Y轴
    float gyro_z;       // 陀螺仪Z轴
    float mag_x;        // 磁力计X轴
    float mag_y;        // 磁力计Y轴
    float mag_z;        // 磁力计Z轴
    float roll;         // 横滚角
    float pitch;        // 俯仰角
    float yaw;          // 偏航角
    uint8_t valid;      // 数据有效标志
} IMU_Data_t;

// 融合数据结构
typedef struct {
    float joint_angle;      // 关节角度
    TrainingMode_t mode;    // 训练模式
    float target_angle;     // 目标角度
    float angle_threshold;  // 角度阈值
    uint16_t training_cycles; // 训练周期数
    uint32_t training_time;   // 训练时间(秒)
} Fusion_Data_t;

// 全局数据
extern IMU_Data_t imu_data;
extern Fusion_Data_t fusion_data;

// 函数声明
void Sensor_Fusion_Init(void);
void Update_Fusion_Data(IR_Sensor_t *ir_data, IMU_Data_t *imu_data, Fusion_Data_t *fusion_data);
float Calculate_Joint_Angle_From_IMU(IMU_Data_t *imu_data);
float Calculate_Joint_Angle(uint16_t current_distance, uint16_t initial_distance);
void Parse_IMU_Data(const char* data_string, IMU_Data_t *imu_data);
float Fusion_Algorithm(float ir_angle, float imu_angle);

#endif /* __SENSOR_FUSION_H */ 
