#include "stm32f10x.h"
#include "delay.h"
// #include "usart.h" // 不需要串口，使用I2C模式
// #include "string.h" // 不需要字符串操作
#include "ir_sensor.h"
#include "IIC.h"
#include "math.h"

// 红外传感器默认I2C地址（根据您的传感器修改）
#define IR_SENSOR_ADDR 0xE0  // 0x70左移一位

// 全局变量
static uint8_t ranging_mode = 0x01; // 单次测量模式

// 传感器数据结构 - 仅内部使用
typedef struct
{
  uint16_t distance;  // 距离值
  uint8_t mode;       // 传感器模式
  uint8_t temp;       // 温度值
} sensor_t;

// 卡尔曼滤波器实例
// 参数优化: 过程噪声q=0.01, 测量噪声r=0.1, 初始协方差p=0.1
static KalmanFilter distance_filter = {0, 1, 1, 0.01, 0.1, 0.1, 0};

// 卡尔曼滤波器初始化
void KalmanFilter_Init(KalmanFilter* filter, float q, float r, float p, float initial_value)
{
    filter->x = initial_value;  // 初始状态估计
    filter->A = 1;              // 状态转移系数（恒等模型）
    filter->H = 1;              // 观测系数（恒等模型）
    filter->q = q;              // 过程噪声方差 - 较小值表示系统状态变化平稳
    filter->r = r;              // 测量噪声方差 - 中等值表示对测量有一定信任
    filter->p = p;              // 初始估计误差协方差 - 适中值避免初期波动
}

// 卡尔曼滤波器更新
float KalmanFilter_Update(KalmanFilter* filter, float measurement)
{
    // 时间更新（预测）
    filter->x = filter->A * filter->x;   // 预测状态
    filter->p = filter->A * filter->A * filter->p + filter->q;  // 预测误差协方差
    
    // 测量更新（校正）
    filter->gain = filter->p * filter->H / (filter->p * filter->H * filter->H + filter->r);  // 计算卡尔曼增益
    filter->x = filter->x + filter->gain * (measurement - filter->H * filter->x);  // 更新状态估计
    filter->p = (1 - filter->gain * filter->H) * filter->p;  // 更新误差协方差
    
    return filter->x;  // 返回滤波后的估计值
}

// 外部I2C函数声明
extern u8 I2C_Start(void);
extern void I2C_Stop(void);
extern void I2C_SendByte(u8 dat);
extern u8 I2C_WaitAck(void);
extern u8 I2C_RecvByte(void);
extern void I2C_SendACK(u8 i);
extern void IIC_Init(void);  // 使用正确的函数名

// 触发一次测距 - 传感器特定函数
u8 takeRangeReading(u8 Slave_Address)
{
    if(I2C_Start()==0)  // 起始信号
    {
        I2C_Stop(); 
        return 0;
    }        

    I2C_SendByte(Slave_Address);   // 发送设备地址+写信号
    if(!I2C_WaitAck())
    {
        I2C_Stop(); 
        return 0;
    }
    
    I2C_SendByte(0x51);    // 触发测距命令
    if(!I2C_WaitAck())
    {
        I2C_Stop(); 
        return 0;
    }
    
    I2C_Stop();   	
    return 1;
}

// 读取测距结果 - 传感器特定函数
u8 requestRange(u8 Slave_Address, uint16_t *distance)
{
    u8 REG_data[2]={0,0};
    
    if(I2C_Start()==0)  // 起始信号
    {
        I2C_Stop(); 
        return 0;
    }         
     
    I2C_SendByte(Slave_Address);    // 发送设备地址+读信号
    if(!I2C_WaitAck())
    {
        I2C_Stop(); 
        return 0;
    } 
    
    REG_data[0]=I2C_RecvByte();     // 接收高字节
    I2C_SendACK(0);                // 应答
    
    REG_data[1]=I2C_RecvByte();     // 接收低字节
    I2C_SendACK(1);                // 非应答，停止传输
    
    I2C_Stop();                    // 停止信号
    *distance = (REG_data[0]<<8) | REG_data[1];
    return 1;
}

// 初始化红外传感器I2C模式和滤波器
void VL53L0X_Init(void)
{
    // I2C初始化
    IIC_Init(); // 使用正确的函数名初始化I2C总线
    
    // 卡尔曼滤波器初始化
    KalmanFilter_Init(&distance_filter, 0.01, 0.1, 0.1, 50); // 50mm作为初始值
    
    // 延时等待模块初始化稳定
    delay_ms(100);
}

// 触发一次距离测量
uint8_t VL53L0X_TriggerMeasurement(void)
{
    // 根据示例代码，发送测量命令0x51
    return takeRangeReading(IR_SENSOR_ADDR);
}

// 读取测量结果
uint8_t VL53L0X_ReadDistance(uint16_t *distance)
{
    // 根据示例代码，读取测量结果
    return requestRange((IR_SENSOR_ADDR+1), distance);
}

// 处理传感器数据 - 主程序中调用此函数获取数据
uint8_t Process_VL53L0X_Data(IR_Sensor_t *sensor_data)
{
    uint16_t distance_mm = 0;
    uint16_t filtered_distance = 0;
    uint8_t retry_count = 0;
    
    // 最多尝试3次触发测量
    while(retry_count < 3)
    {
        // 触发测量
        if(VL53L0X_TriggerMeasurement()) 
        {
            // 测量触发成功，延时等待测量完成
            // 根据GP2Y0A02YK0F传感器规格调整延时
            delay_ms(60); // 增加延时以确保测量完成
            
            // 读取距离数据
            if(VL53L0X_ReadDistance(&distance_mm)) 
            {
                // 扩大测量范围，检查测量值是否在有效范围内(20mm-800mm)
                if(distance_mm >= 20 && distance_mm <= 800) 
                {
                    // 对测量距离进行卡尔曼滤波
                    filtered_distance = (uint16_t)KalmanFilter_Update(&distance_filter, (float)distance_mm);
                    
                    // 更新传感器数据结构
                    sensor_data->distance = filtered_distance;
                    sensor_data->mode = ranging_mode;
                    sensor_data->temp = 0; // 假设温度信息不直接从此函数获取
                    
                    return 1; // 成功读取有效数据
                }
                // 测量值无效，重试
            }
        }
        
        retry_count++;        // 增加重试计数
        delay_ms(20);         // 短暂延时后再次尝试
    }
    
    // 多次尝试后仍然失败
    return 0;
}

// 将测量的距离转换为关节角度
float ConvertDistanceToAngle(uint16_t distance)
{
    float angle = 0;
    static uint16_t calibrated_init_distance = 0;
    extern uint16_t initial_distance; // 从system_defs.h中声明的全局变量
    
    // 第一次使用时，如果校准值有效则保存
    if (calibrated_init_distance == 0 && initial_distance > 0)
    {
        calibrated_init_distance = initial_distance;
    }
    
    // 扩大有效测量范围，确保能覆盖到0-90度
    if(distance < 20)
    {
        distance = 20;  // 最小测量距离为20mm
    }
    else if(distance > 800)
    {
        distance = 800; // 最大测量距离为800mm
    }
    
    // 使用校准后的距离计算角度
    if (calibrated_init_distance > 0)
    {
        // 根据校准距离和当前距离的比率计算角度
        float ratio = (float)distance / (float)calibrated_init_distance;
        
        if (ratio >= 1.0f) // 距离大于等于初始距离，角度为0
        {
            angle = 0.0f;
        }
        else
        {
            // 使用反余弦函数计算关节角度
            angle = acosf(ratio) * (180.0f / 3.14159f); // 转换为角度
            
            // 扩大映射范围，确保能够映射到0-90度
            // 通过线性映射调整计算出的角度
            angle = angle * 90.0f / 70.0f; // 假设传感器原始输出范围约为0-70度，映射到0-90度
            
            // 限制角度范围
            if (angle > 90.0f)
            {
                angle = 90.0f;
            }
        }
    }
    else
    {
        // 如果没有校准值，使用改进的分段线性映射方法，覆盖0-90度
        if(distance <= 150) // 小角度区域
        {
            // 距离20mm-150mm对应角度0-30°
            angle = (float)(distance - 20) * 30.0f / 130.0f;
        }
        else if(distance <= 450) // 中角度区域
        {
            // 距离150mm-450mm对应角度30°-60°
            angle = 30.0f + (float)(distance - 150) * 30.0f / 300.0f;
        }
        else // 大角度区域
        {
            // 距离450mm-800mm对应角度60°-90°
            angle = 60.0f + (float)(distance - 450) * 30.0f / 350.0f;
        }
    }
    
    return angle;
}

// 根据关节角度确定训练模式
TrainingMode_t DetermineTrainingMode(float joint_angle)
{
    // 基于修改后的角度计算（0-90度范围）调整训练模式阈值
    if(joint_angle < 30) 
    {
        return MODE_LOW_FORCE;     // 低阻力模式 (0°-30°)
    }
    else if(joint_angle < 60) 
    {
        return MODE_MEDIUM_FORCE;  // 中等阻力模式 (30°-60°)
    }
    else
    {
        return MODE_HIGH_FORCE;    // 高阻力模式 (60°-90°)
    }
}

