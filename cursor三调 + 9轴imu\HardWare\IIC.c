#include "IIC.h"
#include "delay.h"
 
void IIC_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	/* 使能与 I2C相关的时钟 */
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE );  

	/* PB6-I2C_SCL, PB7-I2C_SDA */
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6| GPIO_Pin_7; 
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_10MHz; 
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_OD; 
	GPIO_Init(GPIOB, &GPIO_InitStructure); 
}

// 微秒延时函数，提高IIC通信可靠性
void delay_1us(u8 x)
{
    u8 i=20; // 增加延时参数，提高可靠性
    x=i*x;
    while(x--);
}

////////IIC初始化//////////
/*
IIC初始:SCL在上升沿时，SDA从高电平到低电平产生一个下降沿，然后SCL保持高电平，
*/
u8 I2C_Start(void)
{
		SDA_H; 
		delay_1us(5);	//延时确保时钟频率大于100K，防止误判
		SCL_H;
		delay_1us(5);//延时确保时钟频率大于100K，防止误判
		if(!SDA_read) return 0;//SDA为低电平，表示从机应答，退出
		SDA_L;   //SCL在上升沿时，SDA从高电平到低电平
		delay_1us(5);
	  if(SDA_read) return 0;//SDA为高电平，表示主机应答，退出
		SCL_L;
	  delay_1us(5);
	  return 1;
}
//**************************************
//IIC停止信号
/*
IIC停止:SCL在上升沿时，SDA从低电平到高电平产生一个上升沿，然后SCL保持高电平，
*/
//**************************************
void I2C_Stop(void)
{
    SDA_L;
		SCL_L;
		delay_1us(5);
		SCL_H;
		delay_1us(5);
		SDA_H;//SCL在上升沿时，SDA从低电平到高电平产生一个上升沿，然后SCL保持高电平
}
//**************************************
//IIC应答信号
//内部:ack (0:ACK 1:NAK)
/*
应答:主机发送数据后，等待从机应答，从机应答信号为低电平
标准:SDA保持高电平，SCL高电平时，主机释放SDA
*/
//**************************************
void I2C_SendACK(u8 i)
{
    if(1==i)
			SDA_H;	             //标准SDA保持高电平，从机应答
    else 
			SDA_L;  						//标准SDA保持高电平，从机不应答 	
	  SCL_H;                    //时钟上升沿，主机释放SDA
    delay_1us(5);                 //延时
    SCL_L ;                  //时钟下降沿，从机应答
    delay_1us(5);    
} 
///////等待从机应答////////
/*
等待:主机发送一个字节数据后，等待从机应答，从机应答信号为低电平
从机应答信号为低电平时，主机释放SDA，从机拉低SDA
*/
/////////////////
u8 I2C_WaitAck(void) 	 //返回值为:=1 ACK,=0 NAK
{
	uint16_t i=0;
	SDA_H;	        //主机释放SDA
	SCL_H;         //SCL高电平时，主机释放SDA
	while(SDA_read)//等待SDA低电平
	{
		i++;      //等待时间循环
		if(i==500)//延时时间循环
		break;
	}
	if(SDA_read)//再次判断SDA是否低电平
	{
		SCL_L; 
		return RESET;//从机应答失败，返回0
	}
  delay_1us(5);//延时确保时钟频率大于100K
	SCL_L;
	delay_1us(5); //延时确保时钟频率大于100K
	return SET;//从机应答成功，返回1
}
//**************************************
//读取IIC总线一个字节
/*
一个字节8bit,SCL低电平时，标准SDA为SCL高电平时，从机输出SDA
*/
//**************************************
void I2C_SendByte(u8 dat)
{
  u8 i;
	SCL_L;//SCL低电平时，SDA保持高电平
  for (i=0; i<8; i++)         //8位循环
  {
		if(dat&0x80)//SDA高电平时
		SDA_H;  
		else 
		SDA_L;
    SCL_H;                //时钟上升沿，从机输出SDA
    delay_1us(5);        //延时确保IIC时钟频率，也是从机输出数据的时间
    SCL_L;                //时钟下降沿，SDA保持高电平
    delay_1us(5); 		  //延时确保IIC时钟频率，也是从机输出数据的时间
		dat <<= 1;          //移除数据最高位  
  }					 
}
//**************************************
//读取IIC总线一个字节
//**************************************
u8 I2C_RecvByte()
{
    u8 i;
    u8 dat = 0;
    SDA_H;//主机释放SDA，从机拉低SDA
    delay_1us(1);         //延时从机保持SDA高电平时            
    for (i=0; i<8; i++)         //8位循环
    { 
		  dat <<= 1;
			
      SCL_H;                //时钟上升沿，从机输出SDA
     
		  if(SDA_read) //判断    
		   dat |=0x01;      
       delay_1us(5);     //延时确保IIC时钟频率		
       SCL_L;           //时钟下降沿，主机接收数据
       delay_1us(5);   //延时从机保持SDA高电平时
    } 
    return dat;
}
//**************************************
//写入IIC设备一个字节
//**************************************
u8 Single_WriteI2C_byte(u8 Slave_Address,u8 REG_Address,u8 data)
{
	  if(I2C_Start()==0)  //开始信号
		{I2C_Stop(); return RESET;}           

    I2C_SendByte(Slave_Address);   //设备地址+写信号
 	  if(!I2C_WaitAck()){I2C_Stop(); return RESET;}
   
		I2C_SendByte(REG_Address);    //内部寄存器地址
 	  if(!I2C_WaitAck()){I2C_Stop(); return RESET;}
   
		I2C_SendByte(data);       //内部寄存器数据
	  if(!I2C_WaitAck()){I2C_Stop(); return RESET;}
		
		I2C_Stop();   //停止信号
		
		return SET;
}
//**************************************
//读取IIC设备一个字节
//**************************************
u8 Single_ReadI2C(u8 Slave_Address,u8 REG_Address,u8 *REG_data,u8 length)
{
 if(I2C_Start()==0)  //开始信号
		{I2C_Stop(); return RESET;}          
	 
	I2C_SendByte(Slave_Address);    //设备地址+写信号
 	if(!I2C_WaitAck()){I2C_Stop(); return RESET;} 
	
	I2C_SendByte(REG_Address);     //读取存储单元地址
 	if(!I2C_WaitAck()){I2C_Stop(); return RESET;} 
	
	if(I2C_Start()==0)  //开始信号
			{I2C_Stop(); return RESET;}            

	I2C_SendByte(Slave_Address+1);  //设备地址+读信号
 	if(!I2C_WaitAck()){I2C_Stop(); return RESET;}
	
	while(length-1)
	{
		*REG_data++=I2C_RecvByte();       //内部读取数据
		I2C_SendACK(0);               //应答
		length--;
	}
	*REG_data=I2C_RecvByte();  
	I2C_SendACK(1);     //停止信号
	I2C_Stop();                    //停止信号
	return SET;
}
