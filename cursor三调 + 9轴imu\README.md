# STM32F1 AFE板测试系统

## 项目简介
基于STM32F103的AFE（模拟前端）板测试系统，用于测试AFE板的模拟信号采集、处理和输出功能。集成ADC采集、OLED显示和按键控制。

## 硬件配置
- **主控**: STM32F103C8T6
- **显示**: 128x64 OLED (I2C接口)
- **模拟输入**: 4路ADC通道 (PA0-PA3)
- **被测设备**: AFE板 (模拟前端板)
- **交互**: 按键输入 (PA0)

## 功能特性
- [x] 4路ADC模拟信号采集
- [x] OLED实时显示测试数据
- [x] 多种测试模式（单通道/多通道/连续/校准）
- [x] 按键交互控制
- [x] 实时数据处理和统计
- [x] AFE板性能测试
- [x] 自动校准功能
- [x] 测试结果显示

## 项目结构
```
cursor三调 + 9轴imu/
├── HardWare/           # 硬件驱动层
│   ├── Delay.c/h       # 延时函数
│   ├── OLED.c/h        # OLED显示驱动
│   ├── OLED_Font.c/h   # OLED字体数据
│   ├── IIC.c/h         # I2C通信
│   ├── Key.c/h         # 按键驱动
│   └── AFE_Test.c/h    # AFE板测试驱动
├── Library/            # STM32标准库
├── Start/              # 启动文件和系统配置
├── User/               # 用户应用层
│   ├── main.c          # AFE测试主程序
│   ├── system_defs.h   # 系统定义
│   └── stm32f10x_conf.h # 库配置
├── docs/               # 项目文档
└── Project.uvprojx     # Keil工程文件
```

## 编译环境
- **IDE**: Keil MDK-ARM
- **编译器**: ARM Compiler 5/6
- **调试器**: ST-Link V2

## 使用说明

### 测试模式
- **单通道测试**: 只测试AFE板的第一个输出通道
- **多通道测试**: 同时测试AFE板的所有4个输出通道
- **连续测试**: 高速连续采集模式
- **校准模式**: 零点校准和偏移补偿

### 操作方式
1. 上电后系统显示当前测试模式
2. **短按按键**: 切换测试模式
3. **长按按键**: 开始当前模式的测试
4. 测试中短按停止，长按强制退出
5. OLED实时显示测试数据和结果

### 连接方式
- AFE板输出通道1 → STM32 PA0 (ADC_IN0)
- AFE板输出通道2 → STM32 PA1 (ADC_IN1)
- AFE板输出通道3 → STM32 PA2 (ADC_IN2)
- AFE板输出通道4 → STM32 PA3 (ADC_IN3)
- 按键 → STM32 PA0 (共用，注意电路设计)

## 开发说明

### 主要修改
- 完全重构为AFE板测试系统
- 移除了IMU、红外传感器、电机控制等不相关功能
- 添加了专用的ADC采集和AFE测试功能
- 简化了用户界面和操作逻辑
- 优化了代码结构，专注于AFE板测试

### 扩展功能
如需添加新功能，建议在以下位置进行：
- AFE测试驱动: `HardWare/AFE_Test.c`
- 测试算法: `AFE_Process_Data()` 函数
- 显示界面: `AFE_Display_Results()` 函数
- 主程序逻辑: `User/main.c`

## 注意事项
- 确保AFE板与STM32的连接正确
- 注意ADC参考电压设置 (默认3.3V)
- 按键与ADC通道共用PA0，需要合理的电路设计
- 首次使用建议进行校准测试
- 测试时注意AFE板的输入信号范围

## 版本历史
- v1.0: 步态数据采集系统
- v1.1: 代码清理和云功能移除
- v2.0: 重构为AFE板测试系统
