# STM32F1 AFE板测试系统

## 项目简介
基于STM32F103的AFE（模拟前端）板测试系统，专门用于测试LTC2248 ADC核心的AFE板。通过12位并行数字接口读取AFE板输出，实现实时数据采集、统计分析和OLED显示。

## 硬件配置
- **主控**: STM32F103C8T6
- **显示**: 128x64 OLED (I2C接口)
- **数据接口**: 12位并行数字输入 (PC0-PC11)
- **时钟接口**: ADC时钟输入 (PA0，外部中断)
- **被测设备**: LTC2248 ADC核心AFE板

## 功能特性
- [x] 12位并行数字数据采集
- [x] 外部中断驱动的实时采样
- [x] 256个采样点的缓存处理
- [x] 实时统计分析（最大值、最小值、平均值）
- [x] OLED实时显示测试数据
- [x] 高速数据采集和处理
- [x] 完全硬件驱动，无需人工干预

## 项目结构
```
cursor三调 + 9轴imu/
├── HardWare/           # 硬件驱动层
│   ├── Delay.c/h       # 延时函数
│   ├── OLED.c/h        # OLED显示驱动
│   ├── OLED_Font.c/h   # OLED字体数据
│   ├── IIC.c/h         # I2C通信
│   └── AFE_Test.c/h    # AFE板测试驱动
├── Library/            # STM32标准库
├── Start/              # 启动文件和系统配置
├── User/               # 用户应用层
│   ├── main.c          # AFE测试主程序
│   ├── system_defs.h   # 系统定义
│   └── stm32f10x_conf.h # 库配置
├── docs/               # 项目文档
└── Project.uvprojx     # Keil工程文件
```

## 编译环境
- **IDE**: Keil MDK-ARM
- **编译器**: ARM Compiler 5/6
- **调试器**: ST-Link V2

## 使用说明

### 工作原理
1. **外部中断驱动**: AFE板ADC时钟信号触发STM32外部中断
2. **并行数据读取**: 中断服务函数读取12位并行数据
3. **缓存管理**: 256个采样点缓存，满后触发数据处理
4. **统计计算**: 自动计算最大值、最小值、平均值等统计信息
5. **实时显示**: OLED显示当前值和统计结果

### 工作方式
1. 上电后系统自动初始化
2. 等待AFE板ADC时钟信号
3. 每个时钟上升沿触发数据采集
4. 缓存满256个采样后进行统计分析
5. OLED实时显示采样值和统计结果
6. 持续循环，无需人工干预

### 连接方式
**AFE板12位数据线:**
- AFE_D0 → STM32 PC0
- AFE_D1 → STM32 PC1
- AFE_D2 → STM32 PC2
- ...
- AFE_D11 → STM32 PC11

**AFE板时钟线:**
- AFE_CLK → STM32 PA0 (外部中断0)

**显示接口:**
- OLED_SCL → STM32 PB6 (I2C时钟)
- OLED_SDA → STM32 PB7 (I2C数据)

## 开发说明

### 主要修改
- 完全重构为AFE板测试系统
- 移除了IMU、红外传感器、电机控制等不相关功能
- 添加了专用的ADC采集和AFE测试功能
- 简化了用户界面和操作逻辑
- 优化了代码结构，专注于AFE板测试

### 扩展功能
如需添加新功能，建议在以下位置进行：
- AFE测试驱动: `HardWare/AFE_Test.c`
- 测试算法: `AFE_Process_Data()` 函数
- 显示界面: `AFE_Display_Results()` 函数
- 主程序逻辑: `User/main.c`

## 技术规格
- **数据位宽**: 12位 (0-4095)
- **采样缓存**: 256个采样点
- **中断响应**: 外部中断0，上升沿触发
- **显示更新**: 每500ms更新一次
- **数据处理**: 硬件中断驱动，实时统计

## 注意事项
- 确保AFE板12位数据线与STM32 PC0-PC11正确连接
- AFE板时钟信号必须连接到STM32 PA0
- 确保AFE板输出为3.3V CMOS电平
- 系统上电后自动开始测试，无需额外操作
- 中断优先级设置为2，确保实时响应
- 数据缓存满后会自动重置，持续采集

## 版本历史
- v1.0: 步态数据采集系统
- v1.1: 代码清理和云功能移除
- v2.0: 重构为AFE板测试系统
- v2.1: 移除按键功能，改为全自动测试
- v2.2: 重新设计为12位并行数字接口AFE板测试
