# STM32F1 步态数据采集系统

## 项目简介
基于STM32F103的步态数据采集系统，集成9轴IMU传感器、红外距离传感器、OLED显示屏和电机控制功能。

## 硬件配置
- **主控**: STM32F103C8T6
- **显示**: 128x64 OLED (I2C接口)
- **传感器**: 
  - 9轴IMU (加速度计、陀螺仪、磁力计)
  - VL53L0X红外距离传感器
- **执行器**: 步进电机/舵机
- **交互**: 按键输入

## 功能特性
- [x] OLED实时显示传感器数据
- [x] 9轴IMU数据采集和处理
- [x] 红外距离测量
- [x] 传感器数据融合
- [x] 电机控制（多种训练模式）
- [x] 按键交互控制
- [x] 系统状态管理
- [ ] ~~云连接功能~~ (已禁用)

## 项目结构
```
cursor三调 + 9轴imu/
├── HardWare/           # 硬件驱动层
│   ├── Delay.c/h       # 延时函数
│   ├── OLED.c/h        # OLED显示驱动
│   ├── IIC.c/h         # I2C通信
│   ├── Key.c/h         # 按键驱动
│   ├── Motor.c/h       # 电机控制
│   ├── usart.c/h       # 串口通信
│   ├── ir_sensor.c/h   # 红外传感器
│   ├── sensor_fusion.c/h # 传感器融合
│   ├── control.c/h     # 控制逻辑
│   ├── exti.c/h        # 外部中断
│   └── spi.c/h         # SPI通信
├── Library/            # STM32标准库
├── Start/              # 启动文件和系统配置
├── User/               # 用户应用层
│   ├── main.c          # 主程序
│   ├── system_defs.h   # 系统定义
│   └── stm32f10x_conf.h # 库配置
├── docs/               # 项目文档
└── Project.uvprojx     # Keil工程文件
```

## 编译环境
- **IDE**: Keil MDK-ARM
- **编译器**: ARM Compiler 5/6
- **调试器**: ST-Link V2

## 使用说明

### 系统状态
- **IDLE**: 空闲状态，等待用户操作
- **RUNNING**: 运行状态，进行数据采集和电机控制
- **DEBUG**: 调试模式，显示详细传感器信息
- **CALIBRATION**: 校准模式，设置传感器初始值

### 操作方式
1. 上电后系统进入IDLE状态
2. 短按按键进入RUNNING状态开始工作
3. 长按按键可在不同状态间切换
4. OLED实时显示当前状态和传感器数据

### 训练模式
- **低阻力模式**: 适合热身和恢复训练
- **中阻力模式**: 标准训练强度
- **高阻力模式**: 高强度训练
- **自适应模式**: 根据用户表现自动调整

## 开发说明

### 主要修改
- 移除了云连接功能以简化系统
- 优化了代码结构和注释
- 删除了不必要的库文件和临时文件
- 修正了文件命名错误

### 扩展功能
如需添加新功能，建议在以下位置进行：
- 硬件驱动: `HardWare/` 目录
- 应用逻辑: `User/main.c`
- 系统配置: `User/system_defs.h`

## 注意事项
- 确保硬件连接正确
- 首次使用需要进行传感器校准
- 电机控制需要根据实际硬件调整参数
- 建议在安全环境下进行测试

## 版本历史
- v1.0: 基础功能实现
- v1.1: 代码优化和云功能移除
