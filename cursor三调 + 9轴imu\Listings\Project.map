Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to delay.o(i.delay_ms) for delay_ms
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to oled.o(i.OLED_Clear) for OLED_Clear
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to oled.o(i.OLED_Update) for OLED_Update
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to stm32f10x_it.o(.data) for press_time
    stm32f10x_it.o(i.EXTI0_IRQHandler) refers to main.o(.data) for systemState
    stm32f10x_it.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    stm32f10x_it.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    stm32f10x_it.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    stm32f10x_it.o(i.USART1_IRQHandler) refers to strstr.o(.text) for strstr
    stm32f10x_it.o(i.USART1_IRQHandler) refers to cloud_connect.o(.data) for ESP8266_RxCounter
    stm32f10x_it.o(i.USART1_IRQHandler) refers to cloud_connect.o(.bss) for ESP8266_RxBuffer
    stm32f10x_it.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    stm32f10x_it.o(i.USART2_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    stm32f10x_it.o(i.USART2_IRQHandler) refers to stm32f10x_it.o(.data) for rebuf_num
    stm32f10x_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for re_Buf_Data
    stm32f10x_it.o(i.USART2_IRQHandler) refers to usart.o(.data) for Receive_ok
    stm32f10x_it.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    stm32f10x_it.o(i.USART3_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    stm32f10x_it.o(i.USART3_IRQHandler) refers to usart.o(.data) for imu_rx_len
    stm32f10x_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for imu_Buf_Data
    main.o(i.Cloud_Command_Handler) refers to strcmpv7m.o(.text) for strcmp
    main.o(i.Cloud_Command_Handler) refers to strstr.o(.text) for strstr
    main.o(i.Cloud_Command_Handler) refers to motor.o(i.Motor_Release) for Motor_Release
    main.o(i.Cloud_Command_Handler) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.Cloud_Command_Handler) refers to main.o(.data) for motor_control_flag
    main.o(i.Debug_Mode) refers to contorl.o(i.Motor_Control_Task) for Motor_Control_Task
    main.o(i.Debug_Mode) refers to ir_sensor.o(i.Process_VL53L0X_Data) for Process_VL53L0X_Data
    main.o(i.Debug_Mode) refers to usart.o(i.Process_IMU_Data) for Process_IMU_Data
    main.o(i.Debug_Mode) refers to sensor_fusion.o(i.Update_Fusion_Data) for Update_Fusion_Data
    main.o(i.Debug_Mode) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    main.o(i.Debug_Mode) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    main.o(i.Debug_Mode) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.Debug_Mode) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Debug_Mode) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.Debug_Mode) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.Debug_Mode) refers to oled.o(i.OLED_ShowFloatNum) for OLED_ShowFloatNum
    main.o(i.Debug_Mode) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.Debug_Mode) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.Debug_Mode) refers to main.o(.data) for motor_control_flag
    main.o(i.Debug_Mode) refers to sensor_fusion.o(.bss) for fusion_data
    main.o(i.Process_Adaptive_Training) refers to motor.o(i.Motor_Release) for Motor_Release
    main.o(i.Process_Adaptive_Training) refers to ir_sensor.o(i.Process_VL53L0X_Data) for Process_VL53L0X_Data
    main.o(i.Process_Adaptive_Training) refers to usart.o(i.Process_IMU_Data) for Process_IMU_Data
    main.o(i.Process_Adaptive_Training) refers to sensor_fusion.o(i.Update_Fusion_Data) for Update_Fusion_Data
    main.o(i.Process_Adaptive_Training) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    main.o(i.Process_Adaptive_Training) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    main.o(i.Process_Adaptive_Training) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(i.Process_Adaptive_Training) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    main.o(i.Process_Adaptive_Training) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.Process_Adaptive_Training) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Process_Adaptive_Training) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.Process_Adaptive_Training) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.Process_Adaptive_Training) refers to oled.o(i.OLED_ShowFloatNum) for OLED_ShowFloatNum
    main.o(i.Process_Adaptive_Training) refers to cloud_connect.o(i.Cloud_GetStatus) for Cloud_GetStatus
    main.o(i.Process_Adaptive_Training) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.Process_Adaptive_Training) refers to main.o(.data) for phase_timer
    main.o(i.Process_Adaptive_Training) refers to sensor_fusion.o(.bss) for fusion_data
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to key.o(i.Key_Init) for Key_Init
    main.o(i.main) refers to motor.o(i.Motor_Init) for Motor_Init
    main.o(i.main) refers to contorl.o(i.Control_Init) for Control_Init
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.main) refers to motor.o(i.Motor_Test) for Motor_Test
    main.o(i.main) refers to iic.o(i.IIC_Init) for IIC_Init
    main.o(i.main) refers to usart.o(i.Usart_Int) for Usart_Int
    main.o(i.main) refers to usart.o(i.IMU_Usart_Init) for IMU_Usart_Init
    main.o(i.main) refers to sensor_fusion.o(i.Sensor_Fusion_Init) for Sensor_Fusion_Init
    main.o(i.main) refers to usart.o(i.ESP8266_Usart_Init) for ESP8266_Usart_Init
    main.o(i.main) refers to cloud_connect.o(i.Cloud_Init) for Cloud_Init
    main.o(i.main) refers to cloud_connect.o(i.Cloud_SetCommandCallback) for Cloud_SetCommandCallback
    main.o(i.main) refers to cloud_connect.o(i.Cloud_Connect) for Cloud_Connect
    main.o(i.main) refers to ir_sensor.o(i.VL53L0X_Init) for VL53L0X_Init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to usart.o(i.IMU_Set_Output_Mode) for IMU_Set_Output_Mode
    main.o(i.main) refers to usart.o(i.IMU_Set_Output_Frequency) for IMU_Set_Output_Frequency
    main.o(i.main) refers to usart.o(i.IMU_Calibrate_AccGyro) for IMU_Calibrate_AccGyro
    main.o(i.main) refers to usart.o(i.IMU_Calibrate_Mag) for IMU_Calibrate_Mag
    main.o(i.main) refers to main.o(i.Debug_Mode) for Debug_Mode
    main.o(i.main) refers to motor.o(i.Motor_Release) for Motor_Release
    main.o(i.main) refers to ir_sensor.o(i.Process_VL53L0X_Data) for Process_VL53L0X_Data
    main.o(i.main) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.main) refers to contorl.o(i.Motor_Control_Task) for Motor_Control_Task
    main.o(i.main) refers to usart.o(i.Process_IMU_Data) for Process_IMU_Data
    main.o(i.main) refers to sensor_fusion.o(i.Update_Fusion_Data) for Update_Fusion_Data
    main.o(i.main) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    main.o(i.main) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    main.o(i.main) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.main) refers to oled.o(i.OLED_ShowFloatNum) for OLED_ShowFloatNum
    main.o(i.main) refers to cloud_connect.o(i.Cloud_GetStatus) for Cloud_GetStatus
    main.o(i.main) refers to main.o(i.Cloud_Command_Handler) for Cloud_Command_Handler
    main.o(i.main) refers to usart.o(.data) for IMU_OUTPUT_EULER
    main.o(i.main) refers to main.o(.data) for system_time
    main.o(i.main) refers to sensor_fusion.o(.bss) for fusion_data
    main.o(i.main) refers to sensor_fusion.o(i.Calculate_Joint_Angle_From_IMU) for Calculate_Joint_Angle_From_IMU
    main.o(i.main) refers to cloud_connect.o(i.Cloud_Process) for Cloud_Process
    main.o(i.main) refers to cloud_connect.o(i.Cloud_ProcessIMUData) for Cloud_ProcessIMUData
    main.o(i.main) refers to cloud_connect.o(i.Cloud_UploadProperty) for Cloud_UploadProperty
    main.o(i.main) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    main.o(i.main) refers to main.o(i.Process_Adaptive_Training) for Process_Adaptive_Training
    contorl.o(i.Control_Init) refers to motor.o(i.Motor_Init) for Motor_Init
    contorl.o(i.Control_Init) refers to key.o(i.Key_Init) for Key_Init
    contorl.o(i.Control_Init) refers to motor.o(i.Motor_Test) for Motor_Test
    contorl.o(i.Control_Init) refers to contorl.o(.data) for motor_initialized
    contorl.o(i.Motor_Control_Task) refers to motor.o(i.Motor_GetStatus) for Motor_GetStatus
    contorl.o(i.Motor_Control_Task) refers to motor.o(i.Motor_IsRunning) for Motor_IsRunning
    contorl.o(i.Motor_Control_Task) refers to motor.o(i.Motor_One) for Motor_One
    contorl.o(i.Motor_Control_Task) refers to motor.o(i.Motor_two) for Motor_two
    contorl.o(i.Motor_Control_Task) refers to motor.o(i.Motor_one_two) for Motor_one_two
    contorl.o(i.Motor_Control_Task) refers to delay.o(i.delay_ms) for delay_ms
    contorl.o(i.Motor_Control_Task) refers to motor.o(i.Motor_Direction_Angle) for Motor_Direction_Angle
    contorl.o(i.Motor_Control_Task) refers to motor.o(i.Motor_Stop) for Motor_Stop
    contorl.o(i.Motor_Control_Task) refers to motor.o(i.Motor_Init) for Motor_Init
    contorl.o(i.Motor_Control_Task) refers to main.o(.data) for g_app_context
    contorl.o(i.Motor_Control_Task) refers to contorl.o(.data) for motor_initialized
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for Times_us
    delay.o(i.delay_ms) refers to delay.o(.data) for Times_ms
    delay.o(i.delay_us) refers to delay.o(.data) for Times_us
    exti.o(i.Exti_Int) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    exti.o(i.Exti_Int) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    exti.o(i.Exti_Int) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    exti.o(i.Exti_Int) refers to sqrt.o(i.sqrt) for sqrt
    exti.o(i.Exti_Int) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    exti.o(i.Exti_Int) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    exti.o(i.Exti_Int) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    iic.o(i.I2C_RecvByte) refers to iic.o(i.delay_1us) for delay_1us
    iic.o(i.I2C_RecvByte) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    iic.o(i.I2C_SendACK) refers to iic.o(i.delay_1us) for delay_1us
    iic.o(i.I2C_SendByte) refers to iic.o(i.delay_1us) for delay_1us
    iic.o(i.I2C_Start) refers to iic.o(i.delay_1us) for delay_1us
    iic.o(i.I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    iic.o(i.I2C_Stop) refers to iic.o(i.delay_1us) for delay_1us
    iic.o(i.I2C_WaitAck) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    iic.o(i.I2C_WaitAck) refers to iic.o(i.delay_1us) for delay_1us
    iic.o(i.IIC_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    iic.o(i.IIC_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    iic.o(i.Single_ReadI2C) refers to iic.o(i.I2C_Start) for I2C_Start
    iic.o(i.Single_ReadI2C) refers to iic.o(i.I2C_Stop) for I2C_Stop
    iic.o(i.Single_ReadI2C) refers to iic.o(i.I2C_SendByte) for I2C_SendByte
    iic.o(i.Single_ReadI2C) refers to iic.o(i.I2C_WaitAck) for I2C_WaitAck
    iic.o(i.Single_ReadI2C) refers to iic.o(i.I2C_RecvByte) for I2C_RecvByte
    iic.o(i.Single_ReadI2C) refers to iic.o(i.I2C_SendACK) for I2C_SendACK
    iic.o(i.Single_WriteI2C_byte) refers to iic.o(i.I2C_Start) for I2C_Start
    iic.o(i.Single_WriteI2C_byte) refers to iic.o(i.I2C_Stop) for I2C_Stop
    iic.o(i.Single_WriteI2C_byte) refers to iic.o(i.I2C_SendByte) for I2C_SendByte
    iic.o(i.Single_WriteI2C_byte) refers to iic.o(i.I2C_WaitAck) for I2C_WaitAck
    ir_sensor.o(i.ConvertDistanceToAngle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    ir_sensor.o(i.ConvertDistanceToAngle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    ir_sensor.o(i.ConvertDistanceToAngle) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    ir_sensor.o(i.ConvertDistanceToAngle) refers to acosf.o(i.acosf) for acosf
    ir_sensor.o(i.ConvertDistanceToAngle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    ir_sensor.o(i.ConvertDistanceToAngle) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    ir_sensor.o(i.ConvertDistanceToAngle) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    ir_sensor.o(i.ConvertDistanceToAngle) refers to ir_sensor.o(.data) for calibrated_init_distance
    ir_sensor.o(i.ConvertDistanceToAngle) refers to main.o(.data) for initial_distance
    ir_sensor.o(i.DetermineTrainingMode) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    ir_sensor.o(i.KalmanFilter_Update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    ir_sensor.o(i.KalmanFilter_Update) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    ir_sensor.o(i.KalmanFilter_Update) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    ir_sensor.o(i.KalmanFilter_Update) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    ir_sensor.o(i.Process_VL53L0X_Data) refers to ir_sensor.o(i.VL53L0X_TriggerMeasurement) for VL53L0X_TriggerMeasurement
    ir_sensor.o(i.Process_VL53L0X_Data) refers to delay.o(i.delay_ms) for delay_ms
    ir_sensor.o(i.Process_VL53L0X_Data) refers to ir_sensor.o(i.VL53L0X_ReadDistance) for VL53L0X_ReadDistance
    ir_sensor.o(i.Process_VL53L0X_Data) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    ir_sensor.o(i.Process_VL53L0X_Data) refers to ir_sensor.o(i.KalmanFilter_Update) for KalmanFilter_Update
    ir_sensor.o(i.Process_VL53L0X_Data) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    ir_sensor.o(i.Process_VL53L0X_Data) refers to ir_sensor.o(.data) for distance_filter
    ir_sensor.o(i.VL53L0X_Init) refers to iic.o(i.IIC_Init) for IIC_Init
    ir_sensor.o(i.VL53L0X_Init) refers to ir_sensor.o(i.KalmanFilter_Init) for KalmanFilter_Init
    ir_sensor.o(i.VL53L0X_Init) refers to delay.o(i.delay_ms) for delay_ms
    ir_sensor.o(i.VL53L0X_Init) refers to ir_sensor.o(.data) for distance_filter
    ir_sensor.o(i.VL53L0X_ReadDistance) refers to ir_sensor.o(i.requestRange) for requestRange
    ir_sensor.o(i.VL53L0X_TriggerMeasurement) refers to ir_sensor.o(i.takeRangeReading) for takeRangeReading
    ir_sensor.o(i.requestRange) refers to iic.o(i.I2C_Start) for I2C_Start
    ir_sensor.o(i.requestRange) refers to iic.o(i.I2C_Stop) for I2C_Stop
    ir_sensor.o(i.requestRange) refers to iic.o(i.I2C_SendByte) for I2C_SendByte
    ir_sensor.o(i.requestRange) refers to iic.o(i.I2C_WaitAck) for I2C_WaitAck
    ir_sensor.o(i.requestRange) refers to iic.o(i.I2C_RecvByte) for I2C_RecvByte
    ir_sensor.o(i.requestRange) refers to iic.o(i.I2C_SendACK) for I2C_SendACK
    ir_sensor.o(i.takeRangeReading) refers to iic.o(i.I2C_Start) for I2C_Start
    ir_sensor.o(i.takeRangeReading) refers to iic.o(i.I2C_Stop) for I2C_Stop
    ir_sensor.o(i.takeRangeReading) refers to iic.o(i.I2C_SendByte) for I2C_SendByte
    ir_sensor.o(i.takeRangeReading) refers to iic.o(i.I2C_WaitAck) for I2C_WaitAck
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    key.o(i.Key_Init) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    key.o(i.Key_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    key.o(i.Key_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    motor.o(i.Motor_Control) refers to motor.o(i.Motor_Start) for Motor_Start
    motor.o(i.Motor_Control) refers to motor.o(i.Motor_IsRunning) for Motor_IsRunning
    motor.o(i.Motor_Control) refers to motor.o(i.Motor_One) for Motor_One
    motor.o(i.Motor_Control) refers to motor.o(i.Motor_two) for Motor_two
    motor.o(i.Motor_Control) refers to motor.o(i.Motor_one_two) for Motor_one_two
    motor.o(i.Motor_Control) refers to motor.o(i.Motor_Stop) for Motor_Stop
    motor.o(i.Motor_Control) refers to motor.o(i.Motor_Release) for Motor_Release
    motor.o(i.Motor_Control) refers to motor.o(.data) for motor_running
    motor.o(i.Motor_Direction) refers to motor.o(i.Motor_One) for Motor_One
    motor.o(i.Motor_Direction) refers to motor.o(i.Motor_two) for Motor_two
    motor.o(i.Motor_Direction) refers to motor.o(i.Motor_one_two) for Motor_one_two
    motor.o(i.Motor_Direction) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_Direction) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_Direction) refers to delay.o(i.delay_ms) for delay_ms
    motor.o(i.Motor_Direction) refers to motor.o(.data) for current_step_mode
    motor.o(i.Motor_Direction_Angle) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_Direction_Angle) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_Direction_Angle) refers to delay.o(i.delay_ms) for delay_ms
    motor.o(i.Motor_Direction_Angle) refers to motor.o(.data) for current_step_mode
    motor.o(i.Motor_Direction_Angle) refers to motor.o(i.Motor_Release) for Motor_Release
    motor.o(i.Motor_GetStatus) refers to motor.o(.data) for current_step_mode
    motor.o(i.Motor_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.Motor_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.Motor_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_Init) refers to motor.o(.data) for motor_running
    motor.o(i.Motor_IsRunning) refers to motor.o(.data) for motor_running
    motor.o(i.Motor_One) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_One) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_One) refers to delay.o(i.delay_ms) for delay_ms
    motor.o(i.Motor_One) refers to motor.o(.data) for step
    motor.o(i.Motor_Release) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_Release) refers to motor.o(.data) for motor_running
    motor.o(i.Motor_Start) refers to motor.o(i.Motor_One) for Motor_One
    motor.o(i.Motor_Start) refers to motor.o(i.Motor_two) for Motor_two
    motor.o(i.Motor_Start) refers to motor.o(i.Motor_one_two) for Motor_one_two
    motor.o(i.Motor_Start) refers to motor.o(.data) for current_step_mode
    motor.o(i.Motor_Stop) refers to motor.o(i.Motor_Release) for Motor_Release
    motor.o(i.Motor_Stop) refers to motor.o(.data) for motor_running
    motor.o(i.Motor_Test) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_Test) refers to delay.o(i.delay_ms) for delay_ms
    motor.o(i.Motor_Test) refers to motor.o(i.Motor_Direction_Angle) for Motor_Direction_Angle
    motor.o(i.Motor_Test) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_Test) refers to motor.o(i.Motor_Release) for Motor_Release
    motor.o(i.Motor_one_two) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_one_two) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_one_two) refers to delay.o(i.delay_ms) for delay_ms
    motor.o(i.Motor_one_two) refers to motor.o(.data) for step
    motor.o(i.Motor_two) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_two) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_two) refers to delay.o(i.delay_ms) for delay_ms
    motor.o(i.Motor_two) refers to motor.o(.data) for step
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    oled.o(i.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(i.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i.OLED_DrawEllipse) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_GPIO_Init) for OLED_GPIO_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_font.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowChinese) refers to strcmpv7m.o(.text) for strcmp
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChinese) refers to oled_font.o(.constdata) for OLED_CF16x16
    oled.o(i.OLED_ShowFloatNum) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_W_SCL) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_W_SDA) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    spi.o(i.Spi1_Int) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    spi.o(i.Spi1_Int) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    spi.o(i.Spi1_Int) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    spi.o(i.Spi1_Int) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    spi.o(i.Spi1_Int) refers to stm32f10x_spi.o(i.SPI_Init) for SPI_Init
    spi.o(i.Spi1_Int) refers to stm32f10x_spi.o(i.SPI_Cmd) for SPI_Cmd
    spi.o(i.Spi_RW) refers to stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus) for SPI_I2S_GetFlagStatus
    spi.o(i.Spi_RW) refers to stm32f10x_spi.o(i.SPI_I2S_SendData) for SPI_I2S_SendData
    spi.o(i.Spi_RW) refers to stm32f10x_spi.o(i.SPI_I2S_ReceiveData) for SPI_I2S_ReceiveData
    spi.o(i.Spi_read_buf) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    spi.o(i.Spi_read_buf) refers to spi.o(i.Spi_RW) for Spi_RW
    spi.o(i.Spi_read_buf) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    spi.o(i.Spi_write_buf) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    spi.o(i.Spi_write_buf) refers to spi.o(i.Spi_RW) for Spi_RW
    spi.o(i.Spi_write_buf) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    usart.o(i.ESP8266_Usart_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.ESP8266_Usart_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.ESP8266_Usart_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.ESP8266_Usart_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.ESP8266_Usart_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.ESP8266_Usart_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.IMU_Calibrate_AccGyro) refers to usart.o(i.IMU_Send_Command) for IMU_Send_Command
    usart.o(i.IMU_Calibrate_Mag) refers to usart.o(i.IMU_Send_Command) for IMU_Send_Command
    usart.o(i.IMU_Restore_Default) refers to usart.o(i.IMU_Send_Command) for IMU_Send_Command
    usart.o(i.IMU_Send_Command) refers to usart.o(i.USART3_send_byte) for USART3_send_byte
    usart.o(i.IMU_Set_Output_Frequency) refers to usart.o(i.USART3_send_byte) for USART3_send_byte
    usart.o(i.IMU_Set_Output_Mode) refers to usart.o(i.USART3_send_byte) for USART3_send_byte
    usart.o(i.IMU_Usart_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart.o(i.IMU_Usart_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.IMU_Usart_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    usart.o(i.IMU_Usart_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.IMU_Usart_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.IMU_Usart_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.IMU_Usart_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.IMU_Usart_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.NVIC_Configuration) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    usart.o(i.NVIC_Configuration) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.Process_IMU_Data) refers to sensor_fusion.o(i.Parse_IMU_Data) for Parse_IMU_Data
    usart.o(i.Process_IMU_Data) refers to usart.o(.data) for imu_Receive_ok
    usart.o(i.Process_IMU_Data) refers to usart.o(.bss) for imu_Buf_Data
    usart.o(i.Process_IMU_Data) refers to sensor_fusion.o(.bss) for imu_data
    usart.o(i.USART1_send_byte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART2_Send_bytes) refers to usart.o(i.USART2_send_byte) for USART2_send_byte
    usart.o(i.USART2_send_byte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART3_Send_bytes) refers to usart.o(i.USART3_send_byte) for USART3_send_byte
    usart.o(i.USART3_send_byte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART_Send) refers to usart.o(i.USART1_send_byte) for USART1_send_byte
    usart.o(i.USART_Send_bytes) refers to usart.o(i.USART1_send_byte) for USART1_send_byte
    usart.o(i.Usart_Int) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart.o(i.Usart_Int) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.Usart_Int) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.Usart_Int) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.Usart_Int) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.Usart_Int) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.Usart_Int) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.display) refers to usart.o(i.USART1_send_byte) for USART1_send_byte
    usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.send_3out) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.send_3out) refers to usart.o(i.USART_Send) for USART_Send
    usart.o(i.send_out) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.send_out) refers to usart.o(i.USART_Send) for USART_Send
    usart.o(.data) refers to usart.o(.conststring) for .conststring
    sensor_fusion.o(i.Calculate_Joint_Angle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    sensor_fusion.o(i.Calculate_Joint_Angle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    sensor_fusion.o(i.Calculate_Joint_Angle) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    sensor_fusion.o(i.Calculate_Joint_Angle) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    sensor_fusion.o(i.Calculate_Joint_Angle) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    sensor_fusion.o(i.Calculate_Joint_Angle) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    sensor_fusion.o(i.Calculate_Joint_Angle) refers to acosf.o(i.acosf) for acosf
    sensor_fusion.o(i.Calculate_Joint_Angle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    sensor_fusion.o(i.Calculate_Joint_Angle_From_IMU) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    sensor_fusion.o(i.Calculate_Joint_Angle_From_IMU) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    sensor_fusion.o(i.Fusion_Algorithm) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    sensor_fusion.o(i.Fusion_Algorithm) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    sensor_fusion.o(i.Fusion_Algorithm) refers to sqrtf.o(i.sqrtf) for sqrtf
    sensor_fusion.o(i.Fusion_Algorithm) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    sensor_fusion.o(i.Fusion_Algorithm) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    sensor_fusion.o(i.Fusion_Algorithm) refers to sensor_fusion.o(i.Kalman_Filter_Update) for Kalman_Filter_Update
    sensor_fusion.o(i.Fusion_Algorithm) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    sensor_fusion.o(i.Fusion_Algorithm) refers to sensor_fusion.o(.bss) for imu_data
    sensor_fusion.o(i.Fusion_Algorithm) refers to sensor_fusion.o(.data) for angle_filter
    sensor_fusion.o(i.Kalman_Filter_Update) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    sensor_fusion.o(i.Kalman_Filter_Update) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    sensor_fusion.o(i.Kalman_Filter_Update) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    sensor_fusion.o(i.Kalman_Filter_Update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    sensor_fusion.o(i.Parse_IMU_Data) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    sensor_fusion.o(i.Parse_IMU_Data) refers to strncpy.o(.text) for strncpy
    sensor_fusion.o(i.Parse_IMU_Data) refers to strchr.o(.text) for strchr
    sensor_fusion.o(i.Parse_IMU_Data) refers to strstr.o(.text) for strstr
    sensor_fusion.o(i.Parse_IMU_Data) refers to strtok.o(.text) for strtok
    sensor_fusion.o(i.Parse_IMU_Data) refers to atof.o(i.atof) for atof
    sensor_fusion.o(i.Parse_IMU_Data) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    sensor_fusion.o(i.Parse_IMU_Data) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    sensor_fusion.o(i.Parse_IMU_Data) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    sensor_fusion.o(i.Parse_IMU_Data) refers to sqrtf.o(i.sqrtf) for sqrtf
    sensor_fusion.o(i.Parse_IMU_Data) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    sensor_fusion.o(i.Parse_IMU_Data) refers to atan2f.o(i.atan2f) for atan2f
    sensor_fusion.o(i.Sensor_Fusion_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    sensor_fusion.o(i.Sensor_Fusion_Init) refers to sensor_fusion.o(.bss) for imu_data
    sensor_fusion.o(i.Sensor_Fusion_Init) refers to sensor_fusion.o(.data) for angle_filter
    sensor_fusion.o(i.Update_Fusion_Data) refers to sensor_fusion.o(i.Calculate_Joint_Angle) for Calculate_Joint_Angle
    sensor_fusion.o(i.Update_Fusion_Data) refers to sensor_fusion.o(i.Calculate_Joint_Angle_From_IMU) for Calculate_Joint_Angle_From_IMU
    sensor_fusion.o(i.Update_Fusion_Data) refers to sensor_fusion.o(i.Fusion_Algorithm) for Fusion_Algorithm
    sensor_fusion.o(i.Update_Fusion_Data) refers to main.o(.data) for initial_distance
    cloud_connect.o(i.AT_write) refers to strlen.o(.text) for strlen
    cloud_connect.o(i.AT_write) refers to usart.o(i.USART2_Send_bytes) for USART2_Send_bytes
    cloud_connect.o(i.AT_write) refers to strstr.o(.text) for strstr
    cloud_connect.o(i.AT_write) refers to delay.o(i.delay_ms) for delay_ms
    cloud_connect.o(i.AT_write) refers to cloud_connect.o(.data) for atok_rec_flag
    cloud_connect.o(i.AT_write) refers to cloud_connect.o(.bss) for ESP8266_RxBuffer
    cloud_connect.o(i.Cloud_Connect) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    cloud_connect.o(i.Cloud_Connect) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    cloud_connect.o(i.Cloud_Connect) refers to _printf_str.o(.text) for _printf_str
    cloud_connect.o(i.Cloud_Connect) refers to __2sprintf.o(.text) for __2sprintf
    cloud_connect.o(i.Cloud_Connect) refers to cloud_connect.o(i.AT_write) for AT_write
    cloud_connect.o(i.Cloud_Connect) refers to cloud_connect.o(.data) for atok_rec_flag
    cloud_connect.o(i.Cloud_Connect) refers to cloud_connect.o(.bss) for cloud_status
    cloud_connect.o(i.Cloud_GetStatus) refers to cloud_connect.o(.bss) for cloud_status
    cloud_connect.o(i.Cloud_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    cloud_connect.o(i.Cloud_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    cloud_connect.o(i.Cloud_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    cloud_connect.o(i.Cloud_Init) refers to delay.o(i.delay_ms) for delay_ms
    cloud_connect.o(i.Cloud_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    cloud_connect.o(i.Cloud_Init) refers to cloud_connect.o(i.AT_write) for AT_write
    cloud_connect.o(i.Cloud_Init) refers to cloud_connect.o(.data) for atok_rec_flag
    cloud_connect.o(i.Cloud_Process) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    cloud_connect.o(i.Cloud_Process) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    cloud_connect.o(i.Cloud_Process) refers to _printf_str.o(.text) for _printf_str
    cloud_connect.o(i.Cloud_Process) refers to strstr.o(.text) for strstr
    cloud_connect.o(i.Cloud_Process) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    cloud_connect.o(i.Cloud_Process) refers to strlen.o(.text) for strlen
    cloud_connect.o(i.Cloud_Process) refers to __2sprintf.o(.text) for __2sprintf
    cloud_connect.o(i.Cloud_Process) refers to cloud_connect.o(i.AT_write) for AT_write
    cloud_connect.o(i.Cloud_Process) refers to cloud_connect.o(.data) for ESP8266_DataReceived
    cloud_connect.o(i.Cloud_Process) refers to cloud_connect.o(.bss) for cloud_status
    cloud_connect.o(i.Cloud_Process) refers to cloud_connect.o(.conststring) for .conststring
    cloud_connect.o(i.Cloud_ProcessIMUData) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    cloud_connect.o(i.Cloud_ProcessIMUData) refers to cloud_connect.o(.bss) for cloud_status
    cloud_connect.o(i.Cloud_SetCommandCallback) refers to cloud_connect.o(.data) for cmd_callback
    cloud_connect.o(i.Cloud_UploadData) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    cloud_connect.o(i.Cloud_UploadData) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    cloud_connect.o(i.Cloud_UploadData) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    cloud_connect.o(i.Cloud_UploadData) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    cloud_connect.o(i.Cloud_UploadData) refers to _printf_dec.o(.text) for _printf_int_dec
    cloud_connect.o(i.Cloud_UploadData) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    cloud_connect.o(i.Cloud_UploadData) refers to _printf_str.o(.text) for _printf_str
    cloud_connect.o(i.Cloud_UploadData) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    cloud_connect.o(i.Cloud_UploadData) refers to __2sprintf.o(.text) for __2sprintf
    cloud_connect.o(i.Cloud_UploadData) refers to cloud_connect.o(i.AT_write) for AT_write
    cloud_connect.o(i.Cloud_UploadData) refers to cloud_connect.o(.bss) for cloud_status
    cloud_connect.o(i.Cloud_UploadData) refers to cloud_connect.o(.conststring) for .conststring
    cloud_connect.o(i.Cloud_UploadData) refers to cloud_connect.o(.data) for atok_rec_flag
    cloud_connect.o(i.Cloud_UploadProperty) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    cloud_connect.o(i.Cloud_UploadProperty) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    cloud_connect.o(i.Cloud_UploadProperty) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    cloud_connect.o(i.Cloud_UploadProperty) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    cloud_connect.o(i.Cloud_UploadProperty) refers to _printf_str.o(.text) for _printf_str
    cloud_connect.o(i.Cloud_UploadProperty) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    cloud_connect.o(i.Cloud_UploadProperty) refers to __2sprintf.o(.text) for __2sprintf
    cloud_connect.o(i.Cloud_UploadProperty) refers to cloud_connect.o(i.AT_write) for AT_write
    cloud_connect.o(i.Cloud_UploadProperty) refers to cloud_connect.o(.bss) for cloud_status
    cloud_connect.o(i.Cloud_UploadProperty) refers to cloud_connect.o(.conststring) for .conststring
    cloud_connect.o(i.Cloud_UploadProperty) refers to cloud_connect.o(.data) for atok_rec_flag
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    strtok.o(.text) refers to strtok_int.o(.text) for __strtok_internal
    strtok.o(.text) refers to strtok.o(.data) for .data
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    acosf.o(i.__softfp_acosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acosf.o(i.__softfp_acosf) refers to acosf.o(i.acosf) for acosf
    acosf.o(i.acosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acosf.o(i.acosf) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    acosf.o(i.acosf) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    acosf.o(i.acosf) refers to sqrtf.o(i.sqrtf) for sqrtf
    acosf.o(i.acosf) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    acosf.o(i.acosf) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    acosf.o(i.acosf) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    acosf.o(i.acosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    acosf.o(i.acosf) refers to _rserrno.o(.text) for __set_errno
    acosf.o(i.acosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    acosf_x.o(i.____softfp_acosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acosf_x.o(i.____softfp_acosf$lsc) refers to acosf_x.o(i.__acosf$lsc) for __acosf$lsc
    acosf_x.o(i.__acosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acosf_x.o(i.__acosf$lsc) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    acosf_x.o(i.__acosf$lsc) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    acosf_x.o(i.__acosf$lsc) refers to sqrtf.o(i.sqrtf) for sqrtf
    acosf_x.o(i.__acosf$lsc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    acosf_x.o(i.__acosf$lsc) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    acosf_x.o(i.__acosf$lsc) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    acosf_x.o(i.__acosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    acosf_x.o(i.__acosf$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.atan2f) for atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atan2f.o(i.atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atan2f.o(i.atan2f) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.__atan2f$lsc) for __atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atan2f_x.o(i.__atan2f$lsc) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f_x.o(i.__atan2f$lsc) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    atan2f_x.o(i.__atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.__atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    strtok_int.o(.text) refers to strspn.o(.text) for strspn
    strtok_int.o(.text) refers to strcspn.o(.text) for strcspn
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dsqrt_noumaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fsqrt.o(x$fpl$fsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fsqrt.o(x$fpl$fsqrt) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    scalbnf.o(x$fpl$scalbnf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbnf.o(x$fpl$scalbnf) refers to fcheck1.o(x$fpl$fcheck1) for __fpl_fcheck_NaN1
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    funder.o(i.__mathlib_flt_divzero) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_infnan) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_infnan2) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    funder.o(i.__mathlib_flt_invalid) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_overflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_posinfnan) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    funder.o(i.__mathlib_flt_underflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    fcheck1.o(x$fpl$fcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__mathlib_tofloat) for __mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__mathlib_tofloat) for __mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.__ldexp$lsc) for __ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.__ldexp$lsc) for __ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.__ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.__ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.__ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_Cmd), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseInit), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing main.o(.bss), (100 bytes).
    Removing delay.o(i.delay_us), (60 bytes).
    Removing exti.o(i.Exti_Int), (96 bytes).
    Removing iic.o(i.Single_ReadI2C), (150 bytes).
    Removing iic.o(i.Single_WriteI2C_byte), (90 bytes).
    Removing ir_sensor.o(i.ConvertDistanceToAngle), (300 bytes).
    Removing ir_sensor.o(i.DetermineTrainingMode), (44 bytes).
    Removing motor.o(i.Motor_Control), (272 bytes).
    Removing motor.o(i.Motor_Direction), (432 bytes).
    Removing motor.o(i.Motor_Start), (84 bytes).
    Removing oled.o(i.OLED_DrawArc), (618 bytes).
    Removing oled.o(i.OLED_DrawCircle), (352 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (812 bytes).
    Removing oled.o(i.OLED_DrawLine), (374 bytes).
    Removing oled.o(i.OLED_DrawPoint), (80 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (142 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (232 bytes).
    Removing oled.o(i.OLED_GetPoint), (68 bytes).
    Removing oled.o(i.OLED_IsInAngle), (124 bytes).
    Removing oled.o(i.OLED_Printf), (50 bytes).
    Removing oled.o(i.OLED_Reverse), (52 bytes).
    Removing oled.o(i.OLED_ReverseArea), (144 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (70 bytes).
    Removing oled.o(i.OLED_ShowChinese), (152 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (96 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (112 bytes).
    Removing oled.o(i.OLED_UpdateArea), (124 bytes).
    Removing oled.o(i.OLED_pnpoly), (140 bytes).
    Removing spi.o(i.Spi1_Int), (180 bytes).
    Removing spi.o(i.Spi_RW), (56 bytes).
    Removing spi.o(i.Spi_read_buf), (64 bytes).
    Removing spi.o(i.Spi_write_buf), (64 bytes).
    Removing usart.o(i.IMU_Restore_Default), (24 bytes).
    Removing usart.o(i.NVIC_Configuration), (40 bytes).
    Removing usart.o(i.USART1_send_byte), (32 bytes).
    Removing usart.o(i.USART3_Send_bytes), (28 bytes).
    Removing usart.o(i.USART_Send), (46 bytes).
    Removing usart.o(i.USART_Send_bytes), (28 bytes).
    Removing usart.o(i.display), (244 bytes).
    Removing usart.o(i.fputc), (36 bytes).
    Removing usart.o(i.send_3out), (98 bytes).
    Removing usart.o(i.send_out), (126 bytes).
    Removing cloud_connect.o(i.Cloud_UploadData), (228 bytes).

495 unused section(s) (total 24422 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok_int.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_noumaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcheck1.s                       0x00000000   Number         0  fcheck1.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/fsqrt.s                         0x00000000   Number         0  fsqrt.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scalbnf.s                       0x00000000   Number         0  scalbnf.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/acosf.c                       0x00000000   Number         0  acosf.o ABSOLUTE
    ../mathlib/acosf.c                       0x00000000   Number         0  acosf_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    HardWare\Delay.c                         0x00000000   Number         0  delay.o ABSOLUTE
    HardWare\IIC.c                           0x00000000   Number         0  iic.o ABSOLUTE
    HardWare\Key.c                           0x00000000   Number         0  key.o ABSOLUTE
    HardWare\Motor.c                         0x00000000   Number         0  motor.o ABSOLUTE
    HardWare\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    HardWare\OLED_Font.c                     0x00000000   Number         0  oled_font.o ABSOLUTE
    HardWare\cloud_connect.c                 0x00000000   Number         0  cloud_connect.o ABSOLUTE
    HardWare\contorl.c                       0x00000000   Number         0  contorl.o ABSOLUTE
    HardWare\exti.c                          0x00000000   Number         0  exti.o ABSOLUTE
    HardWare\ir_sensor.c                     0x00000000   Number         0  ir_sensor.o ABSOLUTE
    HardWare\sensor_fusion.c                 0x00000000   Number         0  sensor_fusion.o ABSOLUTE
    HardWare\spi.c                           0x00000000   Number         0  spi.o ABSOLUTE
    HardWare\usart.c                         0x00000000   Number         0  usart.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x08000160   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000014  0x08000166   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800016c   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000170   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000172   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000172   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000178   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000178   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000184   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000184   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800018e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800018e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000190   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000192   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000192   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000192   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000192   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000192   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000192   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000192   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000194   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000194   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000194   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800019a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800019a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800019e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800019e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001a6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001a8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001a8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001ac   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001b4   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001f4   Section        0  __2sprintf.o(.text)
    .text                                    0x08000220   Section        0  _printf_str.o(.text)
    .text                                    0x08000274   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080003fc   Section        0  strtok.o(.text)
    .text                                    0x08000408   Section        0  strchr.o(.text)
    .text                                    0x0800041c   Section        0  strstr.o(.text)
    .text                                    0x08000440   Section        0  strlen.o(.text)
    .text                                    0x0800047e   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080004e2   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000530   Section       86  strncpy.o(.text)
    .text                                    0x08000588   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000608   Section        0  heapauxi.o(.text)
    .text                                    0x0800060e   Section        0  _rserrno.o(.text)
    .text                                    0x08000624   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000627   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000a44   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000a45   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000a74   Section        0  _sputc.o(.text)
    .text                                    0x08000a7e   Section        0  _printf_char.o(.text)
    .text                                    0x08000aac   Section        0  strtod.o(.text)
    _local_sscanf                            0x08000aad   Thumb Code    60  strtod.o(.text)
    .text                                    0x08000b50   Section        0  strtok_int.o(.text)
    .text                                    0x08000b94   Section       68  rt_memclr.o(.text)
    .text                                    0x08000bd8   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000be0   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000be8   Section      138  lludiv10.o(.text)
    .text                                    0x08000c72   Section        0  isspace.o(.text)
    .text                                    0x08000c84   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000d04   Section        0  _sgetc.o(.text)
    .text                                    0x08000d44   Section        0  bigflt0.o(.text)
    .text                                    0x08000e28   Section        0  strcspn.o(.text)
    .text                                    0x08000e48   Section        0  strspn.o(.text)
    .text                                    0x08000e64   Section        8  libspace.o(.text)
    .text                                    0x08000e6c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000eb8   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08000ec8   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08000ec9   Thumb Code   588  scanf_fp.o(.text)
    .text                                    0x080013c0   Section        0  exit.o(.text)
    .text                                    0x080013d4   Section        0  scanf_hexfp.o(.text)
    .text                                    0x080016f4   Section        0  scanf_infnan.o(.text)
    .text                                    0x08001828   Section        0  sys_exit.o(.text)
    .text                                    0x08001834   Section       38  llshl.o(.text)
    .text                                    0x0800185a   Section        0  _chval.o(.text)
    .text                                    0x08001876   Section        2  use_no_semi.o(.text)
    .text                                    0x08001878   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x08001878   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080018b6   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080018fc   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800195c   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2d                             0x08001c94   Section      132  btod.o(CL$$btod_e2d)
    CL$$btod_e2e                             0x08001d18   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001df4   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_edivd                           0x08001e1e   Section       42  btod.o(CL$$btod_edivd)
    CL$$btod_emul                            0x08001e48   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_emuld                           0x08001e72   Section       42  btod.o(CL$$btod_emuld)
    CL$$btod_mult_common                     0x08001e9c   Section      580  btod.o(CL$$btod_mult_common)
    i.AT_write                               0x080020e0   Section        0  cloud_connect.o(i.AT_write)
    AT_write                                 0x080020e1   Thumb Code   110  cloud_connect.o(i.AT_write)
    i.BusFault_Handler                       0x08002170   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Calculate_Joint_Angle                  0x08002174   Section        0  sensor_fusion.o(i.Calculate_Joint_Angle)
    i.Calculate_Joint_Angle_From_IMU         0x08002260   Section        0  sensor_fusion.o(i.Calculate_Joint_Angle_From_IMU)
    i.Cloud_Command_Handler                  0x08002294   Section        0  main.o(i.Cloud_Command_Handler)
    i.Cloud_Connect                          0x0800242c   Section        0  cloud_connect.o(i.Cloud_Connect)
    i.Cloud_GetStatus                        0x0800269c   Section        0  cloud_connect.o(i.Cloud_GetStatus)
    i.Cloud_Init                             0x080026a4   Section        0  cloud_connect.o(i.Cloud_Init)
    i.Cloud_Process                          0x08002740   Section        0  cloud_connect.o(i.Cloud_Process)
    i.Cloud_ProcessIMUData                   0x08002934   Section        0  cloud_connect.o(i.Cloud_ProcessIMUData)
    i.Cloud_SetCommandCallback               0x0800296c   Section        0  cloud_connect.o(i.Cloud_SetCommandCallback)
    i.Cloud_UploadProperty                   0x08002978   Section        0  cloud_connect.o(i.Cloud_UploadProperty)
    i.Control_Init                           0x08002a3c   Section        0  contorl.o(i.Control_Init)
    i.DebugMon_Handler                       0x08002a58   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Debug_Mode                             0x08002a5c   Section        0  main.o(i.Debug_Mode)
    i.ESP8266_Usart_Init                     0x08002c34   Section        0  usart.o(i.ESP8266_Usart_Init)
    i.EXTI0_IRQHandler                       0x08002cd4   Section        0  stm32f10x_it.o(i.EXTI0_IRQHandler)
    i.EXTI_ClearITPendingBit                 0x080030b4   Section        0  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x080030c0   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.EXTI_Init                              0x080030e8   Section        0  stm32f10x_exti.o(i.EXTI_Init)
    i.Fusion_Algorithm                       0x0800317c   Section        0  sensor_fusion.o(i.Fusion_Algorithm)
    i.GPIO_EXTILineConfig                    0x080032e8   Section        0  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    i.GPIO_Init                              0x08003328   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x08003440   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.GPIO_ReadInputDataBit                  0x080034d0   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x080034e2   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x080034e6   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x080034ea   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.HardFault_Handler                      0x080034f4   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.I2C_RecvByte                           0x080034f8   Section        0  iic.o(i.I2C_RecvByte)
    i.I2C_SendACK                            0x0800354c   Section        0  iic.o(i.I2C_SendACK)
    i.I2C_SendByte                           0x08003584   Section        0  iic.o(i.I2C_SendByte)
    i.I2C_Start                              0x080035d4   Section        0  iic.o(i.I2C_Start)
    i.I2C_Stop                               0x08003634   Section        0  iic.o(i.I2C_Stop)
    i.I2C_WaitAck                            0x08003664   Section        0  iic.o(i.I2C_WaitAck)
    i.IIC_Init                               0x080036c4   Section        0  iic.o(i.IIC_Init)
    i.IMU_Calibrate_AccGyro                  0x080036f0   Section        0  usart.o(i.IMU_Calibrate_AccGyro)
    i.IMU_Calibrate_Mag                      0x0800370c   Section        0  usart.o(i.IMU_Calibrate_Mag)
    i.IMU_Send_Command                       0x08003728   Section        0  usart.o(i.IMU_Send_Command)
    i.IMU_Set_Output_Frequency               0x08003750   Section        0  usart.o(i.IMU_Set_Output_Frequency)
    i.IMU_Set_Output_Mode                    0x080037cc   Section        0  usart.o(i.IMU_Set_Output_Mode)
    i.IMU_Usart_Init                         0x0800380c   Section        0  usart.o(i.IMU_Usart_Init)
    i.KalmanFilter_Init                      0x080038c0   Section        0  ir_sensor.o(i.KalmanFilter_Init)
    i.KalmanFilter_Update                    0x080038d6   Section        0  ir_sensor.o(i.KalmanFilter_Update)
    i.Kalman_Filter_Update                   0x08003972   Section        0  sensor_fusion.o(i.Kalman_Filter_Update)
    i.Key_Init                               0x080039cc   Section        0  key.o(i.Key_Init)
    i.MemManage_Handler                      0x08003a3c   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.Motor_Control_Task                     0x08003a40   Section        0  contorl.o(i.Motor_Control_Task)
    i.Motor_Direction_Angle                  0x08003c70   Section        0  motor.o(i.Motor_Direction_Angle)
    i.Motor_GetStatus                        0x080041e4   Section        0  motor.o(i.Motor_GetStatus)
    i.Motor_Init                             0x0800420c   Section        0  motor.o(i.Motor_Init)
    i.Motor_IsRunning                        0x08004250   Section        0  motor.o(i.Motor_IsRunning)
    i.Motor_One                              0x08004270   Section        0  motor.o(i.Motor_One)
    i.Motor_Release                          0x08004360   Section        0  motor.o(i.Motor_Release)
    i.Motor_Stop                             0x08004384   Section        0  motor.o(i.Motor_Stop)
    i.Motor_Test                             0x080043a0   Section        0  motor.o(i.Motor_Test)
    i.Motor_one_two                          0x08004590   Section        0  motor.o(i.Motor_one_two)
    i.Motor_two                              0x08004708   Section        0  motor.o(i.Motor_two)
    i.NMI_Handler                            0x080047f8   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080047fc   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x0800486c   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x08004880   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x080048a8   Section        0  oled.o(i.OLED_ClearArea)
    i.OLED_GPIO_Init                         0x08004938   Section        0  oled.o(i.OLED_GPIO_Init)
    i.OLED_I2C_SendByte                      0x08004998   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x080049d6   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x080049f2   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x08004a08   Section        0  oled.o(i.OLED_Init)
    i.OLED_Pow                               0x08004aa2   Section        0  oled.o(i.OLED_Pow)
    i.OLED_SetCursor                         0x08004ab6   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08004adc   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowFloatNum                      0x08004b30   Section        0  oled.o(i.OLED_ShowFloatNum)
    i.OLED_ShowImage                         0x08004c04   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowNum                           0x08004d00   Section        0  oled.o(i.OLED_ShowNum)
    i.OLED_ShowString                        0x08004d4c   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x08004d7c   Section        0  oled.o(i.OLED_Update)
    i.OLED_W_SCL                             0x08004da4   Section        0  oled.o(i.OLED_W_SCL)
    i.OLED_W_SDA                             0x08004dbc   Section        0  oled.o(i.OLED_W_SDA)
    i.OLED_WriteCommand                      0x08004dd4   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08004df4   Section        0  oled.o(i.OLED_WriteData)
    i.Parse_IMU_Data                         0x08004e24   Section        0  sensor_fusion.o(i.Parse_IMU_Data)
    i.PendSV_Handler                         0x08005008   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.Process_Adaptive_Training              0x0800500c   Section        0  main.o(i.Process_Adaptive_Training)
    i.Process_IMU_Data                       0x0800541c   Section        0  usart.o(i.Process_IMU_Data)
    i.Process_VL53L0X_Data                   0x0800545c   Section        0  ir_sensor.o(i.Process_VL53L0X_Data)
    i.RCC_APB1PeriphClockCmd                 0x080054d8   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080054f8   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08005518   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x080055ec   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.Sensor_Fusion_Init                     0x080055f0   Section        0  sensor_fusion.o(i.Sensor_Fusion_Init)
    i.SetSysClock                            0x08005634   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08005635   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x0800563c   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x0800563d   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x0800571c   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08005744   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08005748   Section        0  system_stm32f10x.o(i.SystemInit)
    i.USART1_IRQHandler                      0x080057a8   Section        0  stm32f10x_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080058bc   Section        0  stm32f10x_it.o(i.USART2_IRQHandler)
    i.USART2_Send_bytes                      0x08005918   Section        0  usart.o(i.USART2_Send_bytes)
    i.USART2_send_byte                       0x08005934   Section        0  usart.o(i.USART2_send_byte)
    i.USART3_IRQHandler                      0x08005954   Section        0  stm32f10x_it.o(i.USART3_IRQHandler)
    i.USART3_send_byte                       0x080059b0   Section        0  usart.o(i.USART3_send_byte)
    i.USART_ClearITPendingBit                0x080059d0   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x080059ee   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08005a06   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08005a20   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08005a74   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08005ac0   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08005b98   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.Update_Fusion_Data                     0x08005ba4   Section        0  sensor_fusion.o(i.Update_Fusion_Data)
    i.UsageFault_Handler                     0x08005c78   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.Usart_Int                              0x08005c7c   Section        0  usart.o(i.Usart_Int)
    i.VL53L0X_Init                           0x08005d20   Section        0  ir_sensor.o(i.VL53L0X_Init)
    i.VL53L0X_ReadDistance                   0x08005d50   Section        0  ir_sensor.o(i.VL53L0X_ReadDistance)
    i.VL53L0X_TriggerMeasurement             0x08005d5e   Section        0  ir_sensor.o(i.VL53L0X_TriggerMeasurement)
    i.__ARM_fpclassify                       0x08005d68   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x08005d90   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__mathlib_dbl_overflow                 0x08005db6   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08005dc4   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_flt_infnan                   0x08005dd4   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x08005dda   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x08005dde   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x08005de6   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_narrow                       0x08005df0   Section        0  narrow.o(i.__mathlib_narrow)
    i.__mathlib_tofloat                      0x08005e00   Section        0  narrow.o(i.__mathlib_tofloat)
    i.__support_ldexp                        0x08005ebc   Section        0  ldexp.o(i.__support_ldexp)
    i._is_digit                              0x08005ed2   Section        0  __printf_wp.o(i._is_digit)
    i.acosf                                  0x08005ee0   Section        0  acosf.o(i.acosf)
    i.atan2f                                 0x08005ffc   Section        0  atan2f.o(i.atan2f)
    i.atof                                   0x08006258   Section        0  atof.o(i.atof)
    i.delay_1us                              0x08006282   Section        0  iic.o(i.delay_1us)
    i.delay_init                             0x08006298   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080062d4   Section        0  delay.o(i.delay_ms)
    i.frexp                                  0x08006310   Section        0  frexp.o(i.frexp)
    i.ldexp                                  0x08006368   Section        0  ldexp.o(i.ldexp)
    i.main                                   0x080063dc   Section        0  main.o(i.main)
    i.requestRange                           0x08006a64   Section        0  ir_sensor.o(i.requestRange)
    i.round                                  0x08006ac4   Section        0  round.o(i.round)
    i.sqrtf                                  0x08006b58   Section        0  sqrtf.o(i.sqrtf)
    i.takeRangeReading                       0x08006b84   Section        0  ir_sensor.o(i.takeRangeReading)
    locale$$code                             0x08006bc8   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08006bf4   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x08006c20   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08006c84   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08006c95   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08006dd4   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x08006de4   Section       24  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$deqf                               0x08006dfc   Section      120  deqf.o(x$fpl$deqf)
    x$fpl$dfixu                              0x08006e74   Section       90  dfixu.o(x$fpl$dfixu)
    x$fpl$dfltu                              0x08006ece   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08006ef4   Section      120  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08006f6c   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080070c0   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800715c   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08007168   Section      108  drleqf.o(x$fpl$drleqf)
    x$fpl$drnd                               0x080071d4   Section      180  drnd.o(x$fpl$drnd)
    x$fpl$drsb                               0x08007288   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x080072a0   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x080072b1   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08007474   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x080074cc   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x080074db   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcheck1                            0x08007590   Section       12  fcheck1.o(x$fpl$fcheck1)
    x$fpl$fcmpinf                            0x0800759c   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x080075b4   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080075b5   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffixu                              0x08007738   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$ffltu                              0x08007778   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fleqf                              0x080077a0   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08007808   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x0800790a   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08007996   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x080079a0   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$frsb                               0x08007a02   Section       20  faddsub_clz.o(x$fpl$frsb)
    x$fpl$fsqrt                              0x08007a18   Section      272  fsqrt.o(x$fpl$fsqrt)
    x$fpl$fsub                               0x08007b28   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08007b37   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$ieeestatus                         0x08007c12   Section        6  istatus.o(x$fpl$ieeestatus)
    x$fpl$printf1                            0x08007c18   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x08007c1c   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08007c80   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$scalbnf                            0x08007cdc   Section       76  scalbnf.o(x$fpl$scalbnf)
    x$fpl$scanf1                             0x08007d28   Section        4  scanf1.o(x$fpl$scanf1)
    x$fpl$scanf2                             0x08007d2c   Section        8  scanf2.o(x$fpl$scanf2)
    x$fpl$trapveneer                         0x08007d34   Section       48  trapv.o(x$fpl$trapveneer)
    .constdata                               0x08007d64   Section     8518  oled_font.o(.constdata)
    x$fpl$usenofp                            0x08007d64   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08009eaa   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08009eaa   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08009ebc   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08009ebc   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08009ef8   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08009f50   Section      153  usart.o(.conststring)
    .conststring                             0x08009fec   Section      141  cloud_connect.o(.conststring)
    c$$dinf                                  0x0800a09c   Section        8  fpconst.o(c$$dinf)
    c$$dmax                                  0x0800a0a4   Section        8  fpconst.o(c$$dmax)
    locale$$data                             0x0800a0ac   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800a0b0   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800a0b8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800a0c4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800a0c6   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800a0c7   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800a0c8   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800a0c8   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0800a0cc   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800a0d4   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800a1d8   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section        5  stm32f10x_it.o(.data)
    press_time                               0x20000014   Data           4  stm32f10x_it.o(.data)
    rebuf_num                                0x20000018   Data           1  stm32f10x_it.o(.data)
    .data                                    0x2000001c   Section       91  main.o(.data)
    last_cloud_process_time                  0x20000058   Data           4  main.o(.data)
    last_cloud_upload_time                   0x2000005c   Data           4  main.o(.data)
    phase                                    0x20000060   Data           1  main.o(.data)
    phase_timer                              0x20000064   Data           4  main.o(.data)
    cycle_count                              0x20000068   Data           1  main.o(.data)
    angle_threshold                          0x2000006c   Data           4  main.o(.data)
    prev_angle                               0x20000070   Data           4  main.o(.data)
    direction                                0x20000074   Data           1  main.o(.data)
    first_run                                0x20000075   Data           1  main.o(.data)
    update_counter                           0x20000076   Data           1  main.o(.data)
    .data                                    0x20000078   Section       16  contorl.o(.data)
    motor_initialized                        0x20000078   Data           1  contorl.o(.data)
    last_motor_run_time                      0x2000007c   Data           4  contorl.o(.data)
    motor_error_count                        0x20000080   Data           1  contorl.o(.data)
    last_mode                                0x20000081   Data           1  contorl.o(.data)
    motor_test_done                          0x20000082   Data           1  contorl.o(.data)
    last_step_time                           0x20000084   Data           4  contorl.o(.data)
    .data                                    0x20000088   Section        4  delay.o(.data)
    Times_us                                 0x20000088   Data           1  delay.o(.data)
    Times_ms                                 0x2000008a   Data           2  delay.o(.data)
    .data                                    0x2000008c   Section       34  ir_sensor.o(.data)
    ranging_mode                             0x2000008c   Data           1  ir_sensor.o(.data)
    distance_filter                          0x20000090   Data          28  ir_sensor.o(.data)
    calibrated_init_distance                 0x200000ac   Data           2  ir_sensor.o(.data)
    .data                                    0x200000ae   Section        9  motor.o(.data)
    motor_running                            0x200000ae   Data           1  motor.o(.data)
    remaining_steps                          0x200000b0   Data           2  motor.o(.data)
    current_step_mode                        0x200000b2   Data           1  motor.o(.data)
    current_speed                            0x200000b3   Data           1  motor.o(.data)
    step                                     0x200000b4   Data           1  motor.o(.data)
    step                                     0x200000b5   Data           1  motor.o(.data)
    step                                     0x200000b6   Data           1  motor.o(.data)
    .data                                    0x200000b8   Section       52  usart.o(.data)
    .data                                    0x200000ec   Section       25  sensor_fusion.o(.data)
    previous_fused_angle                     0x200000ec   Data           4  sensor_fusion.o(.data)
    angle_filter                             0x200000f0   Data          20  sensor_fusion.o(.data)
    init_flag                                0x20000104   Data           1  sensor_fusion.o(.data)
    .data                                    0x20000108   Section        9  cloud_connect.o(.data)
    cmd_callback                             0x2000010c   Data           4  cloud_connect.o(.data)
    atok_rec_flag                            0x20000110   Data           1  cloud_connect.o(.data)
    .data                                    0x20000114   Section        4  strtok.o(.data)
    _strtok_saves1                           0x20000114   Data           4  strtok.o(.data)
    .bss                                     0x20000118   Section     1024  oled.o(.bss)
    .bss                                     0x20000518   Section      150  usart.o(.bss)
    .bss                                     0x200005b0   Section       76  sensor_fusion.o(.bss)
    .bss                                     0x200005fc   Section      336  cloud_connect.o(.bss)
    cloud_status                             0x200006fc   Data          80  cloud_connect.o(.bss)
    .bss                                     0x2000074c   Section       96  libspace.o(.bss)
    HEAP                                     0x200007b0   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x200007b0   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x200009b0   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x200009b0   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x20000db0   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x08000161   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_s                                0x08000167   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800016d   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000171   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000173   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000185   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800018f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000191   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000193   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000193   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000193   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000193   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000193   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000193   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000193   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000195   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000195   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000195   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800019b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800019b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800019f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800019f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001a7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001a9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001a9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001ad   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001b5   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM2_IRQHandler                          0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001cf   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001d1   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __2sprintf                               0x080001f5   Thumb Code    38  __2sprintf.o(.text)
    _printf_str                              0x08000221   Thumb Code    82  _printf_str.o(.text)
    __printf                                 0x08000275   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strtok                                   0x080003fd   Thumb Code     6  strtok.o(.text)
    strchr                                   0x08000409   Thumb Code    20  strchr.o(.text)
    strstr                                   0x0800041d   Thumb Code    36  strstr.o(.text)
    strlen                                   0x08000441   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy4                          0x0800047f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800047f   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800047f   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080004c7   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x080004e3   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080004e3   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080004e3   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080004e7   Thumb Code     0  rt_memclr_w.o(.text)
    strncpy                                  0x08000531   Thumb Code    86  strncpy.o(.text)
    strcmp                                   0x08000589   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000609   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800060b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800060d   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x0800060f   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000619   Thumb Code    12  _rserrno.o(.text)
    __lib_sel_fp_printf                      0x08000625   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080007d7   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000a4f   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000a75   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08000a7f   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000a93   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000aa3   Thumb Code     8  _printf_char.o(.text)
    __strtod_int                             0x08000ae9   Thumb Code    90  strtod.o(.text)
    __strtok_internal                        0x08000b51   Thumb Code    64  strtok_int.o(.text)
    __aeabi_memclr                           0x08000b95   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000b95   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000b99   Thumb Code     0  rt_memclr.o(.text)
    __rt_locale                              0x08000bd9   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08000be1   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000be1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000be1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000be9   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x08000c73   Thumb Code    18  isspace.o(.text)
    _printf_fp_infnan                        0x08000c85   Thumb Code   112  _printf_fp_infnan.o(.text)
    _sgetc                                   0x08000d05   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000d23   Thumb Code    34  _sgetc.o(.text)
    _btod_etento                             0x08000d45   Thumb Code   224  bigflt0.o(.text)
    strcspn                                  0x08000e29   Thumb Code    32  strcspn.o(.text)
    strspn                                   0x08000e49   Thumb Code    28  strspn.o(.text)
    __user_libspace                          0x08000e65   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000e65   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000e65   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000e6d   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08000eb9   Thumb Code    16  rt_ctype_table.o(.text)
    _scanf_really_real                       0x08001115   Thumb Code   684  scanf_fp.o(.text)
    exit                                     0x080013c1   Thumb Code    18  exit.o(.text)
    _scanf_really_hex_real                   0x080013d5   Thumb Code   786  scanf_hexfp.o(.text)
    _scanf_really_infnan                     0x080016f5   Thumb Code   292  scanf_infnan.o(.text)
    _sys_exit                                0x08001829   Thumb Code     8  sys_exit.o(.text)
    __aeabi_llsl                             0x08001835   Thumb Code     0  llshl.o(.text)
    _ll_shift_l                              0x08001835   Thumb Code    38  llshl.o(.text)
    _chval                                   0x0800185b   Thumb Code    28  _chval.o(.text)
    __I$use$semihosting                      0x08001877   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001877   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001879   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x08001879   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080018b7   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080018fd   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800195d   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2d                                     0x08001c95   Thumb Code   122  btod.o(CL$$btod_e2d)
    _e2e                                     0x08001d19   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001df5   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_edivd                              0x08001e1f   Thumb Code    42  btod.o(CL$$btod_edivd)
    _btod_emul                               0x08001e49   Thumb Code    42  btod.o(CL$$btod_emul)
    _btod_emuld                              0x08001e73   Thumb Code    42  btod.o(CL$$btod_emuld)
    __btod_mult_common                       0x08001e9d   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08002171   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    Calculate_Joint_Angle                    0x08002175   Thumb Code   208  sensor_fusion.o(i.Calculate_Joint_Angle)
    Calculate_Joint_Angle_From_IMU           0x08002261   Thumb Code    48  sensor_fusion.o(i.Calculate_Joint_Angle_From_IMU)
    Cloud_Command_Handler                    0x08002295   Thumb Code   296  main.o(i.Cloud_Command_Handler)
    Cloud_Connect                            0x0800242d   Thumb Code   178  cloud_connect.o(i.Cloud_Connect)
    Cloud_GetStatus                          0x0800269d   Thumb Code     4  cloud_connect.o(i.Cloud_GetStatus)
    Cloud_Init                               0x080026a5   Thumb Code   122  cloud_connect.o(i.Cloud_Init)
    Cloud_Process                            0x08002741   Thumb Code   290  cloud_connect.o(i.Cloud_Process)
    Cloud_ProcessIMUData                     0x08002935   Thumb Code    50  cloud_connect.o(i.Cloud_ProcessIMUData)
    Cloud_SetCommandCallback                 0x0800296d   Thumb Code     6  cloud_connect.o(i.Cloud_SetCommandCallback)
    Cloud_UploadProperty                     0x08002979   Thumb Code    80  cloud_connect.o(i.Cloud_UploadProperty)
    Control_Init                             0x08002a3d   Thumb Code    22  contorl.o(i.Control_Init)
    DebugMon_Handler                         0x08002a59   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Debug_Mode                               0x08002a5d   Thumb Code   364  main.o(i.Debug_Mode)
    ESP8266_Usart_Init                       0x08002c35   Thumb Code   152  usart.o(i.ESP8266_Usart_Init)
    EXTI0_IRQHandler                         0x08002cd5   Thumb Code   734  stm32f10x_it.o(i.EXTI0_IRQHandler)
    EXTI_ClearITPendingBit                   0x080030b5   Thumb Code     6  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x080030c1   Thumb Code    34  stm32f10x_exti.o(i.EXTI_GetITStatus)
    EXTI_Init                                0x080030e9   Thumb Code   142  stm32f10x_exti.o(i.EXTI_Init)
    Fusion_Algorithm                         0x0800317d   Thumb Code   318  sensor_fusion.o(i.Fusion_Algorithm)
    GPIO_EXTILineConfig                      0x080032e9   Thumb Code    60  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    GPIO_Init                                0x08003329   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x08003441   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    GPIO_ReadInputDataBit                    0x080034d1   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x080034e3   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x080034e7   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x080034eb   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    HardFault_Handler                        0x080034f5   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    I2C_RecvByte                             0x080034f9   Thumb Code    78  iic.o(i.I2C_RecvByte)
    I2C_SendACK                              0x0800354d   Thumb Code    52  iic.o(i.I2C_SendACK)
    I2C_SendByte                             0x08003585   Thumb Code    76  iic.o(i.I2C_SendByte)
    I2C_Start                                0x080035d5   Thumb Code    90  iic.o(i.I2C_Start)
    I2C_Stop                                 0x08003635   Thumb Code    42  iic.o(i.I2C_Stop)
    I2C_WaitAck                              0x08003665   Thumb Code    92  iic.o(i.I2C_WaitAck)
    IIC_Init                                 0x080036c5   Thumb Code    38  iic.o(i.IIC_Init)
    IMU_Calibrate_AccGyro                    0x080036f1   Thumb Code    10  usart.o(i.IMU_Calibrate_AccGyro)
    IMU_Calibrate_Mag                        0x0800370d   Thumb Code    10  usart.o(i.IMU_Calibrate_Mag)
    IMU_Send_Command                         0x08003729   Thumb Code    40  usart.o(i.IMU_Send_Command)
    IMU_Set_Output_Frequency                 0x08003751   Thumb Code   124  usart.o(i.IMU_Set_Output_Frequency)
    IMU_Set_Output_Mode                      0x080037cd   Thumb Code    64  usart.o(i.IMU_Set_Output_Mode)
    IMU_Usart_Init                           0x0800380d   Thumb Code   168  usart.o(i.IMU_Usart_Init)
    KalmanFilter_Init                        0x080038c1   Thumb Code    22  ir_sensor.o(i.KalmanFilter_Init)
    KalmanFilter_Update                      0x080038d7   Thumb Code   156  ir_sensor.o(i.KalmanFilter_Update)
    Kalman_Filter_Update                     0x08003973   Thumb Code    90  sensor_fusion.o(i.Kalman_Filter_Update)
    Key_Init                                 0x080039cd   Thumb Code   106  key.o(i.Key_Init)
    MemManage_Handler                        0x08003a3d   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    Motor_Control_Task                       0x08003a41   Thumb Code   528  contorl.o(i.Motor_Control_Task)
    Motor_Direction_Angle                    0x08003c71   Thumb Code  1382  motor.o(i.Motor_Direction_Angle)
    Motor_GetStatus                          0x080041e5   Thumb Code    26  motor.o(i.Motor_GetStatus)
    Motor_Init                               0x0800420d   Thumb Code    56  motor.o(i.Motor_Init)
    Motor_IsRunning                          0x08004251   Thumb Code    22  motor.o(i.Motor_IsRunning)
    Motor_One                                0x08004271   Thumb Code   224  motor.o(i.Motor_One)
    Motor_Release                            0x08004361   Thumb Code    22  motor.o(i.Motor_Release)
    Motor_Stop                               0x08004385   Thumb Code    18  motor.o(i.Motor_Stop)
    Motor_Test                               0x080043a1   Thumb Code   490  motor.o(i.Motor_Test)
    Motor_one_two                            0x08004591   Thumb Code   360  motor.o(i.Motor_one_two)
    Motor_two                                0x08004709   Thumb Code   224  motor.o(i.Motor_two)
    NMI_Handler                              0x080047f9   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x080047fd   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x0800486d   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x08004881   Thumb Code    36  oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x080048a9   Thumb Code   140  oled.o(i.OLED_ClearArea)
    OLED_GPIO_Init                           0x08004939   Thumb Code    92  oled.o(i.OLED_GPIO_Init)
    OLED_I2C_SendByte                        0x08004999   Thumb Code    62  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x080049d7   Thumb Code    28  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x080049f3   Thumb Code    22  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x08004a09   Thumb Code   154  oled.o(i.OLED_Init)
    OLED_Pow                                 0x08004aa3   Thumb Code    20  oled.o(i.OLED_Pow)
    OLED_SetCursor                           0x08004ab7   Thumb Code    38  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08004add   Thumb Code    74  oled.o(i.OLED_ShowChar)
    OLED_ShowFloatNum                        0x08004b31   Thumb Code   210  oled.o(i.OLED_ShowFloatNum)
    OLED_ShowImage                           0x08004c05   Thumb Code   248  oled.o(i.OLED_ShowImage)
    OLED_ShowNum                             0x08004d01   Thumb Code    76  oled.o(i.OLED_ShowNum)
    OLED_ShowString                          0x08004d4d   Thumb Code    46  oled.o(i.OLED_ShowString)
    OLED_Update                              0x08004d7d   Thumb Code    36  oled.o(i.OLED_Update)
    OLED_W_SCL                               0x08004da5   Thumb Code    18  oled.o(i.OLED_W_SCL)
    OLED_W_SDA                               0x08004dbd   Thumb Code    18  oled.o(i.OLED_W_SDA)
    OLED_WriteCommand                        0x08004dd5   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08004df5   Thumb Code    46  oled.o(i.OLED_WriteData)
    Parse_IMU_Data                           0x08004e25   Thumb Code   436  sensor_fusion.o(i.Parse_IMU_Data)
    PendSV_Handler                           0x08005009   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    Process_Adaptive_Training                0x0800500d   Thumb Code   840  main.o(i.Process_Adaptive_Training)
    Process_IMU_Data                         0x0800541d   Thumb Code    48  usart.o(i.Process_IMU_Data)
    Process_VL53L0X_Data                     0x0800545d   Thumb Code   116  ir_sensor.o(i.Process_VL53L0X_Data)
    RCC_APB1PeriphClockCmd                   0x080054d9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080054f9   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08005519   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x080055ed   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Sensor_Fusion_Init                       0x080055f1   Thumb Code    48  sensor_fusion.o(i.Sensor_Fusion_Init)
    SysTick_CLKSourceConfig                  0x0800571d   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08005745   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08005749   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    USART1_IRQHandler                        0x080057a9   Thumb Code   224  stm32f10x_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080058bd   Thumb Code    74  stm32f10x_it.o(i.USART2_IRQHandler)
    USART2_Send_bytes                        0x08005919   Thumb Code    28  usart.o(i.USART2_Send_bytes)
    USART2_send_byte                         0x08005935   Thumb Code    26  usart.o(i.USART2_send_byte)
    USART3_IRQHandler                        0x08005955   Thumb Code    74  stm32f10x_it.o(i.USART3_IRQHandler)
    USART3_send_byte                         0x080059b1   Thumb Code    26  usart.o(i.USART3_send_byte)
    USART_ClearITPendingBit                  0x080059d1   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x080059ef   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08005a07   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08005a21   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08005a75   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08005ac1   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08005b99   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    Update_Fusion_Data                       0x08005ba5   Thumb Code   170  sensor_fusion.o(i.Update_Fusion_Data)
    UsageFault_Handler                       0x08005c79   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    Usart_Int                                0x08005c7d   Thumb Code   156  usart.o(i.Usart_Int)
    VL53L0X_Init                             0x08005d21   Thumb Code    30  ir_sensor.o(i.VL53L0X_Init)
    VL53L0X_ReadDistance                     0x08005d51   Thumb Code    14  ir_sensor.o(i.VL53L0X_ReadDistance)
    VL53L0X_TriggerMeasurement               0x08005d5f   Thumb Code    10  ir_sensor.o(i.VL53L0X_TriggerMeasurement)
    __ARM_fpclassify                         0x08005d69   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x08005d91   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __mathlib_dbl_overflow                   0x08005db7   Thumb Code    14  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08005dc5   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_flt_infnan                     0x08005dd5   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x08005ddb   Thumb Code     4  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x08005ddf   Thumb Code     8  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x08005de7   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_narrow                         0x08005df1   Thumb Code    16  narrow.o(i.__mathlib_narrow)
    __mathlib_tofloat                        0x08005e01   Thumb Code   184  narrow.o(i.__mathlib_tofloat)
    __support_ldexp                          0x08005ebd   Thumb Code    22  ldexp.o(i.__support_ldexp)
    _is_digit                                0x08005ed3   Thumb Code    14  __printf_wp.o(i._is_digit)
    acosf                                    0x08005ee1   Thumb Code   242  acosf.o(i.acosf)
    atan2f                                   0x08005ffd   Thumb Code   528  atan2f.o(i.atan2f)
    atof                                     0x08006259   Thumb Code    42  atof.o(i.atof)
    delay_1us                                0x08006283   Thumb Code    22  iic.o(i.delay_1us)
    delay_init                               0x08006299   Thumb Code    52  delay.o(i.delay_init)
    delay_ms                                 0x080062d5   Thumb Code    56  delay.o(i.delay_ms)
    frexp                                    0x08006311   Thumb Code    80  frexp.o(i.frexp)
    ldexp                                    0x08006369   Thumb Code   116  ldexp.o(i.ldexp)
    main                                     0x080063dd   Thumb Code  1588  main.o(i.main)
    requestRange                             0x08006a65   Thumb Code    94  ir_sensor.o(i.requestRange)
    round                                    0x08006ac5   Thumb Code   136  round.o(i.round)
    sqrtf                                    0x08006b59   Thumb Code    44  sqrtf.o(i.sqrtf)
    takeRangeReading                         0x08006b85   Thumb Code    66  ir_sensor.o(i.takeRangeReading)
    _get_lc_numeric                          0x08006bc9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08006bf5   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x08006c21   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08006c21   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08006c85   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08006c85   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08006dd5   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x08006de5   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_cdcmpeq                          0x08006dfd   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x08006dfd   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_d2uiz                            0x08006e75   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08006e75   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_ui2d                             0x08006ecf   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08006ecf   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08006ef5   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08006ef5   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08006f57   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08006f6d   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08006f6d   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080070c1   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800715d   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08007169   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08007169   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    _drnd                                    0x080071d5   Thumb Code   180  drnd.o(x$fpl$drnd)
    __aeabi_drsub                            0x08007289   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08007289   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x080072a1   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x080072a1   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08007475   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08007475   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x080074cd   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x080074cd   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcheck_NaN1                        0x08007591   Thumb Code     6  fcheck1.o(x$fpl$fcheck1)
    __fpl_fcmp_Inf                           0x0800759d   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x080075b5   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080075b5   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2uiz                            0x08007739   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08007739   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_ui2f                             0x08007779   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08007779   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_cfcmple                          0x080077a1   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x080077a1   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x080077f3   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08007809   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08007809   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x0800790b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08007997   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x080079a1   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x080079a1   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_frsub                            0x08007a03   Thumb Code     0  faddsub_clz.o(x$fpl$frsb)
    _frsb                                    0x08007a03   Thumb Code    20  faddsub_clz.o(x$fpl$frsb)
    _fsqrt                                   0x08007a19   Thumb Code   272  fsqrt.o(x$fpl$fsqrt)
    __aeabi_fsub                             0x08007b29   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08007b29   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __ieee_status                            0x08007c13   Thumb Code     6  istatus.o(x$fpl$ieeestatus)
    _printf_fp_dec                           0x08007c19   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x08007c1d   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08007c81   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __ARM_scalbnf                            0x08007cdd   Thumb Code    76  scalbnf.o(x$fpl$scalbnf)
    _scanf_real                              0x08007d29   Thumb Code     4  scanf1.o(x$fpl$scanf1)
    _scanf_hex_real                          0x08007d2d   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    _scanf_infnan                            0x08007d31   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    __fpl_cmpreturn                          0x08007d35   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    OLED_F8x16                               0x08007d64   Data        1520  oled_font.o(.constdata)
    __I$use$fp                               0x08007d64   Number         0  usenofp.o(x$fpl$usenofp)
    OLED_F6x8                                0x08008354   Data         570  oled_font.o(.constdata)
    OLED_CF16x16                             0x0800858e   Data         252  oled_font.o(.constdata)
    Diode                                    0x0800868a   Data          32  oled_font.o(.constdata)
    Face_eyes                                0x080086aa   Data        1024  oled_font.o(.constdata)
    Face_happy                               0x08008aaa   Data        1024  oled_font.o(.constdata)
    Face_mania                               0x08008eaa   Data        1024  oled_font.o(.constdata)
    Face_sleep                               0x080092aa   Data        1024  oled_font.o(.constdata)
    Face_stare                               0x080096aa   Data        1024  oled_font.o(.constdata)
    Face_very_happy                          0x08009aaa   Data        1024  oled_font.o(.constdata)
    Region$$Table$$Base                      0x0800a07c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800a09c   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_HUGE_VAL                         0x0800a09c   Data           0  fpconst.o(c$$dinf)
    __aeabi_HUGE_VALL                        0x0800a09c   Data           0  fpconst.o(c$$dinf)
    __aeabi_INFINITY                         0x0800a09c   Data           0  fpconst.o(c$$dinf)
    __dInf                                   0x0800a09c   Data           0  fpconst.o(c$$dinf)
    __huge_val                               0x0800a09c   Data           0  fpconst.o(c$$dinf)
    __dbl_max                                0x0800a0a4   Data           0  fpconst.o(c$$dmax)
    __ctype                                  0x0800a0d5   Data           0  lc_ctype_c.o(locale$$data)
    systemState                              0x2000001c   Data           1  main.o(.data)
    system_start_flag                        0x2000001d   Data           1  main.o(.data)
    force_refresh_flag                       0x2000001e   Data           1  main.o(.data)
    ir_distance                              0x20000020   Data           2  main.o(.data)
    joint_angle                              0x20000024   Data           4  main.o(.data)
    current_mode                             0x20000028   Data           1  main.o(.data)
    sensor_data                              0x2000002a   Data           4  main.o(.data)
    initial_distance                         0x2000002e   Data           2  main.o(.data)
    training_time                            0x20000030   Data           4  main.o(.data)
    training_cycles                          0x20000034   Data           2  main.o(.data)
    auto_mode_flag                           0x20000036   Data           1  main.o(.data)
    motor_control_flag                       0x20000037   Data           1  main.o(.data)
    g_app_context                            0x20000038   Data          20  main.o(.data)
    prev_joint_angle                         0x2000004c   Data           4  main.o(.data)
    angle_stable_counter                     0x20000050   Data           1  main.o(.data)
    imu_buffer_index                         0x20000051   Data           1  main.o(.data)
    system_time                              0x20000054   Data           4  main.o(.data)
    Receive_ok                               0x200000b8   Data           1  usart.o(.data)
    imu_Receive_ok                           0x200000b9   Data           1  usart.o(.data)
    imu_rx_len                               0x200000ba   Data           2  usart.o(.data)
    IMU_CMD_PREFIX                           0x200000bc   Data           4  usart.o(.data)
    IMU_OUTPUT_EULER                         0x200000c0   Data           4  usart.o(.data)
    IMU_OUTPUT_QUATERNION                    0x200000c4   Data           4  usart.o(.data)
    IMU_OUTPUT_EARTH_A                       0x200000c8   Data           4  usart.o(.data)
    IMU_OUTPUT_ACC                           0x200000cc   Data           4  usart.o(.data)
    IMU_OUTPUT_GYRO                          0x200000d0   Data           4  usart.o(.data)
    IMU_OUTPUT_MAG                           0x200000d4   Data           4  usart.o(.data)
    IMU_OUTPUT_ALL                           0x200000d8   Data           4  usart.o(.data)
    IMU_CALI_AG                              0x200000dc   Data           4  usart.o(.data)
    IMU_CALI_MAG                             0x200000e0   Data           4  usart.o(.data)
    IMU_RESTORE                              0x200000e4   Data           4  usart.o(.data)
    IMU_FREQ_PREFIX                          0x200000e8   Data           4  usart.o(.data)
    ESP8266_RxCounter                        0x20000108   Data           2  cloud_connect.o(.data)
    ESP8266_DataReceived                     0x2000010a   Data           1  cloud_connect.o(.data)
    OLED_DisplayBuf                          0x20000118   Data        1024  oled.o(.bss)
    re_Buf_Data                              0x20000518   Data          50  usart.o(.bss)
    imu_Buf_Data                             0x2000054a   Data         100  usart.o(.bss)
    imu_data                                 0x200005b0   Data          52  sensor_fusion.o(.bss)
    fusion_data                              0x200005e4   Data          24  sensor_fusion.o(.bss)
    ESP8266_RxBuffer                         0x200005fc   Data         256  cloud_connect.o(.bss)
    __libspace_start                         0x2000074c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200007ac   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a2f0, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000a1d8, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO           19    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         4346  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         4881    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         4883    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         4885    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000000   Code   RO         4326    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x08000160   0x00000006   Code   RO         4325    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000166   0x08000166   0x00000006   Code   RO         4323    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800016c   0x0800016c   0x00000004   Code   RO         4517    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000170   0x08000170   0x00000002   Code   RO         4703    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000172   0x08000172   0x00000000   Code   RO         4705    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         4707    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         4710    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         4712    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         4714    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000172   0x08000172   0x00000006   Code   RO         4715    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x00000000   Code   RO         4717    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x0000000c   Code   RO         4718    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         4719    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x00000000   Code   RO         4721    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000184   0x08000184   0x0000000a   Code   RO         4722    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4723    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4725    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4727    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4729    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4731    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4733    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4735    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4737    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4741    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4743    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4745    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4747    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800018e   0x0800018e   0x00000002   Code   RO         4748    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000190   0x08000190   0x00000002   Code   RO         4817    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000192   0x08000192   0x00000000   Code   RO         4840    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000192   0x08000192   0x00000000   Code   RO         4842    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000192   0x08000192   0x00000000   Code   RO         4845    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000192   0x08000192   0x00000000   Code   RO         4848    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000192   0x08000192   0x00000000   Code   RO         4850    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000192   0x08000192   0x00000000   Code   RO         4853    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000192   0x08000192   0x00000002   Code   RO         4854    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000194   0x08000194   0x00000000   Code   RO         4456    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000194   0x08000194   0x00000000   Code   RO         4602    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000194   0x08000194   0x00000006   Code   RO         4614    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800019a   0x0800019a   0x00000000   Code   RO         4604    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800019a   0x0800019a   0x00000004   Code   RO         4605    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800019e   0x0800019e   0x00000000   Code   RO         4607    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800019e   0x0800019e   0x00000008   Code   RO         4608    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001a6   0x080001a6   0x00000002   Code   RO         4753    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001a8   0x080001a8   0x00000000   Code   RO         4781    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001a8   0x080001a8   0x00000004   Code   RO         4782    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001ac   0x080001ac   0x00000006   Code   RO         4783    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001b2   0x080001b2   0x00000002   PAD
    0x080001b4   0x080001b4   0x00000040   Code   RO           20    .text               startup_stm32f10x_md.o
    0x080001f4   0x080001f4   0x0000002c   Code   RO         4293    .text               c_w.l(__2sprintf.o)
    0x08000220   0x08000220   0x00000052   Code   RO         4299    .text               c_w.l(_printf_str.o)
    0x08000272   0x08000272   0x00000002   PAD
    0x08000274   0x08000274   0x00000188   Code   RO         4320    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080003fc   0x080003fc   0x0000000c   Code   RO         4327    .text               c_w.l(strtok.o)
    0x08000408   0x08000408   0x00000014   Code   RO         4330    .text               c_w.l(strchr.o)
    0x0800041c   0x0800041c   0x00000024   Code   RO         4332    .text               c_w.l(strstr.o)
    0x08000440   0x08000440   0x0000003e   Code   RO         4334    .text               c_w.l(strlen.o)
    0x0800047e   0x0800047e   0x00000064   Code   RO         4336    .text               c_w.l(rt_memcpy_w.o)
    0x080004e2   0x080004e2   0x0000004e   Code   RO         4338    .text               c_w.l(rt_memclr_w.o)
    0x08000530   0x08000530   0x00000056   Code   RO         4340    .text               c_w.l(strncpy.o)
    0x08000586   0x08000586   0x00000002   PAD
    0x08000588   0x08000588   0x00000080   Code   RO         4342    .text               c_w.l(strcmpv7m.o)
    0x08000608   0x08000608   0x00000006   Code   RO         4344    .text               c_w.l(heapauxi.o)
    0x0800060e   0x0800060e   0x00000016   Code   RO         4457    .text               c_w.l(_rserrno.o)
    0x08000624   0x08000624   0x0000041e   Code   RO         4467    .text               c_w.l(_printf_fp_dec.o)
    0x08000a42   0x08000a42   0x00000002   PAD
    0x08000a44   0x08000a44   0x00000030   Code   RO         4469    .text               c_w.l(_printf_char_common.o)
    0x08000a74   0x08000a74   0x0000000a   Code   RO         4471    .text               c_w.l(_sputc.o)
    0x08000a7e   0x08000a7e   0x0000002c   Code   RO         4473    .text               c_w.l(_printf_char.o)
    0x08000aaa   0x08000aaa   0x00000002   PAD
    0x08000aac   0x08000aac   0x000000a4   Code   RO         4527    .text               c_w.l(strtod.o)
    0x08000b50   0x08000b50   0x00000044   Code   RO         4529    .text               c_w.l(strtok_int.o)
    0x08000b94   0x08000b94   0x00000044   Code   RO         4531    .text               c_w.l(rt_memclr.o)
    0x08000bd8   0x08000bd8   0x00000008   Code   RO         4619    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000be0   0x08000be0   0x00000008   Code   RO         4624    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000be8   0x08000be8   0x0000008a   Code   RO         4626    .text               c_w.l(lludiv10.o)
    0x08000c72   0x08000c72   0x00000012   Code   RO         4628    .text               c_w.l(isspace.o)
    0x08000c84   0x08000c84   0x00000080   Code   RO         4633    .text               c_w.l(_printf_fp_infnan.o)
    0x08000d04   0x08000d04   0x00000040   Code   RO         4639    .text               c_w.l(_sgetc.o)
    0x08000d44   0x08000d44   0x000000e4   Code   RO         4641    .text               c_w.l(bigflt0.o)
    0x08000e28   0x08000e28   0x00000020   Code   RO         4671    .text               c_w.l(strcspn.o)
    0x08000e48   0x08000e48   0x0000001c   Code   RO         4673    .text               c_w.l(strspn.o)
    0x08000e64   0x08000e64   0x00000008   Code   RO         4687    .text               c_w.l(libspace.o)
    0x08000e6c   0x08000e6c   0x0000004a   Code   RO         4690    .text               c_w.l(sys_stackheap_outer.o)
    0x08000eb6   0x08000eb6   0x00000002   PAD
    0x08000eb8   0x08000eb8   0x00000010   Code   RO         4692    .text               c_w.l(rt_ctype_table.o)
    0x08000ec8   0x08000ec8   0x000004f8   Code   RO         4694    .text               c_w.l(scanf_fp.o)
    0x080013c0   0x080013c0   0x00000012   Code   RO         4696    .text               c_w.l(exit.o)
    0x080013d2   0x080013d2   0x00000002   PAD
    0x080013d4   0x080013d4   0x00000320   Code   RO         4787    .text               c_w.l(scanf_hexfp.o)
    0x080016f4   0x080016f4   0x00000134   Code   RO         4789    .text               c_w.l(scanf_infnan.o)
    0x08001828   0x08001828   0x0000000c   Code   RO         4803    .text               c_w.l(sys_exit.o)
    0x08001834   0x08001834   0x00000026   Code   RO         4807    .text               c_w.l(llshl.o)
    0x0800185a   0x0800185a   0x0000001c   Code   RO         4809    .text               c_w.l(_chval.o)
    0x08001876   0x08001876   0x00000002   Code   RO         4830    .text               c_w.l(use_no_semi.o)
    0x08001878   0x08001878   0x00000000   Code   RO         4832    .text               c_w.l(indicate_semi.o)
    0x08001878   0x08001878   0x0000003e   Code   RO         4644    CL$$btod_d2e        c_w.l(btod.o)
    0x080018b6   0x080018b6   0x00000046   Code   RO         4646    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080018fc   0x080018fc   0x00000060   Code   RO         4645    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800195c   0x0800195c   0x00000338   Code   RO         4654    CL$$btod_div_common  c_w.l(btod.o)
    0x08001c94   0x08001c94   0x00000084   Code   RO         4652    CL$$btod_e2d        c_w.l(btod.o)
    0x08001d18   0x08001d18   0x000000dc   Code   RO         4651    CL$$btod_e2e        c_w.l(btod.o)
    0x08001df4   0x08001df4   0x0000002a   Code   RO         4648    CL$$btod_ediv       c_w.l(btod.o)
    0x08001e1e   0x08001e1e   0x0000002a   Code   RO         4650    CL$$btod_edivd      c_w.l(btod.o)
    0x08001e48   0x08001e48   0x0000002a   Code   RO         4647    CL$$btod_emul       c_w.l(btod.o)
    0x08001e72   0x08001e72   0x0000002a   Code   RO         4649    CL$$btod_emuld      c_w.l(btod.o)
    0x08001e9c   0x08001e9c   0x00000244   Code   RO         4653    CL$$btod_mult_common  c_w.l(btod.o)
    0x080020e0   0x080020e0   0x00000090   Code   RO         4218    i.AT_write          cloud_connect.o
    0x08002170   0x08002170   0x00000004   Code   RO         3196    i.BusFault_Handler  stm32f10x_it.o
    0x08002174   0x08002174   0x000000ec   Code   RO         4155    i.Calculate_Joint_Angle  sensor_fusion.o
    0x08002260   0x08002260   0x00000034   Code   RO         4156    i.Calculate_Joint_Angle_From_IMU  sensor_fusion.o
    0x08002294   0x08002294   0x00000198   Code   RO         3321    i.Cloud_Command_Handler  main.o
    0x0800242c   0x0800242c   0x00000270   Code   RO         4219    i.Cloud_Connect     cloud_connect.o
    0x0800269c   0x0800269c   0x00000008   Code   RO         4220    i.Cloud_GetStatus   cloud_connect.o
    0x080026a4   0x080026a4   0x0000009c   Code   RO         4221    i.Cloud_Init        cloud_connect.o
    0x08002740   0x08002740   0x000001f4   Code   RO         4222    i.Cloud_Process     cloud_connect.o
    0x08002934   0x08002934   0x00000038   Code   RO         4223    i.Cloud_ProcessIMUData  cloud_connect.o
    0x0800296c   0x0800296c   0x0000000c   Code   RO         4224    i.Cloud_SetCommandCallback  cloud_connect.o
    0x08002978   0x08002978   0x000000c4   Code   RO         4226    i.Cloud_UploadProperty  cloud_connect.o
    0x08002a3c   0x08002a3c   0x0000001c   Code   RO         3384    i.Control_Init      contorl.o
    0x08002a58   0x08002a58   0x00000002   Code   RO         3197    i.DebugMon_Handler  stm32f10x_it.o
    0x08002a5a   0x08002a5a   0x00000002   PAD
    0x08002a5c   0x08002a5c   0x000001d8   Code   RO         3322    i.Debug_Mode        main.o
    0x08002c34   0x08002c34   0x000000a0   Code   RO         3993    i.ESP8266_Usart_Init  usart.o
    0x08002cd4   0x08002cd4   0x000003e0   Code   RO         3198    i.EXTI0_IRQHandler  stm32f10x_it.o
    0x080030b4   0x080030b4   0x0000000c   Code   RO          985    i.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x080030c0   0x080030c0   0x00000028   Code   RO          989    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x080030e8   0x080030e8   0x00000094   Code   RO          990    i.EXTI_Init         stm32f10x_exti.o
    0x0800317c   0x0800317c   0x0000016c   Code   RO         4157    i.Fusion_Algorithm  sensor_fusion.o
    0x080032e8   0x080032e8   0x00000040   Code   RO         1344    i.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x08003328   0x08003328   0x00000116   Code   RO         1347    i.GPIO_Init         stm32f10x_gpio.o
    0x0800343e   0x0800343e   0x00000002   PAD
    0x08003440   0x08003440   0x00000090   Code   RO         1349    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x080034d0   0x080034d0   0x00000012   Code   RO         1351    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x080034e2   0x080034e2   0x00000004   Code   RO         1354    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x080034e6   0x080034e6   0x00000004   Code   RO         1355    i.GPIO_SetBits      stm32f10x_gpio.o
    0x080034ea   0x080034ea   0x0000000a   Code   RO         1358    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x080034f4   0x080034f4   0x00000004   Code   RO         3199    i.HardFault_Handler  stm32f10x_it.o
    0x080034f8   0x080034f8   0x00000054   Code   RO         3472    i.I2C_RecvByte      iic.o
    0x0800354c   0x0800354c   0x00000038   Code   RO         3473    i.I2C_SendACK       iic.o
    0x08003584   0x08003584   0x00000050   Code   RO         3474    i.I2C_SendByte      iic.o
    0x080035d4   0x080035d4   0x00000060   Code   RO         3475    i.I2C_Start         iic.o
    0x08003634   0x08003634   0x00000030   Code   RO         3476    i.I2C_Stop          iic.o
    0x08003664   0x08003664   0x00000060   Code   RO         3477    i.I2C_WaitAck       iic.o
    0x080036c4   0x080036c4   0x0000002c   Code   RO         3478    i.IIC_Init          iic.o
    0x080036f0   0x080036f0   0x0000001c   Code   RO         3994    i.IMU_Calibrate_AccGyro  usart.o
    0x0800370c   0x0800370c   0x0000001c   Code   RO         3995    i.IMU_Calibrate_Mag  usart.o
    0x08003728   0x08003728   0x00000028   Code   RO         3997    i.IMU_Send_Command  usart.o
    0x08003750   0x08003750   0x0000007c   Code   RO         3998    i.IMU_Set_Output_Frequency  usart.o
    0x080037cc   0x080037cc   0x00000040   Code   RO         3999    i.IMU_Set_Output_Mode  usart.o
    0x0800380c   0x0800380c   0x000000b4   Code   RO         4000    i.IMU_Usart_Init    usart.o
    0x080038c0   0x080038c0   0x00000016   Code   RO         3543    i.KalmanFilter_Init  ir_sensor.o
    0x080038d6   0x080038d6   0x0000009c   Code   RO         3544    i.KalmanFilter_Update  ir_sensor.o
    0x08003972   0x08003972   0x0000005a   Code   RO         4158    i.Kalman_Filter_Update  sensor_fusion.o
    0x080039cc   0x080039cc   0x00000070   Code   RO         3610    i.Key_Init          key.o
    0x08003a3c   0x08003a3c   0x00000004   Code   RO         3200    i.MemManage_Handler  stm32f10x_it.o
    0x08003a40   0x08003a40   0x00000230   Code   RO         3385    i.Motor_Control_Task  contorl.o
    0x08003c70   0x08003c70   0x00000574   Code   RO         3625    i.Motor_Direction_Angle  motor.o
    0x080041e4   0x080041e4   0x00000028   Code   RO         3626    i.Motor_GetStatus   motor.o
    0x0800420c   0x0800420c   0x00000044   Code   RO         3627    i.Motor_Init        motor.o
    0x08004250   0x08004250   0x00000020   Code   RO         3628    i.Motor_IsRunning   motor.o
    0x08004270   0x08004270   0x000000f0   Code   RO         3629    i.Motor_One         motor.o
    0x08004360   0x08004360   0x00000024   Code   RO         3630    i.Motor_Release     motor.o
    0x08004384   0x08004384   0x0000001c   Code   RO         3632    i.Motor_Stop        motor.o
    0x080043a0   0x080043a0   0x000001f0   Code   RO         3633    i.Motor_Test        motor.o
    0x08004590   0x08004590   0x00000178   Code   RO         3634    i.Motor_one_two     motor.o
    0x08004708   0x08004708   0x000000f0   Code   RO         3635    i.Motor_two         motor.o
    0x080047f8   0x080047f8   0x00000002   Code   RO         3201    i.NMI_Handler       stm32f10x_it.o
    0x080047fa   0x080047fa   0x00000002   PAD
    0x080047fc   0x080047fc   0x00000070   Code   RO          137    i.NVIC_Init         misc.o
    0x0800486c   0x0800486c   0x00000014   Code   RO          138    i.NVIC_PriorityGroupConfig  misc.o
    0x08004880   0x08004880   0x00000028   Code   RO         3712    i.OLED_Clear        oled.o
    0x080048a8   0x080048a8   0x00000090   Code   RO         3713    i.OLED_ClearArea    oled.o
    0x08004938   0x08004938   0x00000060   Code   RO         3721    i.OLED_GPIO_Init    oled.o
    0x08004998   0x08004998   0x0000003e   Code   RO         3723    i.OLED_I2C_SendByte  oled.o
    0x080049d6   0x080049d6   0x0000001c   Code   RO         3724    i.OLED_I2C_Start    oled.o
    0x080049f2   0x080049f2   0x00000016   Code   RO         3725    i.OLED_I2C_Stop     oled.o
    0x08004a08   0x08004a08   0x0000009a   Code   RO         3726    i.OLED_Init         oled.o
    0x08004aa2   0x08004aa2   0x00000014   Code   RO         3728    i.OLED_Pow          oled.o
    0x08004ab6   0x08004ab6   0x00000026   Code   RO         3732    i.OLED_SetCursor    oled.o
    0x08004adc   0x08004adc   0x00000054   Code   RO         3734    i.OLED_ShowChar     oled.o
    0x08004b30   0x08004b30   0x000000d2   Code   RO         3736    i.OLED_ShowFloatNum  oled.o
    0x08004c02   0x08004c02   0x00000002   PAD
    0x08004c04   0x08004c04   0x000000fc   Code   RO         3738    i.OLED_ShowImage    oled.o
    0x08004d00   0x08004d00   0x0000004c   Code   RO         3739    i.OLED_ShowNum      oled.o
    0x08004d4c   0x08004d4c   0x0000002e   Code   RO         3741    i.OLED_ShowString   oled.o
    0x08004d7a   0x08004d7a   0x00000002   PAD
    0x08004d7c   0x08004d7c   0x00000028   Code   RO         3742    i.OLED_Update       oled.o
    0x08004da4   0x08004da4   0x00000018   Code   RO         3744    i.OLED_W_SCL        oled.o
    0x08004dbc   0x08004dbc   0x00000018   Code   RO         3745    i.OLED_W_SDA        oled.o
    0x08004dd4   0x08004dd4   0x00000020   Code   RO         3746    i.OLED_WriteCommand  oled.o
    0x08004df4   0x08004df4   0x0000002e   Code   RO         3747    i.OLED_WriteData    oled.o
    0x08004e22   0x08004e22   0x00000002   PAD
    0x08004e24   0x08004e24   0x000001e4   Code   RO         4159    i.Parse_IMU_Data    sensor_fusion.o
    0x08005008   0x08005008   0x00000002   Code   RO         3202    i.PendSV_Handler    stm32f10x_it.o
    0x0800500a   0x0800500a   0x00000002   PAD
    0x0800500c   0x0800500c   0x00000410   Code   RO         3323    i.Process_Adaptive_Training  main.o
    0x0800541c   0x0800541c   0x00000040   Code   RO         4002    i.Process_IMU_Data  usart.o
    0x0800545c   0x0800545c   0x0000007c   Code   RO         3545    i.Process_VL53L0X_Data  ir_sensor.o
    0x080054d8   0x080054d8   0x00000020   Code   RO         1775    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x080054f8   0x080054f8   0x00000020   Code   RO         1777    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08005518   0x08005518   0x000000d4   Code   RO         1785    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080055ec   0x080055ec   0x00000002   Code   RO         3203    i.SVC_Handler       stm32f10x_it.o
    0x080055ee   0x080055ee   0x00000002   PAD
    0x080055f0   0x080055f0   0x00000044   Code   RO         4160    i.Sensor_Fusion_Init  sensor_fusion.o
    0x08005634   0x08005634   0x00000008   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x0800563c   0x0800563c   0x000000e0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x0800571c   0x0800571c   0x00000028   Code   RO          141    i.SysTick_CLKSourceConfig  misc.o
    0x08005744   0x08005744   0x00000002   Code   RO         3204    i.SysTick_Handler   stm32f10x_it.o
    0x08005746   0x08005746   0x00000002   PAD
    0x08005748   0x08005748   0x00000060   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x080057a8   0x080057a8   0x00000114   Code   RO         3205    i.USART1_IRQHandler  stm32f10x_it.o
    0x080058bc   0x080058bc   0x0000005c   Code   RO         3206    i.USART2_IRQHandler  stm32f10x_it.o
    0x08005918   0x08005918   0x0000001c   Code   RO         4004    i.USART2_Send_bytes  usart.o
    0x08005934   0x08005934   0x00000020   Code   RO         4005    i.USART2_send_byte  usart.o
    0x08005954   0x08005954   0x0000005c   Code   RO         3207    i.USART3_IRQHandler  stm32f10x_it.o
    0x080059b0   0x080059b0   0x00000020   Code   RO         4007    i.USART3_send_byte  usart.o
    0x080059d0   0x080059d0   0x0000001e   Code   RO         2957    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x080059ee   0x080059ee   0x00000018   Code   RO         2960    i.USART_Cmd         stm32f10x_usart.o
    0x08005a06   0x08005a06   0x0000001a   Code   RO         2963    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x08005a20   0x08005a20   0x00000054   Code   RO         2964    i.USART_GetITStatus  stm32f10x_usart.o
    0x08005a74   0x08005a74   0x0000004a   Code   RO         2966    i.USART_ITConfig    stm32f10x_usart.o
    0x08005abe   0x08005abe   0x00000002   PAD
    0x08005ac0   0x08005ac0   0x000000d8   Code   RO         2967    i.USART_Init        stm32f10x_usart.o
    0x08005b98   0x08005b98   0x0000000a   Code   RO         2974    i.USART_ReceiveData  stm32f10x_usart.o
    0x08005ba2   0x08005ba2   0x00000002   PAD
    0x08005ba4   0x08005ba4   0x000000d4   Code   RO         4161    i.Update_Fusion_Data  sensor_fusion.o
    0x08005c78   0x08005c78   0x00000004   Code   RO         3208    i.UsageFault_Handler  stm32f10x_it.o
    0x08005c7c   0x08005c7c   0x000000a4   Code   RO         4010    i.Usart_Int         usart.o
    0x08005d20   0x08005d20   0x00000030   Code   RO         3546    i.VL53L0X_Init      ir_sensor.o
    0x08005d50   0x08005d50   0x0000000e   Code   RO         3547    i.VL53L0X_ReadDistance  ir_sensor.o
    0x08005d5e   0x08005d5e   0x0000000a   Code   RO         3548    i.VL53L0X_TriggerMeasurement  ir_sensor.o
    0x08005d68   0x08005d68   0x00000028   Code   RO         4683    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08005d90   0x08005d90   0x00000026   Code   RO         4584    i.__ARM_fpclassifyf  m_ws.l(fpclassifyf.o)
    0x08005db6   0x08005db6   0x0000000e   Code   RO         4574    i.__mathlib_dbl_overflow  m_ws.l(dunder.o)
    0x08005dc4   0x08005dc4   0x00000010   Code   RO         4576    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08005dd4   0x08005dd4   0x00000006   Code   RO         4587    i.__mathlib_flt_infnan  m_ws.l(funder.o)
    0x08005dda   0x08005dda   0x00000004   Code   RO         4588    i.__mathlib_flt_infnan2  m_ws.l(funder.o)
    0x08005dde   0x08005dde   0x00000008   Code   RO         4589    i.__mathlib_flt_invalid  m_ws.l(funder.o)
    0x08005de6   0x08005de6   0x0000000a   Code   RO         4592    i.__mathlib_flt_underflow  m_ws.l(funder.o)
    0x08005df0   0x08005df0   0x00000010   Code   RO         4774    i.__mathlib_narrow  m_ws.l(narrow.o)
    0x08005e00   0x08005e00   0x000000bc   Code   RO         4775    i.__mathlib_tofloat  m_ws.l(narrow.o)
    0x08005ebc   0x08005ebc   0x00000016   Code   RO         4819    i.__support_ldexp   m_ws.l(ldexp.o)
    0x08005ed2   0x08005ed2   0x0000000e   Code   RO         4313    i._is_digit         c_w.l(__printf_wp.o)
    0x08005ee0   0x08005ee0   0x0000011c   Code   RO         4411    i.acosf             m_ws.l(acosf.o)
    0x08005ffc   0x08005ffc   0x0000025c   Code   RO         4427    i.atan2f            m_ws.l(atan2f.o)
    0x08006258   0x08006258   0x0000002a   Code   RO         4435    i.atof              m_ws.l(atof.o)
    0x08006282   0x08006282   0x00000016   Code   RO         3481    i.delay_1us         iic.o
    0x08006298   0x08006298   0x0000003c   Code   RO         3424    i.delay_init        delay.o
    0x080062d4   0x080062d4   0x0000003c   Code   RO         3425    i.delay_ms          delay.o
    0x08006310   0x08006310   0x00000058   Code   RO         4798    i.frexp             m_ws.l(frexp.o)
    0x08006368   0x08006368   0x00000074   Code   RO         4820    i.ldexp             m_ws.l(ldexp.o)
    0x080063dc   0x080063dc   0x00000688   Code   RO         3324    i.main              main.o
    0x08006a64   0x08006a64   0x0000005e   Code   RO         3549    i.requestRange      ir_sensor.o
    0x08006ac2   0x08006ac2   0x00000002   PAD
    0x08006ac4   0x08006ac4   0x00000094   Code   RO         4438    i.round             m_ws.l(round.o)
    0x08006b58   0x08006b58   0x0000002c   Code   RO         4449    i.sqrtf             m_ws.l(sqrtf.o)
    0x08006b84   0x08006b84   0x00000042   Code   RO         3550    i.takeRangeReading  ir_sensor.o
    0x08006bc6   0x08006bc6   0x00000002   PAD
    0x08006bc8   0x08006bc8   0x0000002c   Code   RO         4669    locale$$code        c_w.l(lc_numeric_c.o)
    0x08006bf4   0x08006bf4   0x0000002c   Code   RO         4756    locale$$code        c_w.l(lc_ctype_c.o)
    0x08006c20   0x08006c20   0x00000062   Code   RO         4348    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08006c82   0x08006c82   0x00000002   PAD
    0x08006c84   0x08006c84   0x00000150   Code   RO         4350    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x08006dd4   0x08006dd4   0x00000010   Code   RO         4749    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x08006de4   0x08006de4   0x00000018   Code   RO         4533    x$fpl$dcmpinf       fz_ws.l(dcmpi.o)
    0x08006dfc   0x08006dfc   0x00000078   Code   RO         4795    x$fpl$deqf          fz_ws.l(deqf.o)
    0x08006e74   0x08006e74   0x0000005a   Code   RO         4364    x$fpl$dfixu         fz_ws.l(dfixu.o)
    0x08006ece   0x08006ece   0x00000026   Code   RO         4368    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x08006ef4   0x08006ef4   0x00000078   Code   RO         4374    x$fpl$dleqf         fz_ws.l(dleqf.o)
    0x08006f6c   0x08006f6c   0x00000154   Code   RO         4376    x$fpl$dmul          fz_ws.l(dmul.o)
    0x080070c0   0x080070c0   0x0000009c   Code   RO         4535    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x0800715c   0x0800715c   0x0000000c   Code   RO         4537    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08007168   0x08007168   0x0000006c   Code   RO         4378    x$fpl$drleqf        fz_ws.l(drleqf.o)
    0x080071d4   0x080071d4   0x000000b4   Code   RO         4539    x$fpl$drnd          fz_ws.l(drnd.o)
    0x08007288   0x08007288   0x00000016   Code   RO         4351    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x0800729e   0x0800729e   0x00000002   PAD
    0x080072a0   0x080072a0   0x000001d4   Code   RO         4352    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08007474   0x08007474   0x00000056   Code   RO         4380    x$fpl$f2d           fz_ws.l(f2d.o)
    0x080074ca   0x080074ca   0x00000002   PAD
    0x080074cc   0x080074cc   0x000000c4   Code   RO         4382    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08007590   0x08007590   0x0000000c   Code   RO         4675    x$fpl$fcheck1       fz_ws.l(fcheck1.o)
    0x0800759c   0x0800759c   0x00000018   Code   RO         4543    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x080075b4   0x080075b4   0x00000184   Code   RO         4389    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08007738   0x08007738   0x0000003e   Code   RO         4392    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x08007776   0x08007776   0x00000002   PAD
    0x08007778   0x08007778   0x00000026   Code   RO         4396    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x0800779e   0x0800779e   0x00000002   PAD
    0x080077a0   0x080077a0   0x00000068   Code   RO         4402    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08007808   0x08007808   0x00000102   Code   RO         4404    x$fpl$fmul          fz_ws.l(fmul.o)
    0x0800790a   0x0800790a   0x0000008c   Code   RO         4545    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08007996   0x08007996   0x0000000a   Code   RO         4547    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x080079a0   0x080079a0   0x00000062   Code   RO         4406    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x08007a02   0x08007a02   0x00000014   Code   RO         4383    x$fpl$frsb          fz_ws.l(faddsub_clz.o)
    0x08007a16   0x08007a16   0x00000002   PAD
    0x08007a18   0x08007a18   0x00000110   Code   RO         4549    x$fpl$fsqrt         fz_ws.l(fsqrt.o)
    0x08007b28   0x08007b28   0x000000ea   Code   RO         4384    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08007c12   0x08007c12   0x00000006   Code   RO         4677    x$fpl$ieeestatus    fz_ws.l(istatus.o)
    0x08007c18   0x08007c18   0x00000004   Code   RO         4408    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08007c1c   0x08007c1c   0x00000064   Code   RO         4751    x$fpl$retnan        fz_ws.l(retnan.o)
    0x08007c80   0x08007c80   0x0000005c   Code   RO         4679    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x08007cdc   0x08007cdc   0x0000004c   Code   RO         4557    x$fpl$scalbnf       fz_ws.l(scalbnf.o)
    0x08007d28   0x08007d28   0x00000004   Code   RO         4681    x$fpl$scanf1        fz_ws.l(scanf1.o)
    0x08007d2c   0x08007d2c   0x00000008   Code   RO         4766    x$fpl$scanf2        fz_ws.l(scanf2.o)
    0x08007d34   0x08007d34   0x00000030   Code   RO         4772    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x08007d64   0x08007d64   0x00000000   Code   RO         4559    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08007d64   0x08007d64   0x00002146   Data   RO         4143    .constdata          oled_font.o
    0x08009eaa   0x08009eaa   0x00000011   Data   RO         4321    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08009ebb   0x08009ebb   0x00000001   PAD
    0x08009ebc   0x08009ebc   0x00000094   Data   RO         4642    .constdata          c_w.l(bigflt0.o)
    0x08009f50   0x08009f50   0x00000099   Data   RO         4016    .conststring        usart.o
    0x08009fe9   0x08009fe9   0x00000003   PAD
    0x08009fec   0x08009fec   0x0000008d   Data   RO         4228    .conststring        cloud_connect.o
    0x0800a079   0x0800a079   0x00000003   PAD
    0x0800a07c   0x0800a07c   0x00000020   Data   RO         4879    Region$$Table       anon$$obj.o
    0x0800a09c   0x0800a09c   0x00000008   Data   RO         4760    c$$dinf             fz_ws.l(fpconst.o)
    0x0800a0a4   0x0800a0a4   0x00000008   Data   RO         4763    c$$dmax             fz_ws.l(fpconst.o)
    0x0800a0ac   0x0800a0ac   0x0000001c   Data   RO         4668    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800a0c8   0x0800a0c8   0x00000110   Data   RO         4755    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800a1d8, Size: 0x00000db0, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800a1d8   0x00000014   Data   RW         1805    .data               stm32f10x_rcc.o
    0x20000014   0x0800a1ec   0x00000005   Data   RW         3209    .data               stm32f10x_it.o
    0x20000019   0x0800a1f1   0x00000003   PAD
    0x2000001c   0x0800a1f4   0x0000005b   Data   RW         3326    .data               main.o
    0x20000077   0x0800a24f   0x00000001   PAD
    0x20000078   0x0800a250   0x00000010   Data   RW         3386    .data               contorl.o
    0x20000088   0x0800a260   0x00000004   Data   RW         3427    .data               delay.o
    0x2000008c   0x0800a264   0x00000022   Data   RW         3551    .data               ir_sensor.o
    0x200000ae   0x0800a286   0x00000009   Data   RW         3636    .data               motor.o
    0x200000b7   0x0800a28f   0x00000001   PAD
    0x200000b8   0x0800a290   0x00000034   Data   RW         4017    .data               usart.o
    0x200000ec   0x0800a2c4   0x00000019   Data   RW         4163    .data               sensor_fusion.o
    0x20000105   0x0800a2dd   0x00000003   PAD
    0x20000108   0x0800a2e0   0x00000009   Data   RW         4229    .data               cloud_connect.o
    0x20000111   0x0800a2e9   0x00000003   PAD
    0x20000114   0x0800a2ec   0x00000004   Data   RW         4328    .data               c_w.l(strtok.o)
    0x20000118        -       0x00000400   Zero   RW         3749    .bss                oled.o
    0x20000518        -       0x00000096   Zero   RW         4015    .bss                usart.o
    0x200005ae   0x0800a2f0   0x00000002   PAD
    0x200005b0        -       0x0000004c   Zero   RW         4162    .bss                sensor_fusion.o
    0x200005fc        -       0x00000150   Zero   RW         4227    .bss                cloud_connect.o
    0x2000074c        -       0x00000060   Zero   RW         4688    .bss                c_w.l(libspace.o)
    0x200007ac   0x0800a2f0   0x00000004   PAD
    0x200007b0        -       0x00000200   Zero   RW           18    HEAP                startup_stm32f10x_md.o
    0x200009b0        -       0x00000400   Zero   RW           17    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1696        856        141          9        336       7482   cloud_connect.o
       588         38          0         16          0       2756   contorl.o
         0          0          0          0          0       4548   core_cm3.o
       120         12          0          4          0       1148   delay.o
       526         36          0          0          0       3936   iic.o
       534         26          0         34          0       6110   ir_sensor.o
       112          6          0          0          0        551   key.o
      3592        844          0         91          0       6761   main.o
       172         22          0          0          0     207289   misc.o
      2952        158          0          9          0       7859   motor.o
      1438         42          0          0       1024      11393   oled.o
         0          0       8518          0          0       1352   oled_font.o
      1506        188          0         25         76       7919   sensor_fusion.o
        64         26        236          0       1536        768   startup_stm32f10x_md.o
         0          0          0          0          0       1644   stm32f10x_adc.o
       200         18          0          0          0       4024   stm32f10x_exti.o
       522         10          0          0          0      13592   stm32f10x_gpio.o
      1478        346          0          5          0       9070   stm32f10x_it.o
       276         32          0         20          0      12878   stm32f10x_rcc.o
       464          6          0          0          0      11850   stm32f10x_usart.o
       328         28          0          0          0      24797   system_stm32f10x.o
       944         92        153         52        150       8757   usart.o

    ----------------------------------------------------------------------
     17538       <USER>       <GROUP>        276       3124     356484   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        26          0          6         11          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        22          0          0          0          0        100   _rserrno.o
        64          0          0          0          0         84   _sgetc.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      2152        136          0          0          0        960   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        30          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        38          0          0          0          0         68   llshl.o
       138          0          0          0          0         80   lludiv10.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
      1272         16          0          0          0        168   scanf_fp.o
       800         14          0          0          0        100   scanf_hexfp.o
       308         16          0          0          0        100   scanf_infnan.o
        20          0          0          0          0         68   strchr.o
       128          0          0          0          0         68   strcmpv7m.o
        32          0          0          0          0         80   strcspn.o
        62          0          0          0          0         76   strlen.o
        86          0          0          0          0         76   strncpy.o
        28          0          0          0          0         80   strspn.o
        36          0          0          0          0         80   strstr.o
       164         14          0          0          0        120   strtod.o
        12          6          0          4          0         68   strtok.o
        68          4          0          0          0         84   strtok_int.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0         92   d2f.o
       826         16          0          0          0        348   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
        24          0          0          0          0         68   dcmpi.o
       120          4          0          0          0         92   deqf.o
        90          4          0          0          0         92   dfixu.o
        38          0          0          0          0         68   dflt_clz.o
       120          4          0          0          0         92   dleqf.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       108          0          0          0          0         80   drleqf.o
       180          4          0          0          0         80   drnd.o
        86          4          0          0          0         84   f2d.o
       450          8          0          0          0        236   faddsub_clz.o
        12          4          0          0          0         68   fcheck1.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
        62          4          0          0          0         84   ffixu.o
        38          0          0          0          0         68   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
         0          0         16          0          0          0   fpconst.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
       272        100          0          0          0         88   fsqrt.o
         6          0          0          0          0         68   istatus.o
         4          0          0          0          0         68   printf1.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        76          0          0          0          0         68   scalbnf.o
         4          0          0          0          0         68   scanf1.o
         8          0          0          0          0         84   scanf2.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
       284         42          0          0          0        120   acosf.o
       604         76          0          0          0        116   atan2f.o
        42          0          0          0          0         84   atof.o
        30          6          0          0          0        136   dunder.o
        40          0          0          0          0         68   fpclassify.o
        38          0          0          0          0         68   fpclassifyf.o
        88          8          0          0          0         76   frexp.o
        28          0          0          0          0        272   funder.o
       138          0          0          0          0        160   ldexp.o
       204          4          0          0          0        168   narrow.o
       148         12          0          0          0         88   round.o
        44          0          0          0          0         80   sqrtf.o

    ----------------------------------------------------------------------
     14326        <USER>        <GROUP>          4        100       9292   Library Totals
        28          0          1          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      8202        290        465          4         96       4772   c_w.l
      4408        264         16          0          0       3084   fz_ws.l
      1688        148          0          0          0       1436   m_ws.l

    ----------------------------------------------------------------------
     14326        <USER>        <GROUP>          4        100       9292   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     31864       3488       9568        280       3224     354176   Grand Totals
     31864       3488       9568        280       3224     354176   ELF Image Totals
     31864       3488       9568        280          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                41432 (  40.46kB)
    Total RW  Size (RW Data + ZI Data)              3504 (   3.42kB)
    Total ROM Size (Code + RO Data + RW Data)      41712 (  40.73kB)

==============================================================================

