#ifndef __CLOUD_CONNECT_H
#define __CLOUD_CONNECT_H

#include "stm32f10x.h"
#include "sensor_fusion.h"

// 缓冲区大小定义
#define RX_BUFFER_SIZE  256

// WiFi配置
#define WIFI_SSID       "1"  // WiFi名
#define WIFI_PWD        "1234567h"  // WiFi密码

// 华为云IoT平台配置
#define HUAWEI_MQTT_USERNAME   "6839578232771f177b3cffbf_butai"  // Username
#define HUAWEI_MQTT_PASSWORD   "1234567h"               // Password
#define HUAWEI_MQTT_CLIENTID   "6839578232771f177b3cffbf_butai_0_0_2025060501"                                         // ClientID
#define HUAWEI_MQTT_ADDRESS    "ced607f00d.st1.iotda-device.cn-north-4.myhuaweicloud.com"  // 平台地址
#define HUAWEI_MQTT_PORT       "1883"                                       // 端口号(改为非加密端口)
#define HUAWEI_MQTT_DEVICEID   "6839578232771f177b3cffbf_butai"             // 设备ID
#define HUAWEI_MQTT_SERVICEID  "STM32"                                      // 服务ID
#define HUAWEI_MQTT_PROTOCOL   "MQTTS"                                      // 协议

// 云连接状态码
typedef enum {
    CLOUD_OK = 0,
    CLOUD_ERROR,
    CLOUD_TIMEOUT,
    CLOUD_NOT_CONNECTED
} Cloud_Status_t;

// 命令回调函数类型
typedef void (*Cloud_CommandCallback_t)(const char* commandName, const char* payload);

// 云连接状态信息
typedef struct {
    uint8_t wifi_connected;       // Wi-Fi连接状态
    uint8_t cloud_connected;      // 云平台连接状态
    uint8_t data_ready;           // 数据就绪标志
    IMU_Data_t imu_data;          // IMU数据
    Fusion_Data_t fusion_data;    // 融合数据
} Cloud_Status_Info_t;

// 全局变量声明
extern uint8_t ESP8266_RxBuffer[RX_BUFFER_SIZE];
extern uint16_t ESP8266_RxCounter;
extern uint8_t ESP8266_DataReceived;

// 函数声明
Cloud_Status_t Cloud_Init(void);
Cloud_Status_t Cloud_Connect(void);
Cloud_Status_t Cloud_ProcessIMUData(IMU_Data_t *imuData, Fusion_Data_t *fusionData);
Cloud_Status_t Cloud_UploadData(void);
Cloud_Status_t Cloud_UploadProperty(const char* property_name, float property_value);  // 新增：上传单个属性
void Cloud_SetCommandCallback(Cloud_CommandCallback_t callback);
void Cloud_Process(void);
Cloud_Status_Info_t* Cloud_GetStatus(void);

#endif /* __CLOUD_CONNECT_H */
