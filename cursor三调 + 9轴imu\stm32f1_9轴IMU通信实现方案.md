# STM32F1与9轴IMU通信实现方案

## 项目概述

本文档介绍如何实现STM32F1系列微控制器与9轴IMU（惯性测量单元）之间的通信，实现姿态解算和数据融合。9轴IMU由三轴加速度计、三轴陀螺仪和三轴磁力计组成，能够实现物体在三维空间中的姿态测量。

### 系统架构

系统主要包含以下部分：
1. STM32F1系列微控制器（如STM32F103）
2. 九轴IMU传感器模块
3. 通信接口（IIC/UART）
4. 数据处理和姿态解算算法

## 硬件连接

### IMU与STM32F1连接方式

当前项目通过UART串口通信方式连接IMU与STM32F1。根据代码分析，采用了以下连接方式：

1. IMU模块通过UART3与STM32F1通信
   - STM32F1的PB13（TX）连接到IMU的RX
   - STM32F1的PB14（RX）连接到IMU的TX
   - 通信波特率：115200

2. 供电连接
   - VCC：3.3V
   - GND：地线

### IIC总线接口（备选方案）

项目中也保留了IIC通信的代码，可以用于与其他传感器通信：
   - SCL：PB6
   - SDA：PB7
   - 供电：3.3V

## 通信协议

### IMU通信协议

IMU模块使用基于命令行的通信协议，通过USART3发送命令和接收数据。

#### 命令格式：
```
cmd [命令] [参数]
```

#### 支持的命令：
1. 设置输出模式
   ```
   cmd output euler          // 输出欧拉角
   cmd output quaternion     // 输出四元数
   cmd output earth_a        // 输出世界坐标系加速度
   cmd output acc            // 输出原始加速度
   cmd output gyro           // 输出原始陀螺仪
   cmd output mag            // 输出原始磁力计
   cmd output acc_gyro_mag   // 输出所有数据
   ```

2. 校准命令
   ```
   cmd cali a+g              // 校准加速度计和陀螺仪
   cmd cali mag              // 校准磁力计
   ```

3. 恢复默认设置
   ```
   cmd restore               // 恢复默认设置
   ```

4. 设置输出频率
   ```
   cmd freq 10               // 设置输出频率为10Hz
   ```

### 数据格式

IMU模块输出的数据格式根据不同的模式有所不同：

1. 欧拉角模式：`euler:roll,pitch,yaw`
2. 四元数模式：`quaternion:q0,q1,q2,q3`
3. 世界坐标系加速度：`earth_a:ax,ay,az`
4. 原始加速度：`acc:ax,ay,az`
5. 原始陀螺仪：`gyro:gx,gy,gz`
6. 原始磁力计：`mag:mx,my,mz`
7. 九轴原始数据：`acc_gyro_mag:ax,ay,az,gx,gy,gz,mx,my,mz`

## 软件实现

### 通信初始化

```c
void IMU_Usart_Init(uint32_t BaudRatePrescaler)
{
    GPIO_InitTypeDef GPIO_usartx;
    USART_InitTypeDef Usart_X;
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 使能USART3、GPIOB和AFIO时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB | RCC_APB2Periph_AFIO, ENABLE);
    
    // 设置USART3重映射，使用PB13/PB14
    GPIO_PinRemapConfig(GPIO_PartialRemap_USART3, ENABLE);
    
    // USART3_TX   PB13
    GPIO_usartx.GPIO_Pin = GPIO_Pin_13;
    GPIO_usartx.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_usartx.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(GPIOB, &GPIO_usartx);
    
    // USART3_RX   PB14
    GPIO_usartx.GPIO_Pin = GPIO_Pin_14;
    GPIO_usartx.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(GPIOB, &GPIO_usartx);
    
    // USART3参数配置
    Usart_X.USART_BaudRate = BaudRatePrescaler;
    Usart_X.USART_WordLength = USART_WordLength_8b;
    Usart_X.USART_StopBits = USART_StopBits_1;
    Usart_X.USART_Parity = USART_Parity_No;
    Usart_X.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    Usart_X.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_Init(USART3, &Usart_X);
    
    // 配置USART3中断
    NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 使能USART3接收中断
    USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);
    
    // 使能USART3
    USART_Cmd(USART3, ENABLE);
}
```

### 数据接收中断处理

```c
void USART3_IRQHandler(void)
{
    extern uint8_t imu_Buf_Data[100];  // 接收缓冲区
    extern uint8_t imu_Receive_ok;     // 接收完成标志
    extern uint16_t imu_rx_len;        // 接收数据长度
    
    if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)  // 接收中断
    {
        uint8_t receivedByte = USART_ReceiveData(USART3);  // 读取接收到的数据
        
        if(imu_rx_len < sizeof(imu_Buf_Data) - 1) // 预留一个字节给\0
        {
            imu_Buf_Data[imu_rx_len++] = receivedByte;
            
            // 检查是否接收到换行符，表示一条数据接收完成
            if(receivedByte == '\n')
            {
                // 添加字符串结束符
                imu_Buf_Data[imu_rx_len] = '\0';
                imu_Receive_ok = 1;  // 接收完成标志
            }
        }
        else
        {
            // 缓冲区已满，重置
            imu_rx_len = 0;
        }
    }
}
```

### 发送命令函数

```c
void IMU_Send_Command(const char* command)
{
    uint8_t i = 0;
    // 发送命令
    while(command[i] != '\0')
    {
        USART3_send_byte(command[i++]);
    }
    
    // 添加回车换行
    USART3_send_byte('\r');
    USART3_send_byte('\n');
}

// 设置输出模式
void IMU_Set_Output_Mode(const char* mode)
{
    // 发送命令：cmd output xxx
    USART3_send_byte('c');
    USART3_send_byte('m');
    USART3_send_byte('d');
    USART3_send_byte(' ');
    USART3_send_byte('o');
    USART3_send_byte('u');
    USART3_send_byte('t');
    USART3_send_byte('p');
    USART3_send_byte('u');
    USART3_send_byte('t');
    USART3_send_byte(' ');
    
    // 发送模式参数
    uint8_t i = 0;
    while(mode[i] != '\0')
    {
        USART3_send_byte(mode[i++]);
    }
    
    // 添加回车换行
    USART3_send_byte('\r');
    USART3_send_byte('\n');
}
```

### 数据处理和解析

```c
void Process_IMU_Data(void)
{
    if(imu_Receive_ok)
    {
        imu_Receive_ok = 0; // 清除接收标志
        
        // 确保字符串以NULL结尾
        if(imu_rx_len < sizeof(imu_Buf_Data))
        {
            imu_Buf_Data[imu_rx_len] = '\0';
            
            // 解析IMU数据
            Parse_IMU_Data((char*)imu_Buf_Data, &imu_data);
        }
        
        // 重置接收计数，准备接收下一条数据
        imu_rx_len = 0;
    }
}

// 解析IMU数据
void Parse_IMU_Data(char* data_str, IMU_Data_t* imu_data)
{
    char *token;
    float values[9] = {0.0f};
    int index = 0;
    
    // 清除尾部换行符或回车符
    char* newline = strchr(data_str, '\r');
    if(newline) *newline = '\0';
    newline = strchr(data_str, '\n');
    if(newline) *newline = '\0';
    
    // 解析欧拉角数据
    if(strstr(data_str, "euler:") == data_str)
    {
        // 欧拉角数据: "euler:roll,pitch,yaw"
        token = strtok(data_str + 6, ",");
        
        // 解析欧拉角数据
        while (token != NULL && index < 3)
        {
            values[index++] = atof(token);
            token = strtok(NULL, ",");
        }
        
        // 检查是否解析了所有3个欧拉角
        if(index == 3)
        {
            // 直接使用IMU提供的欧拉角数据
            imu_data->roll = values[0];
            imu_data->pitch = values[1];
            imu_data->yaw = values[2];
            imu_data->valid = 1;
        }
        else
        {
            imu_data->valid = 0;
        }
    }
    // 解析其他数据格式...
}
```

### IMU初始化和校准流程

在主程序中，IMU的初始化和校准流程如下：

```c
// 初始化IMU
IMU_Usart_Init(115200);
delay_ms(200);

// 设置IMU输出模式为欧拉角
IMU_Set_Output_Mode(IMU_OUTPUT_EULER);
IMU_Set_Output_Frequency(10);
delay_ms(50);

// 校准IMU
OLED_Clear();
OLED_ShowString(0, 16, "IMU Initializing", 6);
OLED_Update();

// 校准加速度计和陀螺仪
IMU_Calibrate_AccGyro();
delay_ms(500);

// 校准磁力计
OLED_Clear();
OLED_ShowString(0, 16, "Calibrating Mag", 6);
OLED_ShowString(0, 32, "Rotate device", 6);
OLED_Update();
IMU_Calibrate_Mag();
delay_ms(2000);
```

## 数据融合算法

项目采用卡尔曼滤波作为传感器数据融合算法，将IMU数据和IR传感器数据进行融合，获得更加准确的姿态信息。

### 卡尔曼滤波器实现

```c
// 卡尔曼滤波器更新
float Kalman_Filter_Update(Kalman_Filter_t* filter, float measurement)
{
    // 预测步骤
    // x = A*x + B*u (这里A=1, u=0, 即状态不变)
    // p = A*p*A' + Q (这里A=1, 即p = p + Q)
    filter->p = filter->p + filter->q;
    
    // 更新步骤
    // k = p*H' / (H*p*H' + R) (这里H=1, 即k = p / (p + R))
    filter->k = filter->p / (filter->p + filter->r);
    
    // x = x + K*(z - H*x) (这里H=1, 即x = x + K*(z - x))
    filter->x = filter->x + filter->k * (measurement - filter->x);
    
    // p = (1 - K*H)*p (这里H=1, 即p = (1 - K)*p)
    filter->p = (1.0f - filter->k) * filter->p;
    
    return filter->x;
}
```

### 传感器数据融合算法

```c
// 传感器数据融合算法
float Fusion_Algorithm(float ir_angle, float imu_angle)
{
    float fused_angle;
    static uint8_t init_flag = 0;
    
    // 根据IMU数据的可靠性动态调整权重
    if(imu_data.valid)
    {
        // 卡尔曼滤波更新
        angle_filter.r = KALMAN_R_IMU;  // 设置IMU噪声系数
        fused_angle = Kalman_Filter_Update(&angle_filter, imu_angle);
    }
    else
    {
        // 使用IR传感器数据
        angle_filter.r = KALMAN_R_IR;  // 设置IR噪声系数
        fused_angle = Kalman_Filter_Update(&angle_filter, ir_angle);
    }
    
    return fused_angle;
}
```

## 完整流程与工作模式

### 工作模式

系统支持三种工作模式：
1. 空闲模式（SYSTEM_IDLE）：等待用户按键启动
2. 运行模式（SYSTEM_RUNNING）：正常工作模式，读取传感器数据并进行融合
3. 调试模式（SYSTEM_DEBUG）：用于调试和测试

### 主循环

主程序的主循环包含以下功能：
1. 检查系统状态（空闲/运行/调试）
2. 读取IMU和IR传感器数据
3. 调用数据融合算法
4. 将处理后的数据显示在OLED上
5. 响应用户按键输入

```c
while(1)
{
    // 检查调试模式
    if(systemState == SYSTEM_DEBUG)
    {
        Debug_Mode();
        continue;
    }
    
    // 启动检查
    if(system_start_flag && systemState == SYSTEM_IDLE)
    {
        systemState = SYSTEM_RUNNING;
        OLED_Clear();
        OLED_ShowString(0, 16, "Starting...", 6);
        OLED_Update();
        delay_ms(50);
        system_start_flag = 0;
    }
    
    // 系统运行状态
    if(systemState == SYSTEM_RUNNING)
    {
        // 读取传感器数据
        uint8_t read_success = Process_VL53L0X_Data(&sensor_data);
        Process_IMU_Data();
        
        if(read_success)
        {
            ir_distance = sensor_data.distance;
            Update_Fusion_Data(&sensor_data, &imu_data, &fusion_data);
            joint_angle = fusion_data.joint_angle;
            TrainingMode_t mode = fusion_data.mode;
            
            // 更新显示...
        }
        
        delay_ms(20); // 循环延时
    }
}
```

## 注意事项与优化建议

1. **通信可靠性**
   - 确保串口通信波特率正确（115200）
   - 处理通信超时和数据包丢失的情况

2. **校准流程**
   - 初次使用需进行加速度计、陀螺仪和磁力计的校准
   - 磁力计校准时，需360°旋转设备收集不同方向的数据

3. **数据融合优化**
   - 调整卡尔曼滤波参数以平衡响应速度和稳定性
   - IMU数据和IR传感器数据权重可根据实际应用调整

4. **功耗优化**
   - 可通过调整数据更新频率降低功耗
   - 在不需要高精度时可降低采样率

5. **抗干扰能力**
   - 磁力计易受外部磁场干扰，建议增加磁干扰检测算法
   - 加速度计在振动环境下数据可能不准确，可增加振动检测和补偿

## 结论

通过UART接口与9轴IMU通信，可以获取精确的姿态数据。系统采用卡尔曼滤波算法融合多传感器数据，提高了姿态解算的精度和可靠性。用户通过命令行接口可以方便地配置IMU模块的工作模式和参数，满足不同应用场景的需求。 