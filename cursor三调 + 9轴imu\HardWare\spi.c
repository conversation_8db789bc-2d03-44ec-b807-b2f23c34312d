#include "spi.h"
/*
input:BaudRatePrescaler
#define SPI_BaudRatePrescaler_2         ((uint16_t)0x0000)
#define SPI_BaudRatePrescaler_4         ((uint16_t)0x0008)
#define SPI_BaudRatePrescaler_8         ((uint16_t)0x0010)
#define SPI_BaudRatePrescaler_16        ((uint16_t)0x0018)
#define SPI_BaudRatePrescaler_32        ((uint16_t)0x0020)
#define SPI_BaudRatePrescaler_64        ((uint16_t)0x0028)
#define SPI_BaudRatePrescaler_128       ((uint16_t)0x0030)
#define SPI_BaudRatePrescaler_256       ((uint16_t)0x0038)
*/
void Spi1_Int(uint16_t BaudRatePrescaler)
{
	GPIO_InitTypeDef GPIO_spix;
	SPI_InitTypeDef  SPI_X;
	
	// 修改为使用SPI3和GPIOC引脚，避免与电机控制引脚冲突
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_SPI3, ENABLE);
	
	// PC10-SCK, PC11-MISO, PC12-MOSI
	GPIO_spix.GPIO_Pin = GPIO_Pin_10 | GPIO_Pin_11 | GPIO_Pin_12;
	GPIO_spix.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_spix.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOC, &GPIO_spix);
	GPIO_SetBits(GPIOC, GPIO_Pin_10 | GPIO_Pin_11 | GPIO_Pin_12);
	
	// PC8, PC9作为NSS
	GPIO_spix.GPIO_Pin = GPIO_Pin_8 | GPIO_Pin_9;
	GPIO_spix.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_spix.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOC, &GPIO_spix);
	GPIO_SetBits(GPIOC, GPIO_Pin_8 | GPIO_Pin_9);
	
	SPI_X.SPI_Direction = SPI_Direction_2Lines_FullDuplex; // 双线双向全双工
	SPI_X.SPI_Mode = SPI_Mode_Master; // 主模式
	SPI_X.SPI_DataSize = SPI_DataSize_8b; // 数据位大小8位
	SPI_X.SPI_CPOL = SPI_CPOL_High; // 时钟极性，空闲时时钟为高电平
	SPI_X.SPI_CPHA = SPI_CPHA_2Edge; // 时钟相位，在第二个边沿采集数据
	SPI_X.SPI_NSS = SPI_NSS_Soft; // NSS信号由软件控制
	SPI_X.SPI_BaudRatePrescaler = BaudRatePrescaler; // 256分频，传输速率（RCC_APB1=36M，36M/256=140.625KHz）
	SPI_X.SPI_FirstBit = SPI_FirstBit_MSB; // 数据高位在前
	SPI_X.SPI_CRCPolynomial = 7; // CRC校验值
	SPI_Init(SPI3, &SPI_X);
	SPI_Cmd(SPI3, ENABLE);
}

// spi读写底层函数
// spi写过程：填充缓冲寄存器数据--清位标志寄存器
// spi读过程：清位标志寄存器数据--接收缓冲寄存器
// TxData：待发送数据
u8 Spi_RW(u8 TxData)
{		
	// 当发送缓冲寄存器为空时，置1，否则等待
	while (SPI_I2S_GetFlagStatus(SPI3, SPI_I2S_FLAG_TXE) == RESET);
				  
	SPI_I2S_SendData(SPI3, TxData); // 发送一个数据到发送缓冲寄存器

	// 当接收缓冲寄存器是空时，置1，否则等待
	while (SPI_I2S_GetFlagStatus(SPI3, SPI_I2S_FLAG_RXNE) == RESET);
	 						    
	return SPI_I2S_ReceiveData(SPI3); // 从接收缓冲寄存器取一个数据				    
}

// 写数据
/*
add:寄存器地址
*pBuf：写入数据缓冲
num：写入数据个数
*/
void Spi_write_buf(uint8_t add, uint8_t *pBuf, uint8_t num, uint16_t GPIO_Pin_X)
{
	uint8_t i=0;
	GPIO_ResetBits(GPIOC, GPIO_Pin_X); // NSS拉低选择器件
	Spi_RW(add);
	while(i<num)
	Spi_RW(pBuf[i++]);
	GPIO_SetBits(GPIOC, GPIO_Pin_X); // NSS拉高释放器件
}

// 读数据
/*
add:寄存器地址
*pBuf：读取数据
num：读取的数据个数
*/
void Spi_read_buf(uint8_t add, uint8_t *pBuf, uint8_t num, uint16_t GPIO_Pin_X)
{
	uint8_t i=0;
	GPIO_ResetBits(GPIOC, GPIO_Pin_X); // NSS拉低选择器件
	Spi_RW(add);
	while(i<num)
	pBuf[i++]=Spi_RW(0);
	GPIO_SetBits(GPIOC, GPIO_Pin_X); // NSS拉高释放器件
}
