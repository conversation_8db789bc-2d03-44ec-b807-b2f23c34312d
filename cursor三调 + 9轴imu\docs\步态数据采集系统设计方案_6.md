## 6. 性能指标详细解读

本节将对系统设定的各项关键性能指标进行详细解读，分析其重要性、实现难度以及如何通过设计和测试来达成目标值。

### 6.1 测量性能

#### 6.1.1 关节角度测量精度 (±1°目标, ±0.5°最优)

*   **重要性**：关节角度测量的精度是系统功能的核心，直接影响步态分析的准确性和反馈的有效性。在医疗康复和运动科学领域，高精度数据至关重要。
*   **实现难度**：挑战在于传感器自身的精度限制（VL53L0X受目标表面、环境光影响，IMU受运动加速度和磁场干扰）、传感器在肢体上的固定稳定性、以及距离到角度转换模型的精确性。关节轴的瞬时偏移也会引入误差。
*   **达成策略**：
    1.  **传感器选型与优化配置**：选用高精度的VL53L0X和IMU，并在软件中配置其高精度工作模式。
    2.  **精确标定**：通过多点标定建立准确的距离-角度映射关系，并考虑建立补偿模型。
    3.  **多传感器融合**：利用IMU数据修正VL53L0X的测量误差，使用卡尔曼滤波等高级融合算法（如果资源允许）。
    4.  **结构设计**：设计稳固的佩戴结构，最小化传感器相对于关节轴的位移和晃动。
    5.  **软件滤波**：在数据融合后应用适当的数字滤波，进一步平滑数据。
*   **测试验证**：在标准的关节角度仪或专业的运动捕捉系统辅助下，测量系统在不同角度、不同运动速度下的静态和动态精度，计算误差均值和标准差。

#### 6.1.2 角度测量范围 (0-180°目标, 0-135°最低)

*   **重要性**：覆盖主要关节（如膝盖、踝关节）的全部或大部分活动范围，确保在深蹲、跳跃等动作下也能有效测量。
*   **实现难度**：主要取决于VL53L0X传感器的有效测量范围和安装位置。当关节角度接近0°或180°时，传感器与目标点的距离变化可能变得不敏感或超出量程。
*   **达成策略**：
    1.  **传感器安装位置优化**：仔细选择VL53L0X的安装点和朝向，使其在关节的整个活动范围内都能有效测量到目标点。
    2.  **双传感器配置**：采用双VL53L0X传感器，从不同角度测量，扩大有效测量范围。
    3.  **备用算法**：在VL53L0X测量失效的极限角度范围，可以纯粹依赖IMU数据进行角度估计作为补充。
*   **测试验证**：让用户进行全范围的关节活动，记录系统测量的角度范围，并与真实范围对比。

#### 6.1.3 数据采集频率 (≥100Hz最低, ≥200Hz目标)

*   **重要性**：足够高的数据采集频率是捕捉快速运动和步态细节的基础，也是实现低延迟实时反馈的前提。
*   **实现难度**：受限于传感器自身的最高采样率、MCU读取传感器数据的速度（I2C/SPI总线速度）、以及MCU处理数据的能力。VL53L0X采样率约50Hz，MPU9250可达1000Hz。
*   **达成策略**：
    1.  **传感器配置**：将VL53L0X和MPU9250配置到最高可用采样率。
    2.  **接口优化**：选用速度更快的接口（如SPI优于I2C，对于MPU9250）。
    3.  **中断驱动**：利用传感器的数据就绪中断来触发数据读取，而不是轮询。
    4.  **代码优化**：优化传感器读取和数据处理的代码，减少单次处理时间。
    5.  **任务调度**：合理安排高优先级任务（数据采集和关键处理）的执行时机。
*   **测试验证**：测量系统主循环中传感器数据读取和处理的周期时间，或使用示波器监测数据就绪信号到处理完成的时间。

#### 6.1.4 角速度/加速度测量范围 (±2000°/s, ±16g)

*   **重要性**：覆盖人体关节在快速运动和冲击（如跑步、跳跃）中可能产生的最大角速度和加速度，避免数据饱和。
*   **实现难度**：取决于IMU传感器的量程配置。MPU9250支持多档量程。
*   **达成策略**：在IMU初始化时，将陀螺仪量程配置为±2000°/s，加速度计量程配置为±8g或±16g（根据实际运动强度选择）。
*   **测试验证**：在用户进行剧烈运动时，监测IMU输出数据是否达到量程上限。

### 6.2 实时性

#### 6.2.1 端到端系统延迟 (<50ms目标, <35ms最优)

*   **重要性**：低延迟是实时反馈系统的生命线。延迟过高会导致反馈信息与用户实际动作不同步，降低反馈效果甚至产生误导。
*   **实现难度**：延迟累积在数据采集、处理、算法执行和反馈输出的整个流程中。影响因素包括传感器采样率、通信速度、MCU主频、算法复杂度、代码效率。
*   **达成策略**：
    1.  **高采样率**：提高数据采集频率。
    2.  **高效算法**：选择计算量小的算法（如互补滤波优先于EKF）。
    3.  **代码优化**：精简代码，减少不必要的计算和数据拷贝。
    4.  **任务优先级**：确保数据处理和反馈生成任务具有高优先级。
    5.  **中断驱动**：关键任务由中断触发，保证及时响应。
    6.  **硬件加速**：利用MCU的硬件加速功能（如FPU）进行浮点运算（如果STM32F1有硬件FPU）。
*   **测试验证**：使用外部设备（如高速相机或示波器）测量从特定动作发生到系统反馈输出（LED变化、电机振动）的时间间隔。

### 6.3 功耗

#### 6.3.1 连续工作时间 (≥8小时最低, >10小时目标)

*   **重要性**：作为可穿戴设备，电池续航能力直接影响用户的使用体验和便利性。长时间工作是重要的指标。
*   **实现难度**：所有活动元器件（MCU、传感器、显示屏、反馈执行器）都会消耗电能。提高采样率、增加计算量、增强反馈强度都会增加功耗。
*   **达成策略**：
    1.  **元器件选型**：选用低功耗型号的MCU、传感器和外设。
    2.  **电源管理**：合理设计电源转换电路，提高效率。利用电源管理芯片控制充放电。
    3.  **低功耗模式**：在系统空闲、用户静止或屏幕关闭时，让MCU和外设进入低功耗模式（睡眠、停止）。
    4.  **动态功耗调整**：根据系统负载和电量状态，动态调整MCU主频、传感器采样率、显示屏刷新率等。
    5.  **反馈执行优化**：优化LED和电机的工作模式，非必要时不驱动。
    6.  **软件效率**：提高代码执行效率，减少CPU活跃时间。
*   **测试验证**：使用高精度电流表监测系统在不同工作模式（运行、低功耗、待机）下的平均电流，并根据电池容量估算续航时间。进行实际连续运行测试。

#### 6.3.2 工作模式电流/低功耗模式电流

*   **重要性**：量化不同工作状态下的功耗水平，指导硬件和软件的低功耗设计。
*   **实现难度**：需要精确测量微安级别的电流。
*   **达成策略**：同上（连续工作时间）。
*   **测试验证**：使用专用的低功耗电流测量设备，在不同模式下测量系统的平均电流。

### 6.4 可靠性

#### 6.4.1 平均无故障工作时间 (MTBF >5000小时)

*   **重要性**：衡量系统长时间稳定运行的能力，尤其在医疗和运动场景，可靠性至关重要。
*   **实现难度**：受限于元器件的寿命、硬件设计（电路稳定性、热设计）、软件鲁棒性（错误处理、异常恢复）。
*   **达成策略**：
    1.  **元器件选型**：选用工业级或至少满足应用环境要求的元器件。
    2.  **硬件设计**：遵循严格的电子设计规则，保证电路稳定、信号完整性、热设计合理。
    3.  **软件鲁棒性**：实现全面的错误检测和异常处理机制，防止系统崩溃。
    4.  **严格测试**：进行长时间连续运行测试、高低温测试、湿度测试等环境应力测试。
    5.  **生产质量控制**：保证焊接和组装工艺的质量。
*   **测试验证**：进行加速寿命测试 (Accelerated Life Testing, ALT)，通过在更严苛的条件下运行来预测MTBF。或者进行长时间实际运行统计。

#### 6.4.2 工作温度范围 (-10℃~50℃)

*   **重要性**：保证设备在常见的室内外环境温度下正常工作。
*   **实现难度**：元器件的工作温度范围是主要限制。极端温度下可能导致传感器性能漂移、电池性能下降、液晶显示响应变慢等。
*   **达成策略**：
    1.  **元器件选型**：选用工作温度范围符合要求的元器件。
    2.  **热设计**：保证在高温环境下主要发热器件能有效散热。
    3.  **软件补偿**：对温度引起的传感器漂移进行软件补偿（如果需要）。
    4.  **外壳设计**：外壳材料和结构考虑温度变形。
*   **测试验证**：在高低温箱中测试系统在规定温度范围内的功能和性能。

#### 6.4.3 防护等级 (IP54)

*   **重要性**：防止灰尘和水溅进入设备内部，提高在运动和日常使用中的可靠性。
*   **实现难度**：需要通过结构设计、密封材料、接口防护来实现。
*   **达成策略**：
    1.  **外壳设计**：外壳采用密封设计，减少开孔。
    2.  **接口防护**：充电口、调试口等采用防水塞或防水连接器。
    3.  **按键防护**：按键处采用防水膜或防水按键。
    4.  **传感器窗口**：传感器窗口与外壳密封，采用防水透光材料。
*   **测试验证**：按照IP54标准进行防尘和防溅水测试。

#### 6.4.4 跌落测试 (1米)

*   **重要性**：保证设备在意外跌落时不易损坏，提高耐用性。
*   **实现难度**：取决于外壳材料、结构设计、内部元器件的固定方式。
*   **达成策略**：
    1.  **外壳材料**：选用抗冲击性能好的材料（如PC、ABS+PC合金）。
    2.  **结构设计**：外壳内部设计加强筋和缓冲结构；关键元器件（如电池、PCB）采用减震固定。
    3.  **PCB布局**：避免将重物或易碎元件放置在PCB边缘或应力集中区域。
*   **测试验证**：按照标准进行多角度、多次数的跌落测试，检查设备功能和外观是否受损。

### 6.5 物理特性

#### 6.5.1 主板尺寸/整机重量

*   **重要性**：影响设备的佩戴舒适性和隐蔽性，是可穿戴设备的关键指标。
*   **实现难度**：取决于元器件的选择、PCB布局紧凑度、电池尺寸、以及外壳设计。
*   **达成策略**：
    1.  **元器件选型**：选用小封装的SMD元器件。
    2.  **PCB设计**：采用多层板，优化布局和走线，提高空间利用率。
    3.  **电池选型**：选择能量密度高、尺寸匹配的锂聚合物电池。
    4.  **结构设计**：外壳设计紧凑，材料轻量化。
*   **测试验证**：实际测量PCB尺寸和整机重量。

### 6.6 用户体验指标

除了上述技术指标，还需要关注影响用户体验的指标：

*   **易用性**：按键操作是否直观，菜单逻辑是否清晰，校准流程是否简单。
*   **佩戴舒适性**：设备是否贴合人体，固定带是否舒适，长时间佩戴是否引起不适。
*   **反馈感知度**：LED、电机、语音的反馈是否容易被用户感知到，强度是否合适。
*   **APP兼容性**（如果实现）：APP连接是否稳定，界面是否友好，数据展示是否清晰。

这些用户体验指标通常通过用户试用和问卷调查来评估。 