#ifndef __SYSTEM_DEFS_H
#define __SYSTEM_DEFS_H

#include "stm32f10x.h"
#include "ir_sensor.h"  // 包含IR传感器头文件，使用其中的TrainingMode_t定义

// 系统状态定义
typedef enum {
    SYSTEM_IDLE = 0,        // 空闲状态
    SYSTEM_RUNNING,         // 运行状态
    SYSTEM_DEBUG,           // 调试状态
    SYSTEM_CALIBRATION,     // 校准状态
    SYSTEM_AUTO_TRAINING,   // 自动训练状态
    SYSTEM_SETTINGS         // 设置状态
} SystemState_t;

// 训练阶段定义
typedef enum {
    PHASE_WARM_UP = 0,      // 热身阶段
    PHASE_TRAINING,         // 训练阶段
    PHASE_COOL_DOWN,        // 放松阶段
    PHASE_REST              // 休息阶段
} TrainingPhase_t;

// 云平台状态标志 (已禁用)
// #define CLOUD_CONNECTED     0x01  // 云平台已连接
// #define CLOUD_DISCONNECTED  0x00  // 云平台未连接

// 云平台数据传输标志 (已禁用)
// #define CLOUD_DATA_READY    0x01  // 数据已准备好上传
// #define CLOUD_DATA_SENT     0x02  // 数据已发送
// #define CLOUD_CMD_RECEIVED  0x04  // 收到云平台命令

extern SystemState_t systemState;
extern uint8_t system_start_flag;
extern uint16_t initial_distance; // 校准的初始距离

// 全局标志
extern uint8_t motor_control_flag;  // 电机控制标志

// 用户配置参数
typedef struct {
    uint8_t training_duration;      // 训练时长(分钟)
    uint8_t resistance_level;       // 阻力等级(1-5)
    uint8_t auto_adjust;            // 自动调整阻力(0/1)
    uint8_t feedback_level;         // 反馈强度(1-3)
    uint8_t sound_enabled;          // 声音开关(0/1)
} UserConfig_t;

// 应用上下文结构体定义
typedef struct {
    SystemState_t system_state;     // 系统状态
    TrainingMode_t training_mode;   // 训练模式
    TrainingPhase_t training_phase; // 训练阶段
    uint8_t motor_control_flag;     // 电机控制标志
    uint8_t motor_status;           // 电机状态 (0:停止, 1:运行中)
    uint32_t training_time;         // 训练时间(秒)
    uint16_t training_cycles;       // 训练周期计数
    UserConfig_t user_config;       // 用户配置
    // uint8_t cloud_status;           // 云平台状态 (已禁用)
} AppContext_t;

// 全局应用上下文
extern AppContext_t g_app_context;

#endif /* __SYSTEM_DEFS_H */