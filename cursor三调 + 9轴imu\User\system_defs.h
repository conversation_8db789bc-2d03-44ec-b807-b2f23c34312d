#ifndef __SYSTEM_DEFS_H
#define __SYSTEM_DEFS_H

#include "stm32f10x.h"

//==============================================================================
// AFE板测试系统定义
//==============================================================================

// AFE测试系统状态定义
typedef enum {
    AFE_STATE_INIT = 0,         // 初始化状态
    AFE_STATE_READY,            // 就绪状态
    AFE_STATE_TESTING,          // 测试中状态
    AFE_STATE_ERROR             // 错误状态
} AFE_SystemState_t;

// 系统配置参数
typedef struct {
    uint32_t sample_rate;       // 采样率
    uint16_t buffer_size;       // 缓冲区大小
    uint8_t display_mode;       // 显示模式
} AFE_SystemConfig_t;

// 全局变量声明
extern AFE_SystemState_t g_afe_system_state;
extern AFE_SystemConfig_t g_afe_system_config;

#endif /* __SYSTEM_DEFS_H */