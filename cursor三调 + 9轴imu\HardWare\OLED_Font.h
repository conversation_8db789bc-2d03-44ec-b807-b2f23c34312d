#ifndef __OLED_FONT_H
#define __OLED_FONT_H

#include <stdint.h>

/*中文字符字节宽度*/
#define OLED_CHN_CHAR_WIDTH			3		//UTF-8编码格式给3，GB2312编码格式给2

/*字模基本单元*/
typedef struct 
{
	char Index[OLED_CHN_CHAR_WIDTH + 1];	//汉字索引
	uint8_t Data[32];						//字模数据
} ChineseCell_t;

/*ASCII字模数据声明*/
extern const uint8_t OLED_F8x16[][16];  // 8x16字体
extern const uint8_t OLED_F6x8[][6];    // 6x8字体

/*汉字字模数据声明*/
extern const ChineseCell_t OLED_CF16x16[]; // 16x16汉字字体

/*图像数据声明*/
extern const uint8_t Diode[];           // 二极管图标
extern const uint8_t Face_eyes[];       // 眼睛图标
extern const uint8_t Face_happy[];      // 开心表情
extern const uint8_t Face_mania[];      // 疯狂表情
extern const uint8_t Face_sleep[];      // 睡觉表情
extern const uint8_t Face_stare[];      // 呆表情
extern const uint8_t Face_very_happy[]; // 非常开心表情

#endif 
