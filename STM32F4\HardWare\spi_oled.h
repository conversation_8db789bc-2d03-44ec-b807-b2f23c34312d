/**
  ******************************************************************************
  * @file    spi_oled.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   0.96寸SPI OLED驱动头文件 (SSD1306控制器) - STM32F4版本
  ******************************************************************************
  */

#ifndef __SPI_OLED_H
#define __SPI_OLED_H

#include "stm32f4xx.h"

//==============================================================================
// OLED硬件连接定义 - 天空星STM32F407VGT6开发板
//==============================================================================

//== I2C接口定义 ==================================================================
#define OLED_I2C                    I2C1
#define OLED_I2C_CLK                RCC_APB1Periph_I2C1
#define OLED_I2C_CLK_INIT           RCC_APB1PeriphClockCmd

// I2C引脚定义（天空星STM32F407ZGT6，PB6=SCL, PB7=SDA）
#define OLED_SCL_PIN                GPIO_Pin_6      // PB6 - I2C1_SCL
#define OLED_SCL_GPIO_PORT          GPIOB
#define OLED_SCL_GPIO_CLK           RCC_AHB1Periph_GPIOB
#define OLED_SCL_SOURCE             GPIO_PinSource6
#define OLED_SCL_AF                 GPIO_AF_I2C1

#define OLED_SDA_PIN                GPIO_Pin_7      // PB7 - I2C1_SDA
#define OLED_SDA_GPIO_PORT          GPIOB
#define OLED_SDA_GPIO_CLK           RCC_AHB1Periph_GPIOB
#define OLED_SDA_SOURCE             GPIO_PinSource7
#define OLED_SDA_AF                 GPIO_AF_I2C1

// 设备I2C地址（SSD1306常用地址0x3C，如有跳线可改为0x3D）
#define OLED_I2C_ADDRESS            0x3C

//==============================================================================
// 控制引脚操作宏
//==============================================================================

//== 控制引脚宏(四线I2C模块通常不暴露这些引脚，这里定义为空宏以兼容旧实现) ===========
#define OLED_RES_SET()              do { } while(0)
#define OLED_RES_CLR()              do { } while(0)
#define OLED_DC_SET()               do { } while(0)
#define OLED_DC_CLR()               do { } while(0)
#define OLED_CS_SET()               do { } while(0)
#define OLED_CS_CLR()               do { } while(0)

//==============================================================================
// OLED显示参数
//==============================================================================

#define OLED_WIDTH                  128             // OLED宽度
#define OLED_HEIGHT                 64              // OLED高度
#define OLED_PAGES                  8               // OLED页数

//==============================================================================
// 字体大小定义
//==============================================================================

#define OLED_8X16                   16              // 8x16字体
#define OLED_6X8                    8               // 6x8字体

//==============================================================================
// 函数声明
//==============================================================================

// 基础驱动函数
void OLED_Init(void);
void OLED_Clear(void);
void OLED_Update(void);

// 显示函数
void OLED_ShowChar(int16_t X, int16_t Y, char Char, uint8_t FontSize);
void OLED_ShowString(int16_t X, int16_t Y, char *String, uint8_t FontSize);
void OLED_ShowNum(int16_t X, int16_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);

// 低级驱动函数
void OLED_WriteCmd(uint8_t cmd);
void OLED_WriteData(uint8_t data);
void OLED_WriteByte(uint8_t data, uint8_t cmd);

// 延时函数
void OLED_Delay_ms(uint32_t ms);

#endif /* __SPI_OLED_H */
