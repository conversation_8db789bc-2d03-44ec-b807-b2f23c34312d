.\objects\face_config.o: HardWare\Face_Config.c
.\objects\face_config.o: .\Start\stm32f10x.h
.\objects\face_config.o: .\Start\core_cm3.h
.\objects\face_config.o: D:\keil5   MDK\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\face_config.o: .\Start\system_stm32f10x.h
.\objects\face_config.o: .\User\stm32f10x_conf.h
.\objects\face_config.o: .\Library\stm32f10x_adc.h
.\objects\face_config.o: .\Start\stm32f10x.h
.\objects\face_config.o: .\Library\stm32f10x_bkp.h
.\objects\face_config.o: .\Library\stm32f10x_can.h
.\objects\face_config.o: .\Library\stm32f10x_cec.h
.\objects\face_config.o: .\Library\stm32f10x_crc.h
.\objects\face_config.o: .\Library\stm32f10x_dac.h
.\objects\face_config.o: .\Library\stm32f10x_dbgmcu.h
.\objects\face_config.o: .\Library\stm32f10x_dma.h
.\objects\face_config.o: .\Library\stm32f10x_exti.h
.\objects\face_config.o: .\Library\stm32f10x_flash.h
.\objects\face_config.o: .\Library\stm32f10x_fsmc.h
.\objects\face_config.o: .\Library\stm32f10x_gpio.h
.\objects\face_config.o: .\Library\stm32f10x_i2c.h
.\objects\face_config.o: .\Library\stm32f10x_iwdg.h
.\objects\face_config.o: .\Library\stm32f10x_pwr.h
.\objects\face_config.o: .\Library\stm32f10x_rcc.h
.\objects\face_config.o: .\Library\stm32f10x_rtc.h
.\objects\face_config.o: .\Library\stm32f10x_sdio.h
.\objects\face_config.o: .\Library\stm32f10x_spi.h
.\objects\face_config.o: .\Library\stm32f10x_tim.h
.\objects\face_config.o: .\Library\stm32f10x_usart.h
.\objects\face_config.o: .\Library\stm32f10x_wwdg.h
.\objects\face_config.o: .\Library\misc.h
.\objects\face_config.o: HardWare\OLED.h
.\objects\face_config.o: HardWare\OLED_Data.h
.\objects\face_config.o: HardWare\BlueTooth.h
