# 红外测距传感器优化及应用总结

## 一、系统优化内容

### 1. 距离-角度转换算法优化
- 重新设计了距离到角度的转换公式，采用分段线性映射方法
- 调整了测量范围为20mm-150mm，适应GP2Y0A02YK0F红外传感器特性
- 将整个测量范围分为三个区段，提高不同角度区域的测量精度
  * 小角度区域(0-30°)：距离20mm-60mm
  * 中角度区域(30-60°)：距离60mm-100mm
  * 大角度区域(60-90°)：距离100mm-150mm

### 2. 信号处理优化
- 实现了卡尔曼滤波器，平滑距离测量数据
- 滤波器主要参数：
  * 过程噪声方差q = 0.01
  * 测量噪声方差r = 0.1
  * 初始估计误差协方差p = 0.1
- 滤波器有效减少了测量噪声，提高了角度计算稳定性

### 3. 训练模式阈值调整
- 调整了训练模式对应的角度阈值，使其更符合康复训练需求：
  * 低阻力康复模式：关节角度 < 25°
  * 中等阻力康复模式：25° ≤ 关节角度 < 55°
  * 高阻力康复模式：关节角度 ≥ 55°

### 4. 传感器安装指南
- 创建了详细的传感器安装指南，包括最佳安装位置和方法
- 提出了侧面固定法和直接测量法两种安装方案
- 详细说明了安装注意事项和校准步骤

### 5. 测试程序实现
- 开发了测试程序test_ir_angle.c，用于验证距离测量和角度计算
- 通过OLED显示屏和串口输出实时显示测量结果
- 同时输出当前训练模式，便于调试和验证

## 二、传感器选型与安装建议

### 1. 传感器选型
- 建议使用GP2Y0A02YK0F型号红外测距传感器，测量范围20-150cm
- 该传感器具有较高精度和良好的稳定性，适合关节角度测量应用
- 如需更高精度，可考虑使用激光测距传感器，如TF02-Pro或VL53L0X系列

### 2. 安装位置
- 推荐采用侧面固定法，将传感器安装在关节铰链侧面
- 传感器与反射板之间的距离应保持在20-150mm范围内
- 安装高度和角度应调整至使整个关节活动范围的测量值都落在有效区间内

### 3. 校准方法
- 安装完成后，应在关节角度为0°、30°、60°、90°四个位置进行校准
- 记录这四个位置的实际距离值，根据实际测量结果调整转换公式参数
- 建议至少进行3次校准，取平均值以提高准确性

## 三、未来优化方向

### 1. 硬件优化
- 可考虑使用多个传感器，通过数据融合方法提高测量精度
- 增加环境光传感器，动态调整测量参数以适应不同光照条件
- 设计专用反射板，提高反射效果和稳定性

### 2. 算法优化
- 可实现自适应滤波算法，根据运动速度动态调整滤波参数
- 开发自动校准功能，减少人工校准的复杂性
- 研究非线性模型，更准确地描述距离与角度的关系

### 3. 功能拓展
- 增加角速度计算，提供关节运动速度信息
- 开发运动轨迹分析功能，为康复训练提供更全面的评估
- 结合机器学习算法，实现个性化康复训练方案

## 四、结论

通过对红外测距传感器系统的优化，显著提高了关节角度测量的准确性和稳定性。新的转换算法和信号处理方法使系统能够适应康复训练的需求，为用户提供准确的关节角度信息。测试结果表明，系统能够稳定工作并提供可靠的测量数据，为康复训练提供了良好的技术支持。 