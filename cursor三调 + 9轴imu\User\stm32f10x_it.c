/**
  ******************************************************************************
  * @file    Project/STM32F10x_StdPeriph_Template/stm32f10x_it.c 
  * <AUTHOR> Application Team
  * @version V3.5.0
  * @date    08-April-2011
  * @brief   Main Interrupt Service Routines.
  *          This file provides template for all exceptions handler and 
  *          peripherals interrupt service routine.
  ******************************************************************************
  * @attention
  *
  * THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
  * WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE
  * TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR ANY
  * DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING
  * FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE
  * CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
  *
  * <h2><center>&copy; COPYRIGHT 2011 STMicroelectronics</center></h2>
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stm32f10x_it.h"
#include "delay.h"
#include "AFE_Test.h"     // AFE板测试头文件

/** @addtogroup STM32F10x_StdPeriph_Template
  * @{
  */

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/******************************************************************************/
/*            Cortex-M3 Processor Exceptions Handlers                         */
/******************************************************************************/

/**
  * @brief  This function handles NMI exception.
  * @param  None
  * @retval None
  */
void NMI_Handler(void)
{
}

/**
  * @brief  This function handles Hard Fault exception.
  * @param  None
  * @retval None
  */
void HardFault_Handler(void)
{
  /* Go to infinite loop when Hard Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Memory Manage exception.
  * @param  None
  * @retval None
  */
void MemManage_Handler(void)
{
  /* Go to infinite loop when Memory Manage exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Bus Fault exception.
  * @param  None
  * @retval None
  */
void BusFault_Handler(void)
{
  /* Go to infinite loop when Bus Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Usage Fault exception.
  * @param  None
  * @retval None
  */
void UsageFault_Handler(void)
{
  /* Go to infinite loop when Usage Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles SVCall exception.
  * @param  None
  * @retval None
  */
void SVC_Handler(void)
{
}

/**
  * @brief  This function handles Debug Monitor exception.
  * @param  None
  * @retval None
  */
void DebugMon_Handler(void)
{
}

/**
  * @brief  This function handles PendSVC exception.
  * @param  None
  * @retval None
  */
void PendSV_Handler(void)
{
}

/**
  * @brief  This function handles SysTick Handler.
  * @param  None
  * @retval None
  */
void SysTick_Handler(void)
{
}

/******************************************************************************/
/*                 STM32F10x Peripherals Interrupt Handlers                   */
/*  Add here the Interrupt Handler for the used peripheral(s) (PPP), for the  */
/*  available peripheral interrupt handler's name please refer to the startup */
/*  file (startup_stm32f10x_xx.s).                                            */
/******************************************************************************/

/**
  * @brief  This function handles EXTI0 interrupt request (AFE时钟中断).
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler(void)
{
    // AFE板时钟中断处理
    extern uint16_t g_adc_buffer[];
    extern volatile uint16_t g_buffer_index;
    extern volatile uint8_t g_data_ready_flag;

    if(EXTI_GetITStatus(EXTI_Line0) != RESET)
    {
        // 读取AFE板12位并行数据
        uint16_t adc_data = GPIO_ReadInputData(GPIOC) & 0x0FFF;

        // 存储到缓存
        if(g_buffer_index < 256)  // AFE_BUFFER_SIZE
        {
            g_adc_buffer[g_buffer_index] = adc_data;
            g_buffer_index++;

            // 缓存满时设置数据就绪标志
            if(g_buffer_index >= 256)  // AFE_BUFFER_SIZE
            {
                g_data_ready_flag = 1;
            }
        }

        // 清除中断标志
        EXTI_ClearITPendingBit(EXTI_Line0);
    }
}

/**
  * @brief  This function handles USART1 interrupt request.
  * @param  None
  * @retval None
  * @note   AFE测试系统不使用串口，此函数为空实现
  */
void USART1_IRQHandler(void)
{
    // AFE测试系统不使用USART1
}
/**
  * @brief  This function handles USART2 interrupt request.
  * @param  None
  * @retval None
  * @note   AFE测试系统不使用串口，此函数为空实现
  */
void USART2_IRQHandler(void)
{
    // AFE测试系统不使用USART2
}

/**
  * @brief  This function handles USART3 interrupt request.
  * @param  None
  * @retval None
  * @note   AFE测试系统不使用串口，此函数为空实现
  */
void USART3_IRQHandler(void)
{
    // AFE测试系统不使用USART3
}

/**
  * @brief  This function handles PPP interrupt request.
  * @param  None
  * @retval None
  */
/*void PPP_IRQHandler(void)
{
}*/

/**
  * @}
  */ 


/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
