/**
  ******************************************************************************
  * @file    Project/STM32F10x_StdPeriph_Template/stm32f10x_it.c 
  * <AUTHOR> Application Team
  * @version V3.5.0
  * @date    08-April-2011
  * @brief   Main Interrupt Service Routines.
  *          This file provides template for all exceptions handler and 
  *          peripherals interrupt service routine.
  ******************************************************************************
  * @attention
  *
  * THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
  * WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE
  * TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR ANY
  * DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING
  * FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE
  * CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
  *
  * <h2><center>&copy; COPYRIGHT 2011 STMicroelectronics</center></h2>
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stm32f10x_it.h"
#include "delay.h"
#include "system_defs.h"  // 包含系统状态定义
#include "OLED.h"         // 添加OLED函数声明
#include "usart.h"        // 添加串口声明
#include "cloud_connect.h" // 添加云连接模块声明
#include <string.h>       // 添加string.h头文件，解决memcpy和strstr警告

/** @addtogroup STM32F10x_StdPeriph_Template
  * @{
  */

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/******************************************************************************/
/*            Cortex-M3 Processor Exceptions Handlers                         */
/******************************************************************************/

/**
  * @brief  This function handles NMI exception.
  * @param  None
  * @retval None
  */
void NMI_Handler(void)
{
}

/**
  * @brief  This function handles Hard Fault exception.
  * @param  None
  * @retval None
  */
void HardFault_Handler(void)
{
  /* Go to infinite loop when Hard Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Memory Manage exception.
  * @param  None
  * @retval None
  */
void MemManage_Handler(void)
{
  /* Go to infinite loop when Memory Manage exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Bus Fault exception.
  * @param  None
  * @retval None
  */
void BusFault_Handler(void)
{
  /* Go to infinite loop when Bus Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Usage Fault exception.
  * @param  None
  * @retval None
  */
void UsageFault_Handler(void)
{
  /* Go to infinite loop when Usage Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles SVCall exception.
  * @param  None
  * @retval None
  */
void SVC_Handler(void)
{
}

/**
  * @brief  This function handles Debug Monitor exception.
  * @param  None
  * @retval None
  */
void DebugMon_Handler(void)
{
}

/**
  * @brief  This function handles PendSVC exception.
  * @param  None
  * @retval None
  */
void PendSV_Handler(void)
{
}

/**
  * @brief  This function handles SysTick Handler.
  * @param  None
  * @retval None
  */
void SysTick_Handler(void)
{
}

/******************************************************************************/
/*                 STM32F10x Peripherals Interrupt Handlers                   */
/*  Add here the Interrupt Handler for the used peripheral(s) (PPP), for the  */
/*  available peripheral interrupt handler's name please refer to the startup */
/*  file (startup_stm32f10x_xx.s).                                            */
/******************************************************************************/

/**
  * @brief  This function handles EXTI0 interrupt request (AFE时钟中断).
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler(void)
{
    // AFE板时钟中断处理
    extern uint16_t g_adc_buffer[];
    extern volatile uint16_t g_buffer_index;
    extern volatile uint8_t g_data_ready_flag;

    if(EXTI_GetITStatus(EXTI_Line0) != RESET)
    {
        // 读取AFE板12位并行数据
        uint16_t adc_data = GPIO_ReadInputData(GPIOC) & 0x0FFF;

        // 存储到缓存
        if(g_buffer_index < 256)  // AFE_BUFFER_SIZE
        {
            g_adc_buffer[g_buffer_index] = adc_data;
            g_buffer_index++;

            // 缓存满时设置数据就绪标志
            if(g_buffer_index >= 256)  // AFE_BUFFER_SIZE
            {
                g_data_ready_flag = 1;
            }
        }

        // 清除中断标志
        EXTI_ClearITPendingBit(EXTI_Line0);
    }
}

/**
  * @brief  This function handles USART1 interrupt request (ESP8266模块数据接收中断).
  * @param  None
  * @retval None
  */
void USART1_IRQHandler(void)
{
    extern uint8_t ESP8266_RxBuffer[];
    extern uint16_t ESP8266_RxCounter;
    extern uint8_t ESP8266_DataReceived;
    
    if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)
    {
        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
        uint8_t b = USART_ReceiveData(USART1);
        
        // 检查接收缓冲区是否有足够空间
        if(ESP8266_RxCounter < (RX_BUFFER_SIZE - 1))
        {
            // 保存收到的字符
            ESP8266_RxBuffer[ESP8266_RxCounter++] = b;
            
            // 检查是否接收到了完整的行
            if(b == '\n')
            {
                // 保证字符串以null结尾
                ESP8266_RxBuffer[ESP8266_RxCounter] = '\0';
                
                // 设置接收完成标志
                ESP8266_DataReceived = 1;
                
                // 检查是否为MQTT接收数据
                if(ESP8266_RxCounter >= 12)
                {
                    char temp[13] = {0};
                    memcpy(temp, ESP8266_RxBuffer, 12);
                    if(strstr(temp, "+MQTTSUBRECV"))
                    {
                        // 不立即重置计数器，等待Cloud_Process处理完数据
                    }
                    else if(strstr((char*)ESP8266_RxBuffer, "OK") || 
                            strstr((char*)ESP8266_RxBuffer, "ready") ||
                            strstr((char*)ESP8266_RxBuffer, "ERROR"))
                    {
                        // AT指令响应 - 这里不重置，让AT_write函数处理
                    }
                    else
                    {
                        // 其他响应 - 重置计数器
                        ESP8266_RxCounter = 0;
                    }
                }
                else
                {
                    // 短响应 - 保留给AT_write函数处理
                }
            }
            
            // 如果收到的数据超长，需要判断是否为MQTT接收数据
            if(ESP8266_RxCounter >= 200)
            {
                char temp[13] = {0};
                memcpy(temp, ESP8266_RxBuffer, 12);
                if(strstr(temp, "+MQTTSUBRECV"))
                {
                    // 设置接收完成标志，让Cloud_Process处理
                    ESP8266_DataReceived = 1;
                }
                else
                {
                    // 超长的非MQTT数据，重置计数器
                    ESP8266_RxCounter = 0;
                }
            }
        }
        else
        {
            // 缓冲区已满，重置计数器
            ESP8266_RxCounter = 0;
        }
    }
}

/**
  * @brief  This function handles USART2 interrupt request (GY-56传感器数据接收中断).
  * @param  None
  * @retval None
  */
void USART2_IRQHandler(void)
{
    extern uint8_t re_Buf_Data[];
    extern uint8_t Receive_ok;
    static uint8_t rebuf_num = 0;
    
    if(USART_GetITStatus(USART2, USART_IT_RXNE) != RESET)
    {
        re_Buf_Data[rebuf_num++] = USART_ReceiveData(USART2);
        if(rebuf_num >= 4 && rebuf_num == (re_Buf_Data[3] + 5))
        {
            Receive_ok = 1;
            rebuf_num = 0;
        }
    }
}

/**
  * @brief  This function handles USART3 interrupt request (IMU 9轴传感器数据接收中断).
  * @param  None
  * @retval None
  */
void USART3_IRQHandler(void)
{
    extern uint8_t imu_Buf_Data[];
    extern uint8_t imu_Receive_ok;
    extern uint16_t imu_rx_len;
    
    if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
    {
        uint8_t b = USART_ReceiveData(USART3);
        if(imu_rx_len < 99) {
            imu_Buf_Data[imu_rx_len++] = b;
            if(b == '\n') {
                imu_Buf_Data[imu_rx_len] = '\0';
                imu_Receive_ok = 1;
            }
        }
        else
            imu_rx_len = 0;
    }
}

/**
  * @brief  This function handles PPP interrupt request.
  * @param  None
  * @retval None
  */
/*void PPP_IRQHandler(void)
{
}*/

/**
  * @}
  */ 


/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
