/**
  ******************************************************************************
  * @file    Project/STM32F10x_StdPeriph_Template/stm32f10x_it.c 
  * <AUTHOR> Application Team
  * @version V3.5.0
  * @date    08-April-2011
  * @brief   Main Interrupt Service Routines.
  *          This file provides template for all exceptions handler and 
  *          peripherals interrupt service routine.
  ******************************************************************************
  * @attention
  *
  * THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS AT PROVIDING CUSTOMERS
  * WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN ORDER FOR THEM TO SAVE
  * TIME. AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIABLE FOR ANY
  * DIRECT, INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY CLAIMS ARISING
  * FROM THE CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOMERS OF THE
  * CODING INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR PRODUCTS.
  *
  * <h2><center>&copy; COPYRIGHT 2011 STMicroelectronics</center></h2>
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stm32f10x_it.h"
#include "delay.h"
#include "system_defs.h"  // 包含系统状态定义
#include "OLED.h"         // 添加OLED函数声明
#include "usart.h"        // 添加串口声明
#include "cloud_connect.h" // 添加云连接模块声明
#include <string.h>       // 添加string.h头文件，解决memcpy和strstr警告

/** @addtogroup STM32F10x_StdPeriph_Template
  * @{
  */

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/******************************************************************************/
/*            Cortex-M3 Processor Exceptions Handlers                         */
/******************************************************************************/

/**
  * @brief  This function handles NMI exception.
  * @param  None
  * @retval None
  */
void NMI_Handler(void)
{
}

/**
  * @brief  This function handles Hard Fault exception.
  * @param  None
  * @retval None
  */
void HardFault_Handler(void)
{
  /* Go to infinite loop when Hard Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Memory Manage exception.
  * @param  None
  * @retval None
  */
void MemManage_Handler(void)
{
  /* Go to infinite loop when Memory Manage exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Bus Fault exception.
  * @param  None
  * @retval None
  */
void BusFault_Handler(void)
{
  /* Go to infinite loop when Bus Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles Usage Fault exception.
  * @param  None
  * @retval None
  */
void UsageFault_Handler(void)
{
  /* Go to infinite loop when Usage Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles SVCall exception.
  * @param  None
  * @retval None
  */
void SVC_Handler(void)
{
}

/**
  * @brief  This function handles Debug Monitor exception.
  * @param  None
  * @retval None
  */
void DebugMon_Handler(void)
{
}

/**
  * @brief  This function handles PendSVC exception.
  * @param  None
  * @retval None
  */
void PendSV_Handler(void)
{
}

/**
  * @brief  This function handles SysTick Handler.
  * @param  None
  * @retval None
  */
void SysTick_Handler(void)
{
}

/******************************************************************************/
/*                 STM32F10x Peripherals Interrupt Handlers                   */
/*  Add here the Interrupt Handler for the used peripheral(s) (PPP), for the  */
/*  available peripheral interrupt handler's name please refer to the startup */
/*  file (startup_stm32f10x_xx.s).                                            */
/******************************************************************************/

/**
  * @brief  This function handles EXTI0 interrupt request (按键中断).
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler(void)
{
    // system_defs.h中已经声明了这些变量，不需要再次声明
    // extern uint8_t system_start_flag;
    // extern SystemState_t systemState;
    static uint32_t press_time = 0;      // 按键按下时间
    extern uint8_t force_refresh_flag;   // 添加强制刷新标志变量声明
    extern uint16_t initial_distance;    // 校准的初始距离
    extern uint16_t ir_distance;         // 当前IR传感器的距离值
    extern uint8_t motor_control_flag;   // 电机控制标志
    extern uint8_t auto_mode_flag;       // 自动模式标志
    extern uint32_t training_time;       // 训练时间
    extern uint16_t training_cycles;     // 训练周期
    
    if (EXTI_GetITStatus(EXTI_Line0) == SET)
    { 
        EXTI_ClearITPendingBit(EXTI_Line0);
        
        // 检测按键按下
        if(GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0) == 0)
        {
            press_time = 0;  // 计时开始
            
            // 等待按键释放或超时
            while(GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0) == 0)
            {
                delay_ms(10);  // 延时10ms
                press_time += 10;
                
                // 超过2秒判定为长按
                if(press_time >= 2000)
                {
                    // 如果当前是调试模式，则返回正常模式
                    if(systemState == SYSTEM_DEBUG)
                    {
                        systemState = SYSTEM_RUNNING;  // 切换到正常运行模式
                        
                        // 提供模式切换反馈 - 闪烁提示
                        for(uint8_t i = 0; i < 3; i++)
                        {
                            // 显示模式切换提示
                            OLED_Clear();
                            OLED_ShowString(10, 16, "Switching to", 6);
                            OLED_ShowString(10, 32, "RUNNING MODE", 6);
                            OLED_Update();
                            delay_ms(200);
                            
                            OLED_Clear();
                            OLED_Update();
                            delay_ms(200);
                        }
                    }
                    else if(systemState == SYSTEM_CALIBRATION)
                    {
                        // 从校准模式返回到运行模式
                        systemState = SYSTEM_RUNNING;
                        
                        // 提供模式切换反馈
                        OLED_Clear();
                        OLED_ShowString(10, 16, "Calibration", 6);
                        OLED_ShowString(10, 32, "Cancelled", 6);
                        OLED_Update();
                        delay_ms(1000);
                    }
                    else if(systemState == SYSTEM_AUTO_TRAINING)
                    {
                        // 从自动训练模式返回到运行模式
                        systemState = SYSTEM_RUNNING;
                        auto_mode_flag = 0;
                        
                        // 提供模式切换反馈
                        OLED_Clear();
                        OLED_ShowString(10, 16, "Auto Training", 6);
                        OLED_ShowString(10, 32, "Stopped", 6);
                        OLED_Update();
                        delay_ms(1000);
                    }
                    else if(systemState == SYSTEM_SETTINGS)
                    {
                        // 从设置模式返回到运行模式
                        systemState = SYSTEM_RUNNING;
                        
                        // 提供模式切换反馈
                        OLED_Clear();
                        OLED_ShowString(10, 16, "Settings", 6);
                        OLED_ShowString(10, 32, "Saved", 6);
                        OLED_Update();
                        delay_ms(1000);
                    }
                    else
                    {
                        // 否则进入调试模式
                        systemState = SYSTEM_DEBUG;
                        motor_control_flag = 0;  // 确保切换到调试模式时电机控制标志被重置
                        
                        // 提供模式切换反馈 - 闪烁提示
                        for(uint8_t i = 0; i < 3; i++)
                        {
                            // 显示模式切换提示
                            OLED_Clear();
                            OLED_ShowString(10, 16, "Switching to", 6);
                            OLED_ShowString(10, 32, "DEBUG MODE", 6);
                            OLED_Update();
                            delay_ms(200);
                            
                            OLED_Clear();
                            OLED_Update();
                            delay_ms(200);
                        }
                    }
                    return;
                }
            }
            
            // 短按处理 - 按键时间大于50ms且小于2000ms
            if(press_time < 2000 && press_time > 50)  // 消抖
            {
                if(systemState == SYSTEM_IDLE)
                {
                    // 在空闲状态下，短按启动系统
                    system_start_flag = 1;  // 触发启动标志
                }
                else if(systemState == SYSTEM_RUNNING)
                {
                    // 在运行状态下，短按切换模式
                    if(auto_mode_flag)
                    {
                        // 如果已经在自动模式下，则进入自动训练模式
                        systemState = SYSTEM_AUTO_TRAINING;
                        training_time = 0;
                        training_cycles = 0;
                        
                        // 提供模式切换反馈
                        OLED_Clear();
                        OLED_ShowString(10, 16, "Starting", 6);
                        OLED_ShowString(10, 32, "AUTO TRAINING", 6);
                        OLED_Update();
                        delay_ms(1000);
                    }
                    else
                    {
                        // 否则进入校准模式
                    systemState = SYSTEM_CALIBRATION;
                    motor_control_flag = 0;  // 进入校准模式时停止电机控制
                    
                    // 提供模式切换反馈
                    OLED_Clear();
                    OLED_ShowString(10, 16, "Entering", 6);
                    OLED_ShowString(10, 32, "CALIBRATION", 6);
                    OLED_Update();
                    delay_ms(1000);
                    }
                }
                else if(systemState == SYSTEM_CALIBRATION)
                {
                    // 在校准状态下，短按保存当前读数作为初始距离
                    initial_distance = ir_distance;
                    
                    // 显示保存成功
                    OLED_Clear();
                    OLED_ShowString(0, 16, "Initial Distance", 6);
                    OLED_ShowString(0, 32, "Set to:", 6);
                    OLED_ShowNum(70, 32, initial_distance, 4, 6);
                    OLED_Update();
                    delay_ms(1500);
                    
                    // 返回运行模式
                    systemState = SYSTEM_RUNNING;
                }
                else if(systemState == SYSTEM_DEBUG)
                {
                    // 在调试模式下，短按切换自动模式标志
                    auto_mode_flag = !auto_mode_flag;
                    
                    // 显示模式切换
                    OLED_Clear();
                    OLED_ShowString(0, 16, "Auto Mode:", 6);
                    OLED_ShowString(70, 16, auto_mode_flag ? "ON" : "OFF", 6);
                    OLED_Update();
                    delay_ms(1000);
                    
                    force_refresh_flag = 1; // 强制刷新界面
                }
            }
        }
    }
}

/**
  * @brief  This function handles USART1 interrupt request (ESP8266模块数据接收中断).
  * @param  None
  * @retval None
  */
void USART1_IRQHandler(void)
{
    extern uint8_t ESP8266_RxBuffer[];
    extern uint16_t ESP8266_RxCounter;
    extern uint8_t ESP8266_DataReceived;
    
    if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)
    {
        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
        uint8_t b = USART_ReceiveData(USART1);
        
        // 检查接收缓冲区是否有足够空间
        if(ESP8266_RxCounter < (RX_BUFFER_SIZE - 1))
        {
            // 保存收到的字符
            ESP8266_RxBuffer[ESP8266_RxCounter++] = b;
            
            // 检查是否接收到了完整的行
            if(b == '\n')
            {
                // 保证字符串以null结尾
                ESP8266_RxBuffer[ESP8266_RxCounter] = '\0';
                
                // 设置接收完成标志
                ESP8266_DataReceived = 1;
                
                // 检查是否为MQTT接收数据
                if(ESP8266_RxCounter >= 12)
                {
                    char temp[13] = {0};
                    memcpy(temp, ESP8266_RxBuffer, 12);
                    if(strstr(temp, "+MQTTSUBRECV"))
                    {
                        // 不立即重置计数器，等待Cloud_Process处理完数据
                    }
                    else if(strstr((char*)ESP8266_RxBuffer, "OK") || 
                            strstr((char*)ESP8266_RxBuffer, "ready") ||
                            strstr((char*)ESP8266_RxBuffer, "ERROR"))
                    {
                        // AT指令响应 - 这里不重置，让AT_write函数处理
                    }
                    else
                    {
                        // 其他响应 - 重置计数器
                        ESP8266_RxCounter = 0;
                    }
                }
                else
                {
                    // 短响应 - 保留给AT_write函数处理
                }
            }
            
            // 如果收到的数据超长，需要判断是否为MQTT接收数据
            if(ESP8266_RxCounter >= 200)
            {
                char temp[13] = {0};
                memcpy(temp, ESP8266_RxBuffer, 12);
                if(strstr(temp, "+MQTTSUBRECV"))
                {
                    // 设置接收完成标志，让Cloud_Process处理
                    ESP8266_DataReceived = 1;
                }
                else
                {
                    // 超长的非MQTT数据，重置计数器
                    ESP8266_RxCounter = 0;
                }
            }
        }
        else
        {
            // 缓冲区已满，重置计数器
            ESP8266_RxCounter = 0;
        }
    }
}

/**
  * @brief  This function handles USART2 interrupt request (GY-56传感器数据接收中断).
  * @param  None
  * @retval None
  */
void USART2_IRQHandler(void)
{
    extern uint8_t re_Buf_Data[];
    extern uint8_t Receive_ok;
    static uint8_t rebuf_num = 0;
    
    if(USART_GetITStatus(USART2, USART_IT_RXNE) != RESET)
    {
        re_Buf_Data[rebuf_num++] = USART_ReceiveData(USART2);
        if(rebuf_num >= 4 && rebuf_num == (re_Buf_Data[3] + 5))
        {
            Receive_ok = 1;
            rebuf_num = 0;
        }
    }
}

/**
  * @brief  This function handles USART3 interrupt request (IMU 9轴传感器数据接收中断).
  * @param  None
  * @retval None
  */
void USART3_IRQHandler(void)
{
    extern uint8_t imu_Buf_Data[];
    extern uint8_t imu_Receive_ok;
    extern uint16_t imu_rx_len;
    
    if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
    {
        uint8_t b = USART_ReceiveData(USART3);
        if(imu_rx_len < 99) {
            imu_Buf_Data[imu_rx_len++] = b;
            if(b == '\n') {
                imu_Buf_Data[imu_rx_len] = '\0';
                imu_Receive_ok = 1;
            }
        }
        else
            imu_rx_len = 0;
    }
}

/**
  * @brief  This function handles PPP interrupt request.
  * @param  None
  * @retval None
  */
/*void PPP_IRQHandler(void)
{
}*/

/**
  * @}
  */ 


/******************* (C) COPYRIGHT 2011 STMicroelectronics *****END OF FILE****/
