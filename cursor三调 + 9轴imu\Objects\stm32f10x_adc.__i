--c99 -c --cpu Cortex-M3 -g -O0 --apcs=interwork --split_sections -I .\Start -I .\Library -I .\User -I .\HardWare --no-multibyte-chars
-I.\RTE\_Target_1
-I"D:\keil5   MDK\ARM\PACK\Keil\STM32F1xx_DFP\2.2.0\Device\Include"
-I"D:\keil5   MDK\ARM\CMSIS\Include"
-D__UVISION_VERSION="524" -DSTM32F10X_MD -DUSE_STDPERIPH_DRIVER
-o .\objects\stm32f10x_adc.o --omf_browse .\objects\stm32f10x_adc.crf --depend .\objects\stm32f10x_adc.d "Library\stm32f10x_adc.c"