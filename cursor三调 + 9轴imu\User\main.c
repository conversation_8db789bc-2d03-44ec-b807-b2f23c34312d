#include "stm32f10x.h"
#include "Delay.h"
#include "OLED.h"
#include "AFE_Test.h"  // AFE板测试头文件
#include <string.h>
#include <math.h>
#include <stdio.h>

// AFE测试全局变量
ADC_Stats_t g_adc_stats = {0};          // ADC统计数据
uint32_t system_time = 0;               // 系统时间计数器（毫秒）

// AFE测试主循环函数
void AFE_Test_Loop(void)
{
    static uint32_t display_timer = 0;

    // 处理ADC数据
    AFE_ProcessData(&g_adc_stats);

    // 每500ms更新一次显示
    if(++display_timer >= 50)  // 500ms (假设10ms循环)
    {
        display_timer = 0;
        Display_ADC_Results(&g_adc_stats);
    }
}

int main(void)
{
    // 初始化基本模块
    delay_init(72);
    OLED_Init();

    // 显示初始化提示
    OLED_Clear();
    OLED_ShowString(0, 0, "AFE ADC Test", 6);
    OLED_ShowString(0, 16, "LTC2248 12-bit", 6);
    OLED_ShowString(0, 32, "Initializing...", 6);
    OLED_Update();
    delay_ms(2000);

    // 初始化AFE测试模块
    AFE_Test_Init();

    // 显示就绪界面
    OLED_Clear();
    OLED_ShowString(0, 0, "AFE Test Ready", 6);
    OLED_ShowString(0, 16, "Waiting for", 6);
    OLED_ShowString(0, 32, "ADC Clock...", 6);
    OLED_Update();
    delay_ms(1000);
    
    while(1)
    {
        // 更新系统时间计数
        system_time += 10; // 每个循环周期约10ms

        // 执行AFE测试循环
        AFE_Test_Loop();

        delay_ms(10);  // 主循环延时
    }
}
