#include "stm32f10x.h"
#include "Delay.h"
#include "OLED.h"
#include "Key.h"
#include "Motor.h"
#include "IIC.h"
#include "usart.h"
#include "ir_sensor.h"
#include "control.h"
#include "system_defs.h"  // 包含共享头文件
#include "sensor_fusion.h" // 添加传感器融合头文件
// #include "cloud_connect.h" // 云连接功能已禁用
#include <string.h>
#include <math.h> // 添加对标准数学库的引用，解决fabsf未声明警告

// 全局变量
SystemState_t systemState = SYSTEM_IDLE;  // 系统状态变量定义
uint8_t system_start_flag = 0;            // 按键启动标志
uint8_t force_refresh_flag = 0;           // 强制刷新界面标志
uint16_t ir_distance = 0;                 // 存储IR传感器的距离值
float joint_angle = 0.0f;                 // 关节角度
TrainingMode_t current_mode = MODE_LOW_FORCE;  // 当前训练模式
IR_Sensor_t sensor_data;                       // 传感器数据结构
uint16_t initial_distance = 0;                 // 校准的初始距离
uint32_t training_time = 0;                    // 训练时间计时器(秒)
uint16_t training_cycles = 0;                  // 训练周期计数
uint8_t auto_mode_flag = 0;                    // 自动模式标志
uint8_t motor_control_flag = 0;                // 电机控制标志 - 添加定义

// 全局应用上下文变量定义
AppContext_t g_app_context = {
    .system_state = SYSTEM_IDLE,
    .training_mode = MODE_LOW_FORCE,
    .training_phase = PHASE_WARM_UP,
    .motor_control_flag = 0,
    .training_time = 0,
    .training_cycles = 0,
    // .cloud_status = CLOUD_DISCONNECTED, // 云连接功能已禁用
    .user_config = {
        .training_duration = 10,  // 默认10分钟
        .resistance_level = 2,    // 默认中等阻力
        .auto_adjust = 1,         // 默认开启自动调整
        .feedback_level = 2,      // 默认中等反馈
        .sound_enabled = 1        // 默认开启声音
    }
};

// 用于检测关节角度变化的变量
float prev_joint_angle = 0.0f;                 // 记录上一次的关节角度
uint8_t angle_stable_counter = 0;              // 角度稳定计数器

// IMU通信相关
char imu_buffer[100];
uint8_t imu_buffer_index = 0;
extern uint8_t re_Buf_Data[50];  // 接收缓冲区
extern uint8_t Receive_ok;   // 接收完成标志

uint32_t system_time = 0; // 系统时间计数器（毫秒）

// 云连接功能已禁用
// static uint32_t last_cloud_process_time = 0;
// static uint32_t last_cloud_upload_time = 0;
// #define CLOUD_PROCESS_INTERVAL 100   // 云处理间隔(ms)
// #define CLOUD_UPLOAD_INTERVAL 10000  // 云数据上传间隔(ms)

// 优化调试模式函数，减小代码体积
void Debug_Mode(void)
{
    // 处理电机控制
    if(motor_control_flag)
    {
        g_app_context.motor_control_flag = motor_control_flag;
        g_app_context.training_mode = current_mode;
        Motor_Control_Task();
    }
    
    // 读取传感器数据
    if(Process_VL53L0X_Data(&sensor_data))
    {
        ir_distance = sensor_data.distance;
        Process_IMU_Data();
        Update_Fusion_Data(&sensor_data, &imu_data, &fusion_data);
        joint_angle = fusion_data.joint_angle;
        
        // 检测关节角度变化
        float angle_diff = fabsf(joint_angle - prev_joint_angle);
        
        if (angle_diff > 5.0f && !motor_control_flag)
        {
            if (angle_stable_counter > 3)
            {
                motor_control_flag = 1;
                angle_stable_counter = 0;
            }
            else
                angle_stable_counter++;
        }
        else if (angle_diff > 2.0f)
            angle_stable_counter = 0;
        else if (angle_stable_counter < 10)
                angle_stable_counter++;
        
        prev_joint_angle = joint_angle;
        
        // 简化显示
        OLED_Clear();
        OLED_ShowString(0, 0, "Dist:", 6);
        OLED_ShowNum(40, 0, ir_distance, 4, 6);
        OLED_ShowString(0, 16, "Ang:", 6);
        OLED_ShowFloatNum(40, 16, joint_angle, 2, 1, 6);
        if(imu_data.valid) OLED_ShowString(90, 0, "IMU", 6);
        OLED_ShowString(0, 32, "M:", 6);
        OLED_ShowString(20, 32, motor_control_flag ? "ON" : "OFF", 6);
        OLED_ShowString(0, 48, "LongPress:Exit", 6);
        OLED_Update();
    }
    else
    {
        OLED_Clear();
        OLED_ShowString(0, 16, "SENSOR ERROR", 6);
        OLED_Update();
    }
    delay_ms(100);
}

// 云平台命令回调处理函数已禁用
/*
void Cloud_Command_Handler(const char* commandName, const char* payload)
{
    // 云连接功能已禁用
}
*/

// 自适应训练模式处理函数
void Process_Adaptive_Training(void)
{
    static uint8_t phase = PHASE_WARM_UP;
    static uint32_t phase_timer = 0;
    static uint8_t cycle_count = 0;
    static float angle_threshold = 10.0f; // 初始角度阈值
    
    // 更新训练时间
    if(++phase_timer >= 50) { // 约1秒
        phase_timer = 0;
        training_time++;
        
        // 根据训练时间自动切换训练阶段
        switch(phase) {
            case PHASE_WARM_UP:
                if(training_time >= 60) { // 1分钟热身
                    phase = PHASE_TRAINING;
                    current_mode = MODE_MEDIUM_FORCE; // 切换到中等阻力
                    force_refresh_flag = 1;
                }
                break;
                
            case PHASE_TRAINING:
                if(training_time >= 300) { // 5分钟训练
                    phase = PHASE_COOL_DOWN;
                    current_mode = MODE_LOW_FORCE; // 切换到低阻力
                    force_refresh_flag = 1;
                }
                // 自适应调整阻力
                else if(training_time % 60 == 0) { // 每分钟检查一次
                    // 根据训练周期数调整训练模式
                    if(training_cycles < 5) {
                        current_mode = MODE_LOW_FORCE;
                    } else if(training_cycles < 15) {
                        current_mode = MODE_MEDIUM_FORCE;
                    } else {
                        current_mode = MODE_HIGH_FORCE;
                    }
                    force_refresh_flag = 1;
                }
                break;
                
            case PHASE_COOL_DOWN:
                if(training_time >= 360) { // 1分钟放松
                    phase = PHASE_REST;
                    motor_control_flag = 0; // 停止电机
                    Motor_Release();
                    force_refresh_flag = 1;
                }
                break;
                
            case PHASE_REST:
                if(training_time >= 420) { // 1分钟休息后重新开始
                    phase = PHASE_WARM_UP;
                    training_time = 0;
                    cycle_count++;
                    if(cycle_count >= 3) { // 完成3个周期后退出自动训练
                        systemState = SYSTEM_RUNNING;
                        auto_mode_flag = 0;
                    }
                    force_refresh_flag = 1;
                }
                break;
        }
    }
    
    // 处理传感器数据
    if(Process_VL53L0X_Data(&sensor_data))
    {
        ir_distance = sensor_data.distance;
        Process_IMU_Data();
        Update_Fusion_Data(&sensor_data, &imu_data, &fusion_data);
        joint_angle = fusion_data.joint_angle;
        
        // 检测关节角度变化，计算训练周期
        static float prev_angle = 0.0f;
        static uint8_t direction = 0; // 0:未定义, 1:向上, 2:向下
        
        float angle_diff = joint_angle - prev_angle;
        prev_angle = joint_angle;
        
        // 检测方向变化来计算训练周期
        if(angle_diff > 3.0f && direction != 1) {
            direction = 1; // 向上
            if(direction == 2) { // 从向下变为向上
                training_cycles++;
                // 自适应调整角度阈值
                if(fabsf(joint_angle) > angle_threshold) {
                    angle_threshold = fabsf(joint_angle) * 0.8f; // 80%的最大角度
                }
            }
        }
        else if(angle_diff < -3.0f && direction != 2) {
            direction = 2; // 向下
        }
        
        // 根据当前阶段和角度控制电机
        if(phase != PHASE_REST) {
            if(fabsf(joint_angle) > angle_threshold && !motor_control_flag) {
                motor_control_flag = 1;
            }
            else if(fabsf(joint_angle) < angle_threshold * 0.5f && motor_control_flag) {
                motor_control_flag = 0;
                Motor_Release();
            }
        }
        
        // 显示训练信息
        OLED_Clear();
        
        // 显示当前阶段
        OLED_ShowString(0, 0, "Mode:", 6);
        switch(phase) {
            case PHASE_WARM_UP:
                OLED_ShowString(40, 0, "Warm-Up", 6);
                break;
            case PHASE_TRAINING:
                OLED_ShowString(40, 0, "Training", 6);
                break;
            case PHASE_COOL_DOWN:
                OLED_ShowString(40, 0, "Cool-Down", 6);
                break;
            case PHASE_REST:
                OLED_ShowString(40, 0, "Rest", 6);
                break;
        }
        
        // 显示训练数据
        OLED_ShowString(0, 16, "Time:", 6);
        OLED_ShowNum(40, 16, training_time/60, 2, 6); // 分钟
        OLED_ShowString(52, 16, ":", 6);
        OLED_ShowNum(58, 16, training_time%60, 2, 6); // 秒
        
        OLED_ShowString(0, 32, "Cycles:", 6);
        OLED_ShowNum(50, 32, training_cycles, 3, 6);
        
        OLED_ShowString(0, 48, "Angle:", 6);
        OLED_ShowFloatNum(50, 48, joint_angle, 2, 1, 6);
        
        // 云连接状态显示已禁用
        // Cloud_Status_Info_t* cloud_status_info = Cloud_GetStatus();
        // if(cloud_status_info->cloud_connected) {
        //     OLED_ShowString(90, 48, "Cloud", 6);
        // }
        
        OLED_Update();
    }
    else
    {
        // 显示错误信息
        OLED_Clear();
        OLED_ShowString(0, 16, "SENSOR ERROR", 6);
        OLED_ShowString(0, 32, "Check Connection", 6);
        OLED_Update();
    }
}

int main(void)
{
    // 初始化各个模块 - 调整顺序以避免引脚冲突
    delay_init(72);
    OLED_Init();
    Key_Init();
    
    // 首先初始化电机控制，确保其引脚配置不被覆盖
    Motor_Init();
    Control_Init();
    
    // 显示初始化提示
    OLED_Clear();
    OLED_ShowString(0, 16, "Init...", 6);
    OLED_ShowString(0, 32, "Testing Motor", 6);
    OLED_Update();
    
    // 执行电机测试
    Motor_Test();
    
    // 初始化通信和传感器
    IIC_Init();
    Usart_Int(9600);
    IMU_Usart_Init(115200);
    Sensor_Fusion_Init();
    
    // 云连接功能已禁用
    // ESP8266_Usart_Init(115200);
    // Cloud_Init();
    // Cloud_SetCommandCallback(Cloud_Command_Handler);
    // Cloud_Connect();
    
    // 恢复初始界面
    OLED_Clear();
    OLED_ShowString(0, 16, "Ready", 6);
    OLED_ShowString(0, 32, "Press to start", 6);
    OLED_Update();
    
    // 初始化传感器
    VL53L0X_Init();
    
    // 配置IMU，减少延时
    delay_ms(200);
    IMU_Set_Output_Mode(IMU_OUTPUT_EULER);
    IMU_Set_Output_Frequency(10);
    delay_ms(50);
    
    // 校准过程简化显示
    OLED_Clear();
    OLED_ShowString(0, 16, "IMU Initializing", 6);
    OLED_Update();
    
    // 校准
    IMU_Calibrate_AccGyro();
    delay_ms(500);
    
    OLED_Clear();
    OLED_ShowString(0, 16, "Calibrating Mag", 6);
    OLED_ShowString(0, 32, "Rotate device", 6);
    OLED_Update();
    
    IMU_Calibrate_Mag();
    delay_ms(2000); // 减少等待时间
    
    while(1)
    {
        // 更新系统时间计数
        system_time += 10; // 假设每个循环周期为10ms
        
        // 检查调试模式
        if(systemState == SYSTEM_DEBUG)
        {
            Debug_Mode();
            continue;
        }
        
        // 检查校准模式
        if(systemState == SYSTEM_CALIBRATION)
        {
            // 进入校准模式时确保电机停止工作
            Motor_Release();  // 停止电机
            
            // 显示校准界面
            OLED_Clear();
            OLED_ShowString(0, 0, "Calibration", 6);
            OLED_ShowString(0, 16, "Set init dist:", 6);
            
            // 读取当前传感器距离
            if(Process_VL53L0X_Data(&sensor_data))
            {
                // 显示当前读数
                ir_distance = sensor_data.distance;
                OLED_ShowNum(70, 32, ir_distance, 4, 6);
                OLED_ShowString(0, 32, "Dist:", 6);
                OLED_ShowString(0, 48, "Press to confirm", 6);
            }
            else
            {
                // 显示读取错误
                OLED_ShowString(0, 32, "Sensor Error", 6);
                OLED_ShowString(0, 48, "Try again", 6);
            }
            
            OLED_Update();
            delay_ms(100);
            continue;
        }
        
        // 启动检查
        if(system_start_flag && systemState == SYSTEM_IDLE)
        {
            systemState = SYSTEM_RUNNING;
            OLED_Clear();
            OLED_ShowString(0, 16, "Starting...", 6);
            OLED_Update();
            delay_ms(50);
            system_start_flag = 0;
        }
        
        // 确保在空闲状态下电机停止工作
        if(systemState == SYSTEM_IDLE)
        {
            Motor_Release();  // 在空闲状态下释放电机
        }
        
        // 系统运行状态
        if(systemState == SYSTEM_RUNNING)
        {
            // 移除静态初始化以减少代码体积
            static uint8_t first_run = 1;
            if (first_run)
            {
                first_run = 0;
                OLED_Clear();
                prev_joint_angle = 0.0f;  // 初始化上一次角度
                
                // 直接开启电机，不等待IMU变化触发
                motor_control_flag = 1;
            }
            
            // 处理电机控制 - 使用应用上下文
            g_app_context.motor_control_flag = motor_control_flag;
            g_app_context.training_mode = current_mode;
            Motor_Control_Task();
            
            // 读取传感器数据
            uint8_t read_success = Process_VL53L0X_Data(&sensor_data);
            Process_IMU_Data();
            
            if(read_success)
            {
                ir_distance = sensor_data.distance;
                Update_Fusion_Data(&sensor_data, &imu_data, &fusion_data);
                joint_angle = fusion_data.joint_angle;
                
                // 检测关节角度变化，但不依赖它来触发电机
                float angle_diff = fabsf(joint_angle - prev_joint_angle);
                
                // 只记录角度变化，用于计算训练周期，不触发电机
                if (angle_diff > 5.0f)
                {
                    if (angle_stable_counter > 3)
                    {
                        // 记录显著角度变化，可用于训练周期计算
                        angle_stable_counter = 0;
                    }
                    else
                    {
                        angle_stable_counter++;
                    }
                }
                else if (angle_diff > 2.0f)
                {
                    // 角度仍在变化但幅度较小
                    angle_stable_counter = 0;
                }
                else
                {
                    // 角度变化很小
                    if (angle_stable_counter < 10)
                        angle_stable_counter++;
                }
                
                // 确保电机在RUNNING状态下始终工作
                if(!motor_control_flag && systemState == SYSTEM_RUNNING)
                {
                    motor_control_flag = 1;
                }
                
                // 保存当前角度以便下次比较
                prev_joint_angle = joint_angle;
                
                // 减少屏幕刷新频率，节省处理时间
                static uint8_t update_counter = 0;
                if(current_mode != fusion_data.mode || update_counter >= 20 || force_refresh_flag)
                {
                    update_counter = 0;
                    current_mode = fusion_data.mode;
                    force_refresh_flag = 0;
                    
                    // 简化显示内容
                    OLED_Clear();
                    OLED_ShowString(0, 0, "Angle:", 6);
                    OLED_ShowFloatNum(50, 0, joint_angle, 2, 1, 6);
                    
                    // 简化模式显示
                    OLED_ShowString(0, 32, "Mode:", 6);
                    switch(current_mode)
                    {
                        case MODE_LOW_FORCE:
                            OLED_ShowString(40, 32, "Low", 6);
                            g_app_context.training_mode = MODE_LOW_FORCE;
                            break;
                        case MODE_MEDIUM_FORCE:
                            OLED_ShowString(40, 32, "Med", 6);
                            g_app_context.training_mode = MODE_MEDIUM_FORCE;
                            break;
                        case MODE_HIGH_FORCE:
                            OLED_ShowString(40, 32, "High", 6);
                            g_app_context.training_mode = MODE_HIGH_FORCE;
                            break;
                        case MODE_ADAPTIVE:
                            OLED_ShowString(40, 32, "Auto", 6);
                            g_app_context.training_mode = MODE_ADAPTIVE;
                            break;
                    }
                    
                    // 云连接状态显示已禁用
                    // Cloud_Status_Info_t* cloud_status_info = Cloud_GetStatus();
                    // if(cloud_status_info->cloud_connected)
                    // {
                    //     OLED_ShowString(85, 32, "Cloud", 6);
                    //     // 添加上传指示器
                    //     if(cloud_status_info->data_ready)
                    //         OLED_ShowString(85, 48, "Sync", 6);
                    // }
                    
                    OLED_ShowString(0, 16, "Dist:", 6);
                    OLED_ShowNum(50, 16, ir_distance, 4, 6);
                    
                    // 显示训练周期数
                    OLED_ShowString(0, 48, "Cyc:", 6);
                    OLED_ShowNum(50, 48, training_cycles, 3, 6);
                    
                    OLED_Update();
                }
                else
                {
                    update_counter++;
                }
            }
            else
            {
                // 错误处理简化
                OLED_Clear();
                OLED_ShowString(0, 16, "Sensor Error", 6);
                if(imu_data.valid) {
                    joint_angle = Calculate_Joint_Angle_From_IMU(&imu_data);
                    OLED_ShowString(0, 32, "Using IMU", 6);
                }
                OLED_Update();
                delay_ms(500); // 减少延时
            }
            
            // 云连接处理代码已禁用
            /*
            // 定期处理云平台通信 - 每100ms处理一次
            if(system_time - last_cloud_process_time >= CLOUD_PROCESS_INTERVAL)
            {
                Cloud_Process(); // 处理云平台数据
                last_cloud_process_time = system_time;
            }

            // 定期上传数据到云平台 - 每10秒上传一次
            if(system_time - last_cloud_upload_time >= CLOUD_UPLOAD_INTERVAL)
            {
                // 处理IMU数据并上传
                Cloud_ProcessIMUData(&imu_data, &fusion_data);

                // 使用示例项目中类似的上传方式上传单个属性
                Cloud_UploadProperty("angle", joint_angle);

                // 在训练中时，上传训练信息
                if(training_cycles > 0 || training_time > 0)
                {
                    Cloud_UploadProperty("cycles", (float)training_cycles);
                    Cloud_UploadProperty("time", (float)training_time);
                    Cloud_UploadProperty("mode", (float)current_mode);
                }

                last_cloud_upload_time = system_time;
            }
            */
            
            // 减少延时，提高系统响应速度
            delay_ms(10);
        }
        // 自动训练模式
        else if(systemState == SYSTEM_AUTO_TRAINING)
        {
            Process_Adaptive_Training();
            delay_ms(20);
        }
    }
}
