#include "stm32f10x.h"
#include "Delay.h"
#include "OLED.h"
#include "AFE_Test.h"  // AFE板测试头文件
#include <string.h>
#include <math.h>

// AFE测试全局变量
uint32_t afe_test_time = 0;             // 测试时间计数器
uint32_t system_time = 0;               // 系统时间计数器（毫秒）
uint8_t current_test_mode = 0;          // 当前测试模式索引

// AFE自动测试循环函数
void AFE_Auto_Test_Loop(void)
{
    static uint32_t mode_switch_timer = 0;
    static uint32_t display_timer = 0;

    // 每30秒切换一次测试模式
    if(++mode_switch_timer >= 3000)  // 30秒 (假设10ms循环)
    {
        mode_switch_timer = 0;
        current_test_mode++;
        if(current_test_mode >= 3)  // 只循环前3种模式，跳过校准
            current_test_mode = 0;

        // 停止当前测试，开始新模式测试
        AFE_Stop_Test();
        delay_ms(100);
        AFE_Start_Test((AFE_TestMode_t)current_test_mode);

        // 显示当前模式
        OLED_Clear();
        OLED_ShowString(0, 0, "Mode Switch:", 6);
        switch(current_test_mode)
        {
            case AFE_TEST_SINGLE_CHANNEL:
                OLED_ShowString(0, 16, "Single Channel", 6);
                break;
            case AFE_TEST_MULTI_CHANNEL:
                OLED_ShowString(0, 16, "Multi Channel", 6);
                break;
            case AFE_TEST_CONTINUOUS:
                OLED_ShowString(0, 16, "Continuous", 6);
                break;
        }
        OLED_Update();
        delay_ms(2000);  // 显示2秒
    }

    // 执行AFE测试任务
    AFE_Test_Task();

    // 每500ms更新一次显示
    if(++display_timer >= 50)
    {
        display_timer = 0;
        AFE_Display_Results(g_afe_results);
    }
}

int main(void)
{
    // 初始化基本模块
    delay_init(72);
    OLED_Init();

    // 显示初始化提示
    OLED_Clear();
    OLED_ShowString(0, 0, "AFE Test System", 6);
    OLED_ShowString(0, 16, "Initializing...", 6);
    OLED_Update();
    delay_ms(1000);

    // 初始化AFE测试模块
    AFE_Init();

    // 显示就绪界面
    OLED_Clear();
    OLED_ShowString(0, 0, "AFE Auto Test", 6);
    OLED_ShowString(0, 16, "Starting...", 6);
    OLED_Update();
    delay_ms(2000);

    // 开始第一个测试模式
    AFE_Start_Test(AFE_TEST_SINGLE_CHANNEL);
    
    while(1)
    {
        // 更新系统时间计数
        system_time += 10; // 每个循环周期约10ms
        afe_test_time++;

        // 执行自动测试循环
        AFE_Auto_Test_Loop();

        delay_ms(10);  // 主循环延时
    }
}
