#include "sensor_fusion.h"
#include "ir_sensor.h"
#include "math.h"
#include "string.h"
#include "stdlib.h"
#include <stdio.h>

// 定义常量
static const float PI_F = 3.1415926535f;
static const float SHANK_LENGTH_CM = 40.0f; // 平均小腿长度（厘米）- 重要：请根据实际情况调整!
static const float MAX_KNEE_FLEXION_DEG = 140.0f; // 最大膝关节屈曲角度
static const float MIN_KNEE_FLEXION_DEG = 0.0f;   // 最小膝关节屈曲角度 (0 表示完全伸直)
static const float MAX_ANGULAR_VELOCITY_DEG_S = 400.0f; // 膝关节最大角速度 (度/秒)
static const float SENSOR_UPDATE_INTERVAL_S = 0.02f; // 传感器更新和角度计算的间隔时间（秒），例如20ms
static const float DISTANCE_TOLERANCE_CM = 0.5f;    // 初始距离校准的容差（厘米）

// 全局变量定义
IMU_Data_t imu_data = {0};
Fusion_Data_t fusion_data = {0};
static float previous_fused_angle = 0.0f; // 用于存储上一次融合后的角度

// 卡尔曼滤波参数
#define KALMAN_Q 0.01f        // 过程噪声协方差
#define KALMAN_R_IR 0.1f      // 红外传感器测量噪声协方差
#define KALMAN_R_IMU 0.2f     // IMU测量噪声协方差
#define KALMAN_P_INIT 1.0f    // 初始估计误差协方差

// 卡尔曼滤波器结构体
typedef struct {
    float x;      // 状态值（角度估计）
    float p;      // 估计误差协方差
    float k;      // 卡尔曼增益
    float q;      // 过程噪声协方差
    float r;      // 测量噪声协方差
} Kalman_Filter_t;

static Kalman_Filter_t angle_filter = {0.0f, KALMAN_P_INIT, 0.0f, KALMAN_Q, KALMAN_R_IR};

// 初始化传感器融合模块
void Sensor_Fusion_Init(void)
{
    // 清零IMU数据
    memset(&imu_data, 0, sizeof(IMU_Data_t));
    
    // 初始化IMU数据结构
    imu_data.valid = 0;
    
    // 卡尔曼滤波器初始化
    angle_filter.x = 0.0f;           // 初始状态估计
    angle_filter.p = KALMAN_P_INIT;  // 初始估计误差协方差
    angle_filter.k = 0.0f;           // 初始卡尔曼增益
    angle_filter.q = KALMAN_Q;       // 过程噪声协方差
    angle_filter.r = KALMAN_R_IR;    // 测量噪声协方差
    previous_fused_angle = MIN_KNEE_FLEXION_DEG; // 初始化 previous_fused_angle
}

// 解析从IMU接收到的字符串数据 - 简化版本
void Parse_IMU_Data(const char* data_string, IMU_Data_t *imu_data_ptr)
{
    char *token;
    float values[9] = {0.0f};
    int index = 0;
    
    // 创建数据字符串的可修改副本
    char data_str[100]; // 假设IMU数据字符串不会超过99个字符
    strncpy(data_str, data_string, sizeof(data_str) - 1);
    data_str[sizeof(data_str) - 1] = '\0';
    
    // 清除尾部换行符或回车符
    char* newline = strchr(data_str, '\r');
    if(newline) *newline = '\0';
    newline = strchr(data_str, '\n');
    if(newline) *newline = '\0';
    
    // 只保留必要的数据格式处理：欧拉角和九轴原始数据
    if(strstr(data_str, "euler:") == data_str)
    {
        token = strtok(data_str + 6, ",");
        while (token != NULL && index < 3)
        {
            values[index++] = atof(token);
            token = strtok(NULL, ",");
        }
        if(index == 3)
        {
            imu_data_ptr->roll = values[0];
            imu_data_ptr->pitch = values[1];
            imu_data_ptr->yaw = values[2];
            imu_data_ptr->valid = 1;
        }
        else { imu_data_ptr->valid = 0; }
    }
    else if(strstr(data_str, "acc_gyro_mag:") == data_str || strstr(data_str, "simples:") == data_str)
    {
        char* data_payload = data_str + (strstr(data_str, "acc_gyro_mag:") == data_str ? 13 : 8);
        token = strtok(data_payload, ",");
        while (token != NULL && index < 9)
        {
            values[index++] = atof(token);
            token = strtok(NULL, ",");
        }
        if(index == 9)
        {
            imu_data_ptr->acc_x = values[0]; imu_data_ptr->acc_y = values[1]; imu_data_ptr->acc_z = values[2];
            imu_data_ptr->gyro_x = values[3]; imu_data_ptr->gyro_y = values[4]; imu_data_ptr->gyro_z = values[5];
            imu_data_ptr->mag_x = values[6]; imu_data_ptr->mag_y = values[7]; imu_data_ptr->mag_z = values[8];
            
            // 简化的欧拉角计算 (如果需要，但通常直接使用IMU的欧拉角输出更佳)
            // 这里假设pitch主要来自加速度计数据，适用于低动态情况
            float acc_yz_mag = sqrtf(imu_data_ptr->acc_y * imu_data_ptr->acc_y + imu_data_ptr->acc_z * imu_data_ptr->acc_z);
            if (acc_yz_mag > 0.01f) { // 避免除以零
                 imu_data_ptr->pitch = atan2f(imu_data_ptr->acc_x, acc_yz_mag) * (180.0f / PI_F);
            } else {
                 imu_data_ptr->pitch = 0.0f;
            }
            // Roll 和 Yaw 的简易计算通常不够准确，依赖IMU直接输出
            imu_data_ptr->roll = atan2f(imu_data_ptr->acc_y, imu_data_ptr->acc_z) * (180.0f / PI_F); 
            // Yaw的计算需要磁力计并且更复杂，此处省略，建议使用IMU直接输出的Yaw

            imu_data_ptr->valid = 1; 
        }
        else { imu_data_ptr->valid = 0; }
    }
    else { imu_data_ptr->valid = 0; }
}

// 从IMU姿态角计算关节弯曲角度
float Calculate_Joint_Angle_From_IMU(IMU_Data_t* imu_data_ptr)
{
    if(!imu_data_ptr || !imu_data_ptr->valid)
    {
        return MIN_KNEE_FLEXION_DEG; // 如果数据无效，返回最小角度
    }

    // 假设IMU的pitch角直接对应膝关节屈曲角度
    // 0度表示伸直，正值表示屈曲
    // 需要根据IMU的实际安装方向进行调整和校准
    float angle_deg = imu_data_ptr->pitch;

    // 限制角度到生理范围
    if(angle_deg < MIN_KNEE_FLEXION_DEG)
    {
        angle_deg = MIN_KNEE_FLEXION_DEG;
    }
    else if(angle_deg > MAX_KNEE_FLEXION_DEG)
    {
        angle_deg = MAX_KNEE_FLEXION_DEG;
    }
    
    return angle_deg;
}

// 卡尔曼滤波器更新
float Kalman_Filter_Update(Kalman_Filter_t* filter, float measurement)
{
    filter->p = filter->p + filter->q;
    filter->k = filter->p / (filter->p + filter->r);
    filter->x = filter->x + filter->k * (measurement - filter->x);
    filter->p = (1.0f - filter->k) * filter->p;
    return filter->x;
}

// 传感器数据融合算法
float Fusion_Algorithm(float ir_angle, float imu_angle)
{
    float current_fused_angle;
    static uint8_t init_flag = 0;
    
    // 根据IMU数据的可靠性调整权重
    if(imu_data.valid)
    {
        // IMU数据有效，根据加速度稳定性动态调整权重
        float acc_magnitude = sqrtf(
            imu_data.acc_x * imu_data.acc_x + 
            imu_data.acc_y * imu_data.acc_y + 
            imu_data.acc_z * imu_data.acc_z
        );
        float acc_deviation = fabsf(acc_magnitude - 1.0f); // 假设g为1.0

        // 动态调整IMU和红外的权重
        float ir_weight, imu_weight;

        if(acc_deviation < 0.1f) // IMU稳定
        {
            imu_weight = 0.7f; ir_weight = 0.3f;
            angle_filter.r = KALMAN_R_IR * 0.5f; 
        }
        else if(acc_deviation < 0.3f) // IMU变化中等
        {
            imu_weight = 0.5f; ir_weight = 0.5f;
            angle_filter.r = KALMAN_R_IR;
        }
        else // IMU不稳定 (快速移动或振动)
        {
            imu_weight = 0.2f; ir_weight = 0.8f;
            angle_filter.r = KALMAN_R_IR * 2.0f;
        }
        
        // 融合角度计算
        current_fused_angle = ir_weight * ir_angle + imu_weight * imu_angle;
    }
    else
    {
        // IMU数据无效，完全依赖红外数据
        current_fused_angle = ir_angle; 
        angle_filter.r = KALMAN_R_IR; // 使用标准红外噪声参数
    }
    
    // 应用卡尔曼滤波
    current_fused_angle = Kalman_Filter_Update(&angle_filter, current_fused_angle);
    
    // 首次运行初始化
    if(!init_flag)
    {
        angle_filter.x = current_fused_angle; // 使用第一次的结果初始化滤波器状态
        previous_fused_angle = current_fused_angle; // 初始化上一次角度
        init_flag = 1;
    }
    
    // 最大变化速率限制
    float max_delta_angle = MAX_ANGULAR_VELOCITY_DEG_S * SENSOR_UPDATE_INTERVAL_S;
    float angle_diff = current_fused_angle - previous_fused_angle;
    if (fabsf(angle_diff) > max_delta_angle)
    {
        current_fused_angle = previous_fused_angle + (angle_diff > 0 ? max_delta_angle : -max_delta_angle);
    }
    
    // 确保融合后的角度在生理范围内
    if(current_fused_angle < MIN_KNEE_FLEXION_DEG) current_fused_angle = MIN_KNEE_FLEXION_DEG;
    else if(current_fused_angle > MAX_KNEE_FLEXION_DEG) current_fused_angle = MAX_KNEE_FLEXION_DEG;

    previous_fused_angle = current_fused_angle; // 更新上一次角度
    return current_fused_angle;
}

// 更新融合数据 (此函数结构保持，具体计算在Calculate_Joint_Angle等函数中)
void Update_Fusion_Data(IR_Sensor_t *ir_data, IMU_Data_t *imu_data_ptr, Fusion_Data_t *fusion_data_ptr)
{
    extern uint16_t initial_distance;  // 校准的初始距离 (mm)
    extern uint16_t training_cycles;   // 训练周期数
    extern uint32_t training_time;     // 训练时间
    extern TrainingMode_t current_mode; // 当前训练模式
    
    float ir_angle = Calculate_Joint_Angle(ir_data->distance, initial_distance);
    float imu_angle = Calculate_Joint_Angle_From_IMU(imu_data_ptr);
    
    fusion_data_ptr->joint_angle = Fusion_Algorithm(ir_angle, imu_angle);
    
    fusion_data_ptr->mode = current_mode;
    fusion_data_ptr->training_cycles = training_cycles;
    fusion_data_ptr->training_time = training_time;
    
    // 根据当前模式设置目标角度和阈值 (这部分逻辑保持不变)
    switch(current_mode)
    {
        case MODE_LOW_FORCE:
            fusion_data_ptr->target_angle = 30.0f; fusion_data_ptr->angle_threshold = 15.0f; break;
        case MODE_MEDIUM_FORCE:
            fusion_data_ptr->target_angle = 60.0f; fusion_data_ptr->angle_threshold = 20.0f; break;
        case MODE_HIGH_FORCE:
            fusion_data_ptr->target_angle = 90.0f; fusion_data_ptr->angle_threshold = 25.0f; break;
        case MODE_ADAPTIVE:
            if(training_cycles < 5) {
                fusion_data_ptr->target_angle = 30.0f; fusion_data_ptr->angle_threshold = 15.0f;
            } else if(training_cycles < 15) {
                fusion_data_ptr->target_angle = 60.0f; fusion_data_ptr->angle_threshold = 20.0f;
            } else {
                fusion_data_ptr->target_angle = 90.0f; fusion_data_ptr->angle_threshold = 25.0f;
            }
            break;
        default:
            fusion_data_ptr->target_angle = 30.0f; fusion_data_ptr->angle_threshold = 15.0f; break;
    }
}

// 从距离计算关节角度 (单位: 毫米 mm)
float Calculate_Joint_Angle(uint16_t current_distance_mm, uint16_t initial_distance_mm)
{
    // initial_distance_mm: 腿完全伸直时，传感器到地面的距离 (校准值)
    // current_distance_mm: 当前传感器到地面的距离
    
    if (SHANK_LENGTH_CM <= 0.0f) { // 防止除以零或无效小腿长度
        return MIN_KNEE_FLEXION_DEG;
    }

    // 将毫米转换为厘米
    float initial_d_cm = (float)initial_distance_mm / 10.0f;
    float current_d_cm = (float)current_distance_mm / 10.0f;

    // 传感器高度变化量: 腿伸直时高，弯曲时降低
    float delta_h_cm = initial_d_cm - current_d_cm;

    // 考虑容差: 如果当前距离略大于初始距离（在容差范围内），仍视为0度
    if (delta_h_cm < -DISTANCE_TOLERANCE_CM) { // current_d_cm 明显大于 initial_d_cm + tolerance
        // 传感器读数异常，返回最小角度
        return MIN_KNEE_FLEXION_DEG; 
    }
    if (delta_h_cm < 0.0f) { // 在容差范围内，或者轻微大于初始距离
        delta_h_cm = 0.0f; // 视为高度没有变化，对应0度角
    }
    
    // 调整计算方法，确保能覆盖0-90度范围
    
    // 方法1: 使用反余弦函数计算角度
    // 计算 acos 的参数: 1 - (delta_h / L_shank)
    float cos_theta_arg = 1.0f - (delta_h_cm / SHANK_LENGTH_CM);

    // 限制参数在 acosf 的有效范围内 [-1.0, 1.0]
    if (cos_theta_arg > 1.0f) {
        cos_theta_arg = 1.0f;
    } else if (cos_theta_arg < -1.0f) {
        cos_theta_arg = -1.0f;
    }

    float angle_rad = acosf(cos_theta_arg);
    float angle_deg = angle_rad * (180.0f / PI_F);

    // 应用线性映射以确保覆盖0-90度范围
    // 假设当前计算方法在极限情况下最大只能得到约70度
    if (angle_deg > 0.0f) {
        angle_deg = angle_deg * 90.0f / 70.0f;
    }

    // 确保角度在生理范围内
    if (angle_deg < MIN_KNEE_FLEXION_DEG) {
        angle_deg = MIN_KNEE_FLEXION_DEG;
    } else if (angle_deg > MAX_KNEE_FLEXION_DEG) {
        // 这里我们将最大值限制在90度，与红外传感器的映射一致
        angle_deg = 90.0f;
    }

    return angle_deg;
} 