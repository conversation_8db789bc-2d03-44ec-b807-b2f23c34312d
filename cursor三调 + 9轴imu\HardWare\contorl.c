#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "Motor.h"
#include "Key.h"
#include "control.h"
#include "ir_sensor.h"  // 包含训练模式定义
#include "system_defs.h" // 引入系统定义，包含 AppContext

// 全局变量
extern AppContext_t g_app_context; // 使用在main.c中定义的全局上下文

// 添加电机控制状态变量
static uint8_t motor_initialized = 0;  // 电机初始化标志
static uint32_t last_motor_run_time = 0; // 上次电机运行时间
static uint8_t motor_error_count = 0;    // 电机错误计数

// 添加电机实时控制变量
static TrainingMode_t last_mode = MODE_LOW_FORCE; // 上一次的训练模式

/**
  * 函数名：Control_Init
  * 功能：控制系统初始化
  * 参数：无
  * 返回值：无
  */
void Control_Init(void)
{
	Motor_Init();  // 电机初始化
	Key_Init();    // 按键初始化
	motor_initialized = 1; // 标记电机已初始化
    
    // 初始化时的基本测试，使用简单的启动和停止
    Motor_Test(); // 使用Motor.h中定义的测试函数替代直接GPIO操作
}

/**
  * 函数名：Motor_Control_Task
  * 功能：电机控制任务
  * 参数：无
  * 返回值：无
  */
void Motor_Control_Task(void)
{
    extern uint32_t system_time; // 引入系统时间计数器
    static uint8_t motor_test_done = 0; // 电机测试标志
    static uint32_t last_step_time = 0;  // 上次步进时间
    uint16_t current_speed; // 声明局部变量用于保存当前速度
    
	if(g_app_context.motor_control_flag == 1 && motor_initialized)
	{
        // 获取当前电机速度设置，确保至少为10ms以解决抖动问题
        Motor_GetStatus(NULL, &current_speed, NULL);
        if(current_speed < 10) {
            current_speed = 10; // 大幅增加最小延时值到10ms
        }
        
        // 检查电机是否被频繁启动（防止过热）
        if(system_time - last_motor_run_time < 500) // 至少间隔500ms
        {
            // 间隔太短，但我们可以执行单步步进
            if(Motor_IsRunning() && (system_time - last_step_time >= current_speed))
            {
                last_step_time = system_time;
                
                // 根据当前训练模式直接执行电机步进
                switch(g_app_context.training_mode)
                {
                    case MODE_LOW_FORCE:
                        Motor_One(current_speed);
                        break;
                        
                    case MODE_MEDIUM_FORCE:
                        Motor_two(current_speed);
                        break;
                        
                    case MODE_HIGH_FORCE:
                        Motor_one_two(current_speed);
                        break;
                        
                    case MODE_ADAPTIVE:
                        Motor_two(current_speed);
                        break;
                        
                    default:
                        break;
                }
                
                // 额外延时确保稳定
                delay_ms(5);
                
                // 更新应用上下文中的电机状态信息
                g_app_context.motor_status = 1;
            }
            return;
        }
        
        // 更新电机运行时间
        last_motor_run_time = system_time;
        last_step_time = system_time;
        
        // 如果还没进行过电机测试，先进行测试
        if(!motor_test_done)
        {
            // 电机测试 - 简单正反转，使用较大的延时值
            Motor_Direction_Angle(1, MOTOR_STEP_FULL, 5, 15);  // 正转5度，延时15ms
            delay_ms(300);  // 增加延时
            Motor_Direction_Angle(0, MOTOR_STEP_FULL, 5, 15);  // 反转5度，延时15ms
            delay_ms(300);  // 增加延时
            motor_test_done = 1;
        }
        
        // 检查模式是否改变
        if(g_app_context.training_mode != last_mode || !Motor_IsRunning())
        {
            // 模式改变或电机未运行，需要重新启动电机
            last_mode = g_app_context.training_mode;
            
            // 根据当前训练模式选择合适的电机控制方式
            switch(g_app_context.training_mode)
            {
                case MODE_LOW_FORCE:
                    // 低阻力模式：使用全步进，速度慢，角度小
                    // 直接调用Direction_Angle替代Start
                    Motor_Direction_Angle(1, MOTOR_STEP_FULL, 45, 20);  // 增加到45度，确保有足够步数，延时20ms
                    break;
                    
                case MODE_MEDIUM_FORCE:
                    // 中等阻力模式：使用半步进，中等速度
                    Motor_Direction_Angle(1, MOTOR_STEP_HALF, 30, 15);  // 30度，半步进模式下会有更多步数，延时增加到15ms
                    break;
                    
                case MODE_HIGH_FORCE:
                    // 高阻力模式：使用八拍步进，高速
                    Motor_Direction_Angle(1, MOTOR_STEP_EIGHT, 60, 10);  // 60度，确保八拍步进有足够步数，延时增加到10ms
                    break;
                    
                case MODE_ADAPTIVE:
                    // 自适应模式：根据训练阶段调整
                    Motor_Direction_Angle(1, MOTOR_STEP_HALF, 45, 15);  // 45度，半步进模式，延时增加到15ms
                    break;
                    
                default:
                    // 默认使用全步进模式
                    Motor_Direction_Angle(1, MOTOR_STEP_FULL, 45, 15);  // 增加到45度，延时增加到15ms
                    break;
            }
            
            // 额外延时确保稳定
            delay_ms(20);
            
            // 更新应用上下文中的电机状态信息
            g_app_context.motor_status = 1;
        }
        else if(Motor_IsRunning())
        {
            // 电机正在运行，直接执行当前模式的步进
            switch(g_app_context.training_mode)
            {
                case MODE_LOW_FORCE:
                    Motor_One(current_speed);
                    break;
                    
                case MODE_MEDIUM_FORCE:
                    Motor_two(current_speed);
                    break;
                    
                case MODE_HIGH_FORCE:
                    Motor_one_two(current_speed);
                    break;
                    
                case MODE_ADAPTIVE:
                    Motor_two(current_speed);
                    break;
                    
                default:
                    break;
            }
            
            // 额外延时确保稳定
            delay_ms(5);
            
            // 获取电机状态，确保变量被读取
            MotorStepMode_t current_mode;
            uint16_t remaining_steps;
            Motor_GetStatus(&current_mode, &current_speed, &remaining_steps);
            
            // 更新应用上下文中的电机状态信息
            g_app_context.motor_status = (remaining_steps > 0) ? 1 : 0;
        }
	}
    else if(g_app_context.motor_control_flag == 0)
    {
        // 确保电机停止
        Motor_Stop();
        motor_test_done = 0; // 重置电机测试标志
    }
    else if(!motor_initialized)
    {
        // 电机未初始化，尝试重新初始化
        Motor_Init();
        motor_initialized = 1;
        motor_error_count++;
        
        if(motor_error_count > 3)
        {
            // 多次初始化失败，可能是硬件问题
            // 这里可以添加错误处理代码
        }
	}
}









