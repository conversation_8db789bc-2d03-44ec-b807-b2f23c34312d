#ifndef __OLED_H
#define __OLED_H

#include "stm32f10x.h"
#include "OLED_Font.h"

// OLED显示模式定义（与原OLED.c中保持一致）
#define OLED_8X16  8   // 8x16字体
#define OLED_6X8   6   // 6x8字体

// 函数声明（与OLED.c中函数定义保持一致）
void OLED_Init(void);                                 // OLED初始化
void OLED_Clear(void);                                // 清屏
void OLED_Update(void);                               // 更新显示
void OLED_UpdateArea(int16_t X, int16_t Y, uint8_t Width, uint8_t Height); // 更新指定区域

// 显存控制函数
void OLED_ClearArea(int16_t X, int16_t Y, uint8_t Width, uint8_t Height);  // 清除指定区域
void OLED_Reverse(void);                                                    // 反转显示
void OLED_ReverseArea(int16_t X, int16_t Y, uint8_t Width, uint8_t Height); // 反转指定区域

// 显示函数
void OLED_ShowChar(int16_t X, int16_t Y, char Char, uint8_t FontSize);
void OLED_ShowString(int16_t X, int16_t Y, char *String, uint8_t FontSize);
void OLED_ShowNum(int16_t X, int16_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowSignedNum(int16_t X, int16_t Y, int32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowHexNum(int16_t X, int16_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowBinNum(int16_t X, int16_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowFloatNum(int16_t X, int16_t Y, double Number, uint8_t IntLength, uint8_t FraLength, uint8_t FontSize);
void OLED_ShowChinese(int16_t X, int16_t Y, char *Chinese);
void OLED_ShowImage(int16_t X, int16_t Y, uint8_t Width, uint8_t Height, const uint8_t *Image);
void OLED_Printf(int16_t X, int16_t Y, uint8_t FontSize, char *format, ...);

// 绘图函数
void OLED_DrawPoint(int16_t X, int16_t Y);
uint8_t OLED_GetPoint(int16_t X, int16_t Y);
void OLED_DrawLine(int16_t X0, int16_t Y0, int16_t X1, int16_t Y1);
void OLED_DrawRectangle(int16_t X, int16_t Y, uint8_t Width, uint8_t Height, uint8_t IsFilled);
void OLED_DrawTriangle(int16_t X0, int16_t Y0, int16_t X1, int16_t Y1, int16_t X2, int16_t Y2, uint8_t IsFilled);
void OLED_DrawCircle(int16_t X, int16_t Y, uint8_t Radius, uint8_t IsFilled);
void OLED_DrawEllipse(int16_t X, int16_t Y, uint8_t A, uint8_t B, uint8_t IsFilled);
void OLED_DrawArc(int16_t X, int16_t Y, uint8_t Radius, int16_t StartAngle, int16_t EndAngle, uint8_t IsFilled);

#endif


/*****************江协科技|版权所有****************/
/*****************jiangxiekeji.com*****************/
