#include "stm32f10x.h"                  // Device header
#include "Delay.h"  // 确保头文件名与实际文件匹配
#include "Motor.h"
#include "ir_sensor.h"  // 包含TrainingMode_t定义
#include <stddef.h>     // 添加此头文件以定义NULL宏

// 如果stddef.h不可用，直接定义NULL宏
#ifndef NULL
#define NULL ((void *)0)
#endif

// 不再在此处定义引脚，而是使用Motor.h中定义的引脚
// 注释掉以前的定义
// #define MOTOR_PIN_A GPIO_Pin_1  // 电机A相 - PA1
// #define MOTOR_PIN_B GPIO_Pin_2  // 电机B相 - PA2
// #define MOTOR_PIN_C GPIO_Pin_3  // 电机C相 - PA3
// #define MOTOR_PIN_D GPIO_Pin_4  // 电机D相 - PA4
// #define MOTOR_PORT GPIOA       // 电机控制端口
// #define MOTOR_ALL_PINS (MOTOR_PIN_A | MOTOR_PIN_B | MOTOR_PIN_C | MOTOR_PIN_D)

// 电机控制宏定义 - 简化引脚操作
#define MOTOR_A_HIGH GPIO_SetBits(MOTOR_PORT, MOTOR_PIN_A)
#define MOTOR_A_LOW GPIO_ResetBits(MOTOR_PORT, MOTOR_PIN_A)

#define MOTOR_B_HIGH GPIO_SetBits(MOTOR_PORT, MOTOR_PIN_B)
#define MOTOR_B_LOW GPIO_ResetBits(MOTOR_PORT, MOTOR_PIN_B)

#define MOTOR_C_HIGH GPIO_SetBits(MOTOR_PORT, MOTOR_PIN_C)
#define MOTOR_C_LOW GPIO_ResetBits(MOTOR_PORT, MOTOR_PIN_C)

#define MOTOR_D_HIGH GPIO_SetBits(MOTOR_PORT, MOTOR_PIN_D)
#define MOTOR_D_LOW GPIO_ResetBits(MOTOR_PORT, MOTOR_PIN_D)

// 电机运行状态变量
static uint8_t motor_running = 0;
static uint16_t remaining_steps = 0;
static MotorStepMode_t current_step_mode = MOTOR_STEP_FULL;
static uint8_t current_speed = 10;

void Motor_Init(void)
{
	// 使能GPIO时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Pin = MOTOR_ALL_PINS;  // 使用定义的引脚掩码
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(MOTOR_PORT, &GPIO_InitStructure);
	
	// 初始化时释放所有引脚（低电平），避免电机通电
	GPIO_ResetBits(MOTOR_PORT, MOTOR_ALL_PINS);
    
    // 初始化电机状态
    motor_running = 0;
    remaining_steps = 0;
}

// 释放所有电机线圈，防止发热
void Motor_Release(void)
{
    GPIO_ResetBits(MOTOR_PORT, MOTOR_ALL_PINS);
    motor_running = 0;
    remaining_steps = 0;
}

// 全步进信号序列A-B-C-D (4拍)
void Motor_One(uint16_t speed)
{
    static uint8_t step = 0;
    
    // 确保延时足够长，最小10ms
    if (speed < 4) {
        speed = 4;  // 大幅增加最小延时值，解决抖动问题
    }
    
    // 每次调用只执行一个步骤，循环执行4拍
    // 直接复制示例代码中的MOTOR_Rhythm_4_1_4函数序列
    switch(step)
    {
        case 0:
            MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_HIGH;
            break;
            
        case 1:
            MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
            break;
            
        case 2:
            MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_HIGH;
            break;
            
        case 3:
            MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
            break;
    }
    
    // 执行延时 - 确保实际延时执行
    delay_ms(speed);
    
    // 更新步骤，循环0-3
    step = (step + 1) % 4;
    
    // 减少一个步进计数
    if(remaining_steps > 0)
        remaining_steps--;
    else
        motor_running = 0;
}

// 半步进信号序列 (4拍)
void Motor_two(uint16_t speed)
{	
    static uint8_t step = 0;
    
    // 确保延时足够长，最小10ms
    if (speed < 10) {
        speed = 10;  // 大幅增加最小延时值，解决抖动问题
    }
    
    // 每次调用只执行一个步骤，循环执行4拍
    // 直接复制示例代码中的MOTOR_Rhythm_4_2_4函数序列
    switch(step)
    {
        case 0:
            MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
            break;
            
        case 1:
            MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
            break;
            
        case 2:
            MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_HIGH;
            break;
            
        case 3:
            MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_LOW;
            break;
    }
    
    // 执行延时 - 确保实际延时执行
    delay_ms(speed);
    
    // 更新步骤，循环0-3
    step = (step + 1) % 4;
    
    // 减少一个步进计数
    if(remaining_steps > 0)
        remaining_steps--;
    else
        motor_running = 0;
}

// 八拍步进信号序列 (8拍)
void Motor_one_two(uint16_t speed)
{
    static uint8_t step = 0;
    
    // 确保延时足够长，最小10ms
    if (speed < 10) {
        speed = 10;  // 大幅增加最小延时值，解决抖动问题
    }
    
    // 每次调用只执行一个步骤，循环执行8拍
    // 直接复制示例代码中的MOTOR_Rhythm_4_1_8函数序列
    switch(step)
    {
        case 0:
            MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_HIGH;
            break;
            
        case 1:
            MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
            break;
            
        case 2:
            MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
            break;
            
        case 3:
            MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_HIGH;
            break;
            
        case 4:
            MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_HIGH;
            break;
            
        case 5:
            MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_LOW;
            break;
            
        case 6:
            MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
            break;
            
        case 7:
            MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
            break;
    }
    
    // 执行延时 - 确保实际延时执行
    delay_ms(speed);
    
    // 更新步骤，循环0-7
    step = (step + 1) % 8;
    
    // 减少一个步进计数
    if(remaining_steps > 0)
        remaining_steps--;
    else
        motor_running = 0;
}

// 新增函数 - 启动电机，指定模式、速度和步数
void Motor_Start(MotorStepMode_t mode, uint8_t speed, uint16_t steps)
{
    current_step_mode = mode;
    current_speed = speed;
    remaining_steps = steps;
    motor_running = 1;
    
    // 直接执行一个步进周期，确保电机立即开始工作
    switch(mode)
    {
        case MOTOR_STEP_FULL:
            Motor_One(speed);
            break;
        case MOTOR_STEP_HALF:
            Motor_two(speed);
            break;
        case MOTOR_STEP_EIGHT:
            Motor_one_two(speed);
            break;
        default:
            break;
    }
}

// 停止电机
void Motor_Stop(void)
{
    motor_running = 0;
    remaining_steps = 0;
    Motor_Release();
}

// 检查电机是否运行中
uint8_t Motor_IsRunning(void)
{
    return motor_running && (remaining_steps > 0);
}

// 电机方向控制函数
void Motor_Direction(uint8_t dir, MotorStepMode_t mode, uint8_t speed)
{
    // 更新当前步进模式和速度
    current_step_mode = mode;
    current_speed = speed;
    
    if(dir) // 正向
    {
        switch(mode)
        {
            case MOTOR_STEP_FULL:
                Motor_One(speed);
                break;
            case MOTOR_STEP_HALF:
                Motor_two(speed);
                break;
            case MOTOR_STEP_EIGHT:
                Motor_one_two(speed);
                break;
            default:
                break;
        }
    }
    else // 反向
    {
        // 反向运动的实现，可以通过改变步进序列的顺序实现
        // 这里简化实现，仅演示正向运动
        switch(mode)
        {
            case MOTOR_STEP_FULL:
                // 反向全步进
                MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_HIGH;
                delay_ms(speed);
                
                MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_LOW;
                delay_ms(speed);
                
                MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_LOW;
                delay_ms(speed);
                
                MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_LOW;
                delay_ms(speed);
                break;
                
            case MOTOR_STEP_HALF:
                // 反向半步进
                MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_HIGH;
                delay_ms(speed);
                
                MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
                delay_ms(speed);
                
                MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
                delay_ms(speed);
                
                MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_LOW;
                delay_ms(speed);
                break;
                
            case MOTOR_STEP_EIGHT:
                // 反向八拍步进 (简化实现)
                Motor_one_two(speed);
                break;
                
            default:
                break;
        }
        
        // 减少一个步进计数
        if(remaining_steps > 0)
            remaining_steps--;
    }
}

// 角度控制函数
void Motor_Direction_Angle(uint8_t dir, MotorStepMode_t mode, uint16_t angle, uint8_t speed)
{
    uint16_t steps;
    
    // 大幅增加最小延时，从4ms增加到10ms
    if (speed < 10) {
        speed = 10; // 设置最小延时为10ms以解决抖动问题
    }
    
    // 更新当前步进模式和速度
    current_step_mode = mode;
    current_speed = speed;
    
    // 使用示例代码中的计算方法：64*angle/45，但增加一个乘数
    steps = (64 * angle) / 45;
    
    // 记录下总步数，用于判断电机运行状态
    remaining_steps = steps;
    motor_running = 1;
    
    // 使用单步控制方式，每次执行一个完整周期的步进
    for(uint16_t i = 0; i < steps; i++)
    {
        if(dir) // 正向
        {
            switch(mode)
            {
                case MOTOR_STEP_FULL: // 全步进
                    // 完整执行4拍序列
                    MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
                    delay_ms(speed);
                    break;
                    
                case MOTOR_STEP_HALF: // 半步进
                    // 完整执行4拍序列
                    MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
                    delay_ms(speed);
                    
                    MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_LOW;
                    delay_ms(speed);
                    break;
                    
                case MOTOR_STEP_EIGHT: // 八拍步进
                    // 完整执行8拍序列
                    MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_LOW;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
                    delay_ms(speed);
                    
                    MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
                    delay_ms(speed);
                    break;
                    
                default:
                    break;
            }
        }
        else // 反向
        {
            switch(mode)
            {
                case MOTOR_STEP_FULL: // 全步进
                    // 反向完整执行4拍序列
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    break;
                    
                case MOTOR_STEP_HALF: // 半步进
                    // 反向完整执行4拍序列
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_LOW;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
                    delay_ms(speed);
                    break;
                    
                case MOTOR_STEP_EIGHT: // 八拍步进
                    // 反向完整执行8拍序列
                    MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_LOW;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    
                    MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_HIGH;
                    delay_ms(speed);
                    break;
                    
                default:
                    break;
            }
        }
    }
    
    // 完成后更新状态
    remaining_steps = 0;
    motor_running = 0;
    // 释放电机线圈，避免发热
    Motor_Release();
}

// 根据训练模式控制电机
void Motor_Control(TrainingMode_t mode)
{
    // 将传入的TrainingMode_t参数转换为对应的模式
    switch(mode)
    {
        case MODE_LOW_FORCE:
            if(!motor_running)
            {
                current_step_mode = MOTOR_STEP_FULL;
                current_speed = 30;
                Motor_Start(current_step_mode, current_speed, 128);  // 低阻力模式 - 全步进，低速度
            }
            else if(Motor_IsRunning())
                Motor_One(current_speed);
            break;
            
        case MODE_MEDIUM_FORCE:
            if(!motor_running)
            {
                current_step_mode = MOTOR_STEP_HALF;
                current_speed = 15;
                Motor_Start(current_step_mode, current_speed, 128);  // 中等阻力模式 - 半步进，中速度
            }
            else if(Motor_IsRunning())
                Motor_two(current_speed);
            break;
            
        case MODE_HIGH_FORCE:
            if(!motor_running)
            {
                current_step_mode = MOTOR_STEP_EIGHT;
                current_speed = 5;
                Motor_Start(current_step_mode, current_speed, 256);  // 高阻力模式 - 八拍步进，高速度
            }
            else if(Motor_IsRunning())
                Motor_one_two(current_speed);
            break;
            
        case MODE_ADAPTIVE:
            if(!motor_running)
            {
                current_step_mode = MOTOR_STEP_HALF;
                current_speed = 10;
                Motor_Start(current_step_mode, current_speed, 128);  // 自适应模式 - 半步进，中速度
            }
            else if(Motor_IsRunning())
                Motor_two(current_speed);
            break;
            
        default:
            // 停止电机
            Motor_Stop();
            break;
    }
    
    // 如果步数用完，停止电机
    if(remaining_steps == 0 && motor_running)
    {
        motor_running = 0;
        Motor_Release();
    }
}

// 获取电机状态函数
void Motor_GetStatus(MotorStepMode_t *mode, uint16_t *speed, uint16_t *steps_remaining)
{
    if(mode != NULL)
        *mode = current_step_mode;
    
    if(speed != NULL)
        *speed = current_speed;  // 注意，这里需要隐式转换uint8_t到uint16_t
    
    if(steps_remaining != NULL)
        *steps_remaining = remaining_steps;
}

// 添加一个简单的测试函数，用于验证电机工作
void Motor_Test(void)
{
    // 初始化前先复位所有引脚状态
    MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_LOW;
    delay_ms(500); // 稍等一下，确保电机稳定
    
    // 1. 全步进模式测试 - 增加测试角度和时间
    MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_LOW; // 确保所有引脚初始状态为低
    delay_ms(500);
    
    // 全步进模式，正向90度
    Motor_Direction_Angle(1, MOTOR_STEP_FULL, 90, 5);
    delay_ms(500);
    
    // 全步进模式，反向90度
    Motor_Direction_Angle(0, MOTOR_STEP_FULL, 90, 5);
    delay_ms(500);
    
    // 2. 半步进模式测试
    MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_LOW;
    delay_ms(500);
    
    // 半步进模式，正向90度
    Motor_Direction_Angle(1, MOTOR_STEP_HALF, 90, 5);
    delay_ms(500);
    
    // 半步进模式，反向90度
    Motor_Direction_Angle(0, MOTOR_STEP_HALF, 90, 5);
    delay_ms(500);
    
    // 3. 八拍步进模式测试
    MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_LOW;
    delay_ms(500);
    
    // 八拍步进模式，正向90度
    Motor_Direction_Angle(1, MOTOR_STEP_EIGHT, 90, 5);
    delay_ms(500);
    
    // 八拍步进模式，反向90度
    Motor_Direction_Angle(0, MOTOR_STEP_EIGHT, 90, 5);
    delay_ms(500);
    
    // 4. 直接使用步进序列测试 - 确保基本步进序列能工作
    MOTOR_A_LOW; MOTOR_B_LOW; MOTOR_C_LOW; MOTOR_D_LOW;
    delay_ms(500);
    
    // 测试全步进序列
    for (int i = 0; i < 32; i++) {
        // 全步进序列
        MOTOR_A_LOW; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_HIGH;
        delay_ms(10);
        MOTOR_A_HIGH; MOTOR_B_LOW; MOTOR_C_HIGH; MOTOR_D_HIGH;
        delay_ms(10);
        MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_LOW; MOTOR_D_HIGH;
        delay_ms(10);
        MOTOR_A_HIGH; MOTOR_B_HIGH; MOTOR_C_HIGH; MOTOR_D_LOW;
        delay_ms(10);
    }
    
    // 确保所有测试完成后电机释放
    Motor_Release();
}
