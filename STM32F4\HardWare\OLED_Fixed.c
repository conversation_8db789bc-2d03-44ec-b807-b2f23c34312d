/**
  ******************************************************************************
  * @file    OLED_Fixed.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   0.96寸I2C OLED驱动实现 - 结合硬件I2C通信和丰富显示功能
  ******************************************************************************
  */

#include "stm32f4xx.h"
#include "stm32f4xx_rcc.h"
#include "stm32f4xx_gpio.h"
#include "stm32f4xx_i2c.h"
#include "Delay.h"
#include "OLED_Fixed.h"
#include <string.h>
#include <math.h>
#include <stdio.h>
#include <stdarg.h>

// 硬件I2C OLED配置 (参考spi_oled.c的成功配置)
#define OLED_I2C                    I2C1
#define OLED_I2C_CLK                RCC_APB1Periph_I2C1
#define OLED_I2C_CLK_INIT           RCC_APB1PeriphClockCmd

// I2C引脚定义（PB6=SCL, PB7=SDA）
#define OLED_SCL_PIN                GPIO_Pin_6      // PB6 - I2C1_SCL
#define OLED_SCL_GPIO_PORT          GPIOB
#define OLED_SCL_GPIO_CLK           RCC_AHB1Periph_GPIOB
#define OLED_SCL_SOURCE             GPIO_PinSource6
#define OLED_SCL_AF                 GPIO_AF_I2C1

#define OLED_SDA_PIN                GPIO_Pin_7      // PB7 - I2C1_SDA
#define OLED_SDA_GPIO_PORT          GPIOB
#define OLED_SDA_GPIO_CLK           RCC_AHB1Periph_GPIOB
#define OLED_SDA_SOURCE             GPIO_PinSource7
#define OLED_SDA_AF                 GPIO_AF_I2C1

// 设备I2C地址（SSD1306常用地址0x3C）
#define OLED_I2C_ADDRESS            0x3C

//==============================================================================
// 全局变量定义
//==============================================================================

/**
  * OLED显存数组
  * 所有的显示函数，都只是对显存数组进行读写
  * 只有调用OLED_Update函数或OLED_UpdateArea函数
  * 才会将显存数组的数据发送到OLED硬件，进行显示
  */
uint8_t OLED_DisplayBuf[8][128];

//==============================================================================
// 硬件I2C通信函数 (参考spi_oled.c的成功实现)
//==============================================================================

/**
  * @brief  硬件I2C初始化
  * @note   使用I2C1, PB6=SCL, PB7=SDA
  */
static void OLED_HW_I2C_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    I2C_InitTypeDef  I2C_InitStructure;

    /* 使能时钟 */
    RCC_AHB1PeriphClockCmd(OLED_SCL_GPIO_CLK | OLED_SDA_GPIO_CLK, ENABLE);
    OLED_I2C_CLK_INIT(OLED_I2C_CLK, ENABLE);

    /* 配置I2C引脚 */
    GPIO_PinAFConfig(OLED_SCL_GPIO_PORT, OLED_SCL_SOURCE, OLED_SCL_AF);
    GPIO_PinAFConfig(OLED_SDA_GPIO_PORT, OLED_SDA_SOURCE, OLED_SDA_AF);

    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;  // 开漏
    GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_UP;

    // SCL
    GPIO_InitStructure.GPIO_Pin = OLED_SCL_PIN;
    GPIO_Init(OLED_SCL_GPIO_PORT, &GPIO_InitStructure);
    // SDA
    GPIO_InitStructure.GPIO_Pin = OLED_SDA_PIN;
    GPIO_Init(OLED_SDA_GPIO_PORT, &GPIO_InitStructure);

    /* 复位I2C外设 */
    RCC_APB1PeriphResetCmd(OLED_I2C_CLK, ENABLE);
    RCC_APB1PeriphResetCmd(OLED_I2C_CLK, DISABLE);

    /* I2C配置 */
    I2C_DeInit(OLED_I2C);
    I2C_InitStructure.I2C_Mode = I2C_Mode_I2C;
    I2C_InitStructure.I2C_DutyCycle = I2C_DutyCycle_2;
    I2C_InitStructure.I2C_OwnAddress1 = 0x00;
    I2C_InitStructure.I2C_Ack = I2C_Ack_Enable;
    I2C_InitStructure.I2C_AcknowledgedAddress = I2C_AcknowledgedAddress_7bit;
    I2C_InitStructure.I2C_ClockSpeed = 400000; // 400kHz
    I2C_Init(OLED_I2C, &I2C_InitStructure);

    I2C_Cmd(OLED_I2C, ENABLE);
}

/**
  * @brief  发送一个字节到OLED (硬件I2C版本)
  * @param  data: 要发送的数据
  * @param  cmd : 1-数据, 0-命令
  */
static void OLED_HW_WriteByte(uint8_t data, uint8_t cmd)
{
    uint8_t control = cmd ? 0x40 : 0x00; // 参考SSD1306手册

    // 等待总线空闲
    while (I2C_GetFlagStatus(OLED_I2C, I2C_FLAG_BUSY));

    I2C_GenerateSTART(OLED_I2C, ENABLE);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_MODE_SELECT));

    I2C_Send7bitAddress(OLED_I2C, OLED_I2C_ADDRESS << 1, I2C_Direction_Transmitter);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED));

    I2C_SendData(OLED_I2C, control);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED));

    I2C_SendData(OLED_I2C, data);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED));

    I2C_GenerateSTOP(OLED_I2C, ENABLE);
}

/**
  * @brief  写命令 (硬件I2C版本)
  */
static void OLED_HW_WriteCommand(uint8_t cmd)
{
    OLED_HW_WriteByte(cmd, 0);
}

/**
  * @brief  写数据 (硬件I2C版本)
  */
static void OLED_HW_WriteData(uint8_t data)
{
    OLED_HW_WriteByte(data, 1);
}

//==============================================================================
// OLED驱动函数
//==============================================================================

/**
  * @brief  OLED初始化
  * @param  None
  * @retval None
  * @note   使用前需要调用此初始化函数
  */
void OLED_Init(void)
{
    // 使用硬件I2C初始化 (参考spi_oled.c的成功实现)
    OLED_HW_I2C_Init();

    // 延时等待OLED稳定
    Delay_us(100000);  // 100ms延时
    
    /*SSD1306标准初始化序列 (参考spi_oled.c的成功配置)*/
    OLED_HW_WriteCommand(0xAE); // 关闭显示
    OLED_HW_WriteCommand(0x20); // Memory Addressing Mode
    OLED_HW_WriteCommand(0x02); // 0x02: Page Addressing Mode (与OLED_Update页写一致)
    OLED_HW_WriteCommand(0xB0);
    OLED_HW_WriteCommand(0xC8);
    OLED_HW_WriteCommand(0x00);
    OLED_HW_WriteCommand(0x10);
    OLED_HW_WriteCommand(0x40);
    OLED_HW_WriteCommand(0x81);
    OLED_HW_WriteCommand(0xFF);
    OLED_HW_WriteCommand(0xA1);
    OLED_HW_WriteCommand(0xA6);
    OLED_HW_WriteCommand(0xA8);
    OLED_HW_WriteCommand(0x3F);
    OLED_HW_WriteCommand(0xA4);
    OLED_HW_WriteCommand(0xD3);
    OLED_HW_WriteCommand(0x00);
    OLED_HW_WriteCommand(0xD5);
    OLED_HW_WriteCommand(0x80);
    OLED_HW_WriteCommand(0xD9);
    OLED_HW_WriteCommand(0xF1);
    OLED_HW_WriteCommand(0xDA);
    OLED_HW_WriteCommand(0x12);
    OLED_HW_WriteCommand(0xDB);
    OLED_HW_WriteCommand(0x40);
    OLED_HW_WriteCommand(0x8D);
    OLED_HW_WriteCommand(0x14);
    OLED_HW_WriteCommand(0xAF); // 开启显示

    OLED_Clear();               // 清空显存数组
    OLED_Update();              // 更新显示，清空屏幕，防止初始化后未显示内容时花屏
}

/**
  * @brief  OLED设置显示光标位置
  * @param  Page 指定光标所在的页，范围：0~7
  * @param  X 指定光标所在的X坐标，范围：0~127
  * @retval None
  * @note   OLED默认的Y轴，只能8个Bit为一组写入，即1页包含8个Y坐标
  */
void OLED_SetCursor(uint8_t Page, uint8_t X)
{
    /*如果使用此程序驱动1.3寸OLED显示屏，需要取消注释*/
    /*因为1.3寸OLED驱动芯片是SH1106，有132列*/
    /*屏幕起始列偏移了2列，起始列是第2列*/
    /*所以需要将X加2，才能正常显示*/
    // X += 2;  // 注释掉SH1106的列偏移，适用于SSD1306

    /*通过指令设置页地址和列地址*/
    OLED_HW_WriteCommand(0xB0 | Page);                    // 设置页位置
    OLED_HW_WriteCommand(0x10 | ((X & 0xF0) >> 4));      // 设置X位置高4位
    OLED_HW_WriteCommand(0x00 | (X & 0x0F));             // 设置X位置低4位
}

/**
  * @brief  OLED清屏
  * @param  None
  * @retval None
  */
void OLED_Clear(void)
{
    uint8_t i, j;
    
    // 清空显存
    for (i = 0; i < 8; i++) {
        for (j = 0; j < 128; j++) {
            OLED_DisplayBuf[i][j] = 0x00;
        }
    }
}

/**
  * @brief  OLED更新显示
  * @param  None
  * @retval None
  */
void OLED_Update(void)
{
    uint8_t i, j;

    for (i = 0; i < 8; i++) {
        OLED_SetCursor(i, 0);    // 设置光标位置为第i页，第0列

        for (j = 0; j < 128; j++) {
            OLED_HW_WriteData(OLED_DisplayBuf[i][j]);
        }
    }
}

/**
  * @brief  OLED显示字符
  * @param  X: X坐标
  * @param  Y: Y坐标
  * @param  Char: 字符
  * @param  FontSize: 字体大小
  * @retval None
  */
void OLED_ShowChar(int16_t X, int16_t Y, char Char, uint8_t FontSize)
{
    uint8_t i, j;
    uint8_t FontIndex;
    const uint8_t *FontData;

    if (X > 127 || Y > 63) return;

    // 计算字符在字体数组中的索引
    if (Char >= 0x20 && Char <= 0x7F) {
        FontIndex = Char - 0x20;
    } else {
        FontIndex = 0; // 默认显示空格
    }

    if (FontSize == OLED_8X16) {
        // 使用8x16字体
        FontData = OLED_F8x16[FontIndex];

        for (i = 0; i < 8; i++) {
            for (j = 0; j < 8; j++) {
                if (FontData[i] & (0x80 >> j)) {
                    OLED_DrawPoint(X + j, Y + i);
                }
            }
        }
        for (i = 0; i < 8; i++) {
            for (j = 0; j < 8; j++) {
                if (FontData[i + 8] & (0x80 >> j)) {
                    OLED_DrawPoint(X + j, Y + i + 8);
                }
            }
        }
    } else if (FontSize == OLED_6X8) {
        // 使用6x8字体
        FontData = OLED_F6x8[FontIndex];

        for (i = 0; i < 6; i++) {
            for (j = 0; j < 8; j++) {
                if (FontData[i] & (0x80 >> j)) {
                    OLED_DrawPoint(X + i, Y + j);
                }
            }
        }
    }
}

/**
  * @brief  OLED显示字符串
  * @param  X: X坐标
  * @param  Y: Y坐标
  * @param  String: 字符串
  * @param  FontSize: 字体大小
  * @retval None
  */
void OLED_ShowString(int16_t X, int16_t Y, char *String, uint8_t FontSize)
{
    uint8_t i;

    for (i = 0; String[i] != '\0'; i++) {
        OLED_ShowChar(X, Y, String[i], FontSize);

        if (FontSize == OLED_8X16) {
            X += 8;
        } else if (FontSize == OLED_6X8) {
            X += 6;
        }

        if (X > 127) break; // 超出屏幕宽度
    }
}

/**
  * @brief  OLED显示数字
  * @param  X: X坐标
  * @param  Y: Y坐标
  * @param  Number: 数字
  * @param  Length: 显示长度
  * @param  FontSize: 字体大小
  * @retval None
  */
void OLED_ShowNum(int16_t X, int16_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize)
{
    uint8_t i;
    uint8_t SingleNumber;

    for (i = 0; i < Length; i++) {
        SingleNumber = Number / pow(10, Length - i - 1);
        OLED_ShowChar(X, Y, SingleNumber % 10 + '0', FontSize);

        if (FontSize == OLED_8X16) {
            X += 8;
        } else if (FontSize == OLED_6X8) {
            X += 6;
        }
    }
}

/**
  * @brief  OLED画点
  * @param  X: X坐标
  * @param  Y: Y坐标
  * @retval None
  */
void OLED_DrawPoint(int16_t X, int16_t Y)
{
    uint8_t Page, Remainder;

    if (X > 127 || Y > 63 || X < 0 || Y < 0) return;

    Page = Y / 8;
    Remainder = Y % 8;

    OLED_DisplayBuf[Page][X] |= 0x01 << Remainder;
}
