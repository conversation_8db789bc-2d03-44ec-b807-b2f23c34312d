## 5. 核心算法与数据处理流程

本系统的核心在于对传感器数据的精确处理和智能分析，以计算关节角度并识别步态模式。本节将详细介绍系统采用的关键算法和数据处理流程。

### 5.1 距离到角度转换算法

关节弯曲角度的计算是系统的基础。本项目采用VL53L0X激光测距传感器结合IMU姿态数据来实现。以下是基于传感器安装位置和几何关系的计算原理。

假设将VL53L0X传感器固定在关节一侧的肢体（例如，大腿）上，并使其瞄准关节另一侧肢体（例如，小腿）上的一个特定标记点。为了简化模型，假设传感器、关节旋转轴和标记点都在同一个平面内进行运动，且传感器和标记点到关节旋转轴的垂直距离是已知且固定的。

**简化二维模型**：

考虑一个二维平面上的膝关节模型。关节旋转轴为O点。传感器S固定在大腿上，距离O点为 `Rs`。标记点P固定在小腿上，距离O点为 `Rp`。VL53L0X测量的是S到P的直线距离 `d_SP`。关节的弯曲角度 `θ` 是OS和OP向量之间的夹角。

根据余弦定理，在三角形OSP中：
`d_SP² = Rs² + Rp² - 2 * Rs * Rp * cos(θ)`

由此，可以解出关节角度 `θ` 的余弦值：
`cos(θ) = (Rs² + Rp² - d_SP²) / (2 * Rs * Rp)`

最终，关节角度 `θ` 为：
`θ = arccos( (Rs² + Rp² - d_SP²) / (2 * Rs * Rp) )`

其中，`Rs` 和 `Rp` 是需要通过人体测量和设备安装位置精确确定的常数。

**标定步骤**：

为了获得精确的 `Rs` 和 `Rp` 值以及验证模型的准确性，需要进行以下标定：

1.  **设备固定**：将设备和标记点固定在用户肢体上预定的位置。
2.  **多角度测量**：使用一个标准的角度测量工具（如量角器或运动捕捉系统）测量关节的真实角度 `θ_real`。
3.  **数据采集**：在多个不同的关节角度 `θ_real` 下，同时记录VL53L0X测量的距离 `d_SP`。
4.  **参数拟合**：利用最小二乘法或其他优化方法，根据采集到的多组 `(θ_real, d_SP)` 数据，拟合出最优的 `Rs` 和 `Rp` 值，使 `arccos( (Rs² + Rp² - d_SP²) / (2 * Rs * Rp) )` 的计算结果最接近 `θ_real`。
5.  **补偿曲线**：如果简单的余弦定理模型存在系统误差（例如由于关节轴偏移或传感器/标记点不在同一平面），可以建立一个补偿查找表或拟合一条补偿曲线，将基于余弦定理计算出的角度修正为更准确的值。

**考虑三维运动与IMU辅助**：

上述二维模型是简化的。在实际步态过程中，关节会进行三维复杂运动，传感器和标记点可能不在关节旋转平面内，且设备会发生倾斜。IMU传感器的数据可以用来辅助修正这些影响。

1.  **姿态修正**：通过IMU获取佩戴设备的精确三维姿态（欧拉角或四元数）。如果VL53L0X传感器的朝向相对于佩戴肢体是固定的，可以通过肢体的姿态来估计传感器的朝向。然后，将VL53L0X测量的距离投影到关节的旋转平面上，或者在三维空间中计算点P相对于点S的坐标，再结合关节轴线的方向信息（可能也需要IMU数据或额外的标定）来计算关节角度。这需要更复杂的空间几何计算和坐标变换。

2.  **运动补偿**：快速的肢体运动会产生运动加速度，影响加速度计的读数。陀螺仪数据不受线性加速度影响，可以提供更准确的瞬时角速度信息。利用IMU融合算法可以获得平滑的姿态变化，从而辅助修正VL53L0X的距离测量值（例如，在快速运动时适当降低VL53L0X数据的权重）。

本项目初步阶段可以先实现基于二维模型的计算，并在后续通过IMU数据进行高级修正。

### 5.2 传感器数据融合算法 (IMU + VL53L0X)

为了提高关节角度测量的精度和鲁棒性，需要融合来自VL53L0X和IMU传感器的数据。两种常用的融合方法是互补滤波和卡尔曼滤波。

#### 5.2.1 互补滤波

互补滤波是一种简单有效的传感器数据融合方法，尤其适用于融合高频和低频特性不同的数据源。

*   **VL53L0X数据特性**：提供关节弯曲角度的"绝对"测量值（相对于传感器和标记点的固定位置）。受环境光、目标表面特性、传感器微小位移等影响，可能存在瞬时噪声和跳变，但长期内相对稳定（无漂移）。
*   **IMU数据特性**：通过陀螺仪积分得到的角度变化具有很好的短期精度和快速响应，但长期积分会产生漂移。通过加速度计和磁力计可以提供重力方向和北方方向参考，用于校正姿态，但易受运动加速度和磁场干扰。

互补滤波的基本思想是：
- **高频部分**：相信陀螺仪积分得到的角度变化。
- **低频部分**：相信VL53L0X计算出的角度（作为绝对参考）或由IMU加速度计/磁力计计算出的倾角（作为低频参考）。

融合公式：
`Fused_Angle(t) = α * (Fused_Angle(t-1) + Gyro_Angular_Velocity * Δt) + (1 - α) * VL53L0X_Calculated_Angle`
或者：
`Fused_Angle(t) = α * (Fused_Angle(t-1) + Gyro_Angular_Velocity * Δt) + (1 - α) * IMU_Accelerometer_Magnetometer_Angle`

其中：
- `Fused_Angle(t)`：当前时刻融合后的关节角度
- `Fused_Angle(t-1)`：上一时刻融合后的关节角度
- `Gyro_Angular_Velocity`：由IMU陀螺仪测量的关节旋转角速度
- `Δt`：采样时间间隔
- `VL53L0X_Calculated_Angle`：由VL53L0X距离计算出的关节角度
- `IMU_Accelerometer_Magnetometer_Angle`：仅由IMU加速度计和磁力计计算出的关节倾角或相对角度
- `α`：滤波系数，通常接近1（如0.98）。`α` 值越大，越相信陀螺仪；`1-α` 越小，越相信绝对参考。`α` 的选择取决于陀螺仪和绝对参考源的噪声特性。

**优点**：算法简单，计算量小，易于在资源有限的MCU上实现。
**缺点**：滤波效果依赖于参数 `α` 的选取，对噪声模型建模不精确。

#### 5.2.2 卡尔曼滤波 (或扩展卡尔曼滤波 EKF)

卡尔曼滤波是一种最优线性估计算法，能够根据系统的动力学模型和测量模型，融合带有噪声的传感器数据，得到最优的状态估计。对于非线性系统，可以使用扩展卡尔曼滤波 (EKF)。关节运动是非线性的，因此更适合使用EKF。

**EKF基本思想**：

EKF包含两个主要步骤：

1.  **预测步**：根据系统的状态模型（例如，关节角度和角速度如何随时间变化）和上一时刻的最优状态估计，预测当前时刻的状态及其协方差。
    `x_k|k-1 = f(x_k-1|k-1, u_k)` (状态预测)
    `P_k|k-1 = F_k * P_k-1|k-1 * F_k^T + Q_k` (协方差预测)

2.  **更新步**：根据当前时刻的传感器测量值（VL53L0X角度和IMU数据），利用测量模型，修正预测的状态和协方差，得到当前时刻的最优状态估计。
    `y_k = z_k - h(x_k|k-1)` (测量残差)
    `S_k = H_k * P_k|k-1 * H_k^T + R_k` (残差协方差)
    `K_k = P_k|k-1 * H_k^T * S_k^-1` (卡尔曼增益)
    `x_k|k = x_k|k-1 + K_k * y_k` (状态更新)
    `P_k|k = (I - K_k * H_k) * P_k|k-1` (协方差更新)

其中：
- `x`：系统状态向量（例如，关节角度、关节角速度）
- `u`：控制输入（本系统中可能没有直接的控制输入）
- `z`：传感器测量向量（VL53L0X角度、IMU加速度、陀螺仪、磁力计）
- `f`：状态转移函数（描述状态如何随时间演化）
- `h`：测量函数（描述传感器测量如何由状态决定）
- `F`：状态转移矩阵的雅可比矩阵
- `H`：测量矩阵的雅可比矩阵
- `P`：状态协方差矩阵
- `Q`：过程噪声协方差矩阵
- `R`：测量噪声协方差矩阵
- `K`：卡尔曼增益
- `I`：单位矩阵

**优点**：理论上提供最优估计，能够更好地处理噪声和不确定性，对系统模型有更深的理解。
**缺点**：算法复杂，计算量大，需要精确的系统动力学模型和噪声模型，参数调优困难。

**本项目选择**：

考虑到STM32F103C8T6的资源限制，初步阶段建议采用**互补滤波**实现传感器数据融合，因为它计算简单，易于实现。在满足基本功能后，如果对精度有更高要求且MCU性能允许，可以考虑优化并移植**扩展卡尔曼滤波**算法。

### 5.3 步态模式识别算法

步态模式识别旨在根据采集和处理后的关节角度数据，判断用户当前的步态状态属于正常、调整还是异常。本系统采用基于关节角度阈值判断的简单状态机方法。

**核心特征**：关节弯曲角度及其变化速度。

**步态状态机**：

可以设计一个简单的步态状态机来管理模式判断：

*   **STATE_NORMAL_GAIT**：正常步态状态。
*   **STATE_NEED_ADJUST**：需要调整状态。
*   **STATE_WARNING**：警告状态。

**模式判断规则**：

1.  **进入 STATE_NORMAL_GAIT**：
    *   当前关节角度 `θ` 满足 `θ_min_normal <= θ <= θ_max_normal`。
    *   关节角速度 `ω` 满足 `|ω| <= ω_max_normal`。
    *   系统处于运行模式。

2.  **从 STATE_NORMAL_GAIT 进入 STATE_NEED_ADJUST**：
    *   当前关节角度 `θ` 偏离正常范围，例如 `θ < θ_min_normal - Δθ_adjust` 或 `θ > θ_max_normal + Δθ_adjust`。
    *   或者，关节角速度 `ω` 异常，例如 `|ω| > ω_max_adjust`。
    *   这些偏离是轻微或暂时的。

3.  **从 STATE_NEED_ADJUST 进入 STATE_WARNING**：
    *   当前关节角度 `θ` 严重偏离正常范围，例如 `θ < θ_min_normal - Δθ_warning` 或 `θ > θ_max_normal + Δθ_warning`，其中 `Δθ_warning > Δθ_adjust`。
    *   或者，关节角速度 `ω` 严重异常，例如 `|ω| > ω_max_warning`。
    *   或者，在 STATE_NEED_ADJUST 状态持续时间超过阈值 `T_adjust_max` 仍未改善。

4.  **从 STATE_NEED_ADJUST 或 STATE_WARNING 返回 STATE_NORMAL_GAIT**：
    *   当前关节角度 `θ` 和角速度 `ω` 重新回到正常步态的判断范围内，并持续一段时间 `T_recovery_min`。

**阈值参数**：

*   `θ_min_normal`, `θ_max_normal`：正常步态关节角度的上下限。需要根据不同关节、不同用户和不同步态类型进行标定和设置。
*   `Δθ_adjust`, `Δθ_warning`：角度偏离正常范围进入调整或警告状态的阈值。
*   `ω_max_normal`, `ω_max_adjust`, `ω_max_warning`：不同模式下关节角速度的阈值。
*   `T_adjust_max`：在调整模式下维持的最大时间，超过则升级为警告。
*   `T_recovery_min`：回到正常范围后，维持多久才确认恢复正常。

这些阈值参数应设计为用户可配置，以适应个性化需求和康复/训练进程。可以在系统设置界面中通过按键进行调整。

**基于步态周期的模式识别**（可选高级功能）：

更精确的步态模式识别可以结合步态周期检测，分析步态周期不同阶段的关节运动特征。

1.  **步态周期检测**：利用IMU数据（如踝关节处垂直加速度的峰值和谷值）识别一个完整的步态周期（例如，右脚触地 → 右脚离地 → 左脚触地 → 左脚离地 → 右脚触地）。
2.  **特征分析**：在识别出的每个步态周期内，提取关键特征，例如：
    *   支撑相和摆动相的持续时间比例
    *   关节在支撑相和摆动相的最大/最小弯曲角度
    *   关节角度的最大角速度
    *   触地和离地时的关节角度
3.  **模式分类**：将这些特征输入一个分类器，如决策树、支持向量机(SVM)或简单的规则集，判断当前步态周期是否正常、异常或属于某种特定的异常模式（如剪刀步、慌张步态等）。

这种基于步态周期的分析可以提供更全面的步态评估，但需要更复杂的算法和更强的MCU计算能力。

**本项目选择**：

初期采用基于关节角度和角速度阈值的简单状态机方法，实现基本的正常、调整、警告模式识别。后续可以逐步增加步态周期检测和更复杂的特征分析，提高识别的准确性和鲁棒性。

### 5.4 数据处理流程总结

整个数据处理流程可以概括如下：

```mermaid
graph TD
    A[VL53L0X原始距离] --> B[距离预处理];
    C[IMU原始数据] --> D[IMU预处理];
    D --> E[姿态解算];
    B --> F[距离到角度];
    E --> G[IMU角度修正];
    F --> H[传感器数据融合];
    G --> H;
    H --> I[融合后关节角度];
    I --> J[特征提取];
    J --> K[步态模式识别];
    K --> L[反馈生成];
    I --> M[数据显示];
    L --> N[反馈执行];
    N --> O[用户感知];
    M --> O;
```

1.  **原始数据采集**：通过I2C/SPI接口从VL53L0X和IMU周期性读取原始数据。
2.  **传感器预处理**：对原始数据进行单位转换、零偏补偿、滤波等。
3.  **姿态解算**：利用IMU预处理后的数据运行融合算法，计算设备的三维姿态。
4.  **距离到角度计算**：根据VL53L0X距离和安装几何模型初步计算关节角度。
5.  **IMU角度修正**：利用IMU姿态数据对VL53L0X计算的角度进行修正（可选，如果采用更高级模型）。
6.  **传感器数据融合**：融合VL53L0X计算的角度和IMU辅助的角度（或IMU自身计算的相对角度），得到更准确、平滑的关节角度。
7.  **特征提取**：从融合后的关节角度数据提取步态分析所需的特征（角度值、速度、加速度）。
8.  **步态模式识别**：基于提取的特征，应用阈值判断或更复杂的分类算法，识别当前步态模式。
9.  **反馈生成**：根据步态模式和系统状态生成相应的反馈指令（LED、电机、语音）。
10. **数据显示**：更新OLED屏幕显示内容。
11. **反馈执行**：驱动LED、步进电机、语音模块执行反馈。
12. **用户感知**：用户接收到反馈，可能调整步态。
13. **数据记录**（可选）：将关键处理结果保存到存储介质。

整个流程需要在一个设定的采样周期内（例如10ms，对应100Hz采样率）完成，以确保系统的实时性。

### 5.5 校准流程详细设计

系统的测量精度很大程度上依赖于准确的传感器校准。除了IMU的自身校准（已在硬件设计部分提及），还需要进行关节角度测量的整体校准。

**关节角度校准流程**：

1.  **进入校准模式**：用户通过按键进入校准界面，选择"关节角度校准"。
2.  **零位校准**：
    *   系统提示用户将关节置于参考零位（如腿部完全伸直）。
    *   用户按下"确认"键。
    *   系统采集VL53L0X和IMU数据，记录当前状态下的传感器原始读数和IMU姿态，计算并存储为零位参考。
    *   系统提示校准成功。
3.  **多点校准**（可选，用于建立角度-距离映射）：
    *   系统提示用户将关节弯曲至特定角度（如90度，可使用量角器辅助）。
    *   用户按下"确认"键。
    *   系统采集传感器数据，记录当前状态，与已知角度值关联。
    *   重复该步骤采集多个角度的数据点。
4.  **参数计算与存储**：
    *   根据采集的零位和多点数据，计算距离到角度转换模型的参数（如Rs, Rp）或生成补偿查找表。
    *   将校准参数存储在MCU的内部Flash或外部存储器中，确保掉电不丢失。
5.  **退出校准**：用户按下"返回"键退出校准模式，系统加载新的校准参数并进入运行状态。

**IMU校准集成**：

IMU的加速度计、陀螺仪、磁力计校准可以在系统启动后自动进行（陀螺仪零偏校准）或在用户进入校准模式时引导用户完成（加速度计、磁力计校准）。这些校准参数也需要存储并加载。

**校准数据管理**：

- 校准参数应保存在非易失性存储器中。
- 提供恢复默认校准参数的选项。
- 每次系统启动时加载存储的校准参数。
- 考虑不同用户存储不同的校准配置文件。

准确的校准是系统测量精度的重要保障，需要用户按照提示认真完成。软件应提供清晰的校准指导界面和进度提示。 