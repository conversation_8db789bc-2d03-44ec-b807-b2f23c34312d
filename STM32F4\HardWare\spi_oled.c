/**
  ******************************************************************************
  * @file    spi_oled.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   0.96寸SPI OLED驱动实现 (SSD1306控制器) - STM32F4版本
  ******************************************************************************
  */

#include "spi_oled.h"
#include <string.h>
#include "stm32f4xx_i2c.h"  // 新增I2C头文件
#include "main.h"  // 获取GetTick及Delay支持

//==============================================================================
// 全局变量定义
//==============================================================================

// OLED显存数组
uint8_t OLED_DisplayBuf[8][128];

// 8x16字体数据 (ASCII 0x20-0x7F)
const uint8_t OLED_F8x16[][16] = {
    // 空格 (0x20)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // ! (0x21)
    {0x00,0x00,0x00,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x33,0x30,0x00,0x00,0x00},
    // " (0x22)
    {0x00,0x10,0x0C,0x06,0x10,0x0C,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // # (0x23)
    {0x40,0xC0,0x78,0x40,0xC0,0x78,0x40,0x00,0x04,0x3F,0x04,0x04,0x3F,0x04,0x04,0x00},
    // $ (0x24)
    {0x00,0x70,0x88,0xFC,0x08,0x30,0x00,0x00,0x00,0x18,0x20,0x20,0x3F,0x21,0x1E,0x00},
    // % (0x25)
    {0xF0,0x08,0xF0,0x00,0xE0,0x18,0x00,0x00,0x00,0x21,0x1C,0x03,0x1E,0x21,0x1E,0x00},
    // & (0x26)
    {0x00,0xF0,0x08,0x88,0x70,0x00,0x00,0x00,0x1E,0x21,0x23,0x24,0x19,0x27,0x21,0x10},
    // ' (0x27)
    {0x10,0x16,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // ( (0x28)
    {0x00,0x00,0x00,0xE0,0x18,0x04,0x02,0x00,0x00,0x00,0x00,0x07,0x18,0x20,0x40,0x00},
    // ) (0x29)
    {0x00,0x02,0x04,0x18,0xE0,0x00,0x00,0x00,0x00,0x40,0x20,0x18,0x07,0x00,0x00,0x00},
    // * (0x2A)
    {0x40,0x40,0x80,0xF0,0x80,0x40,0x40,0x00,0x02,0x02,0x01,0x0F,0x01,0x02,0x02,0x00},
    // + (0x2B)
    {0x00,0x00,0x00,0xF0,0x00,0x00,0x00,0x00,0x01,0x01,0x01,0x1F,0x01,0x01,0x01,0x00},
    // , (0x2C)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xB0,0x70,0x00,0x00,0x00,0x00,0x00},
    // - (0x2D)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x01},
    // . (0x2E)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x00,0x00},
    // / (0x2F)
    {0x00,0x00,0x00,0x00,0x80,0x60,0x18,0x04,0x00,0x60,0x18,0x06,0x01,0x00,0x00,0x00},
    // 其他字符省略...
    // 0 (0x30)
    {0x00,0x00,0xE0,0x10,0x08,0x08,0x10,0xE0,0x00,0x00,0x0F,0x10,0x20,0x20,0x10,0x0F},
    // 1 (0x31)
    {0x00,0x00,0x10,0x10,0xF8,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x3F,0x20,0x20,0x00},
    // 2 (0x32)
    {0x00,0x00,0x70,0x08,0x08,0x08,0x88,0x70,0x00,0x00,0x30,0x28,0x24,0x22,0x21,0x30},
    // 3 (0x33)
    {0x00,0x00,0x30,0x08,0x88,0x88,0x48,0x30,0x00,0x00,0x18,0x20,0x20,0x20,0x11,0x0E},
    // 4 (0x34)
    {0x00,0x00,0x00,0xC0,0x20,0x10,0xF8,0x00,0x00,0x00,0x07,0x04,0x24,0x24,0x3F,0x24},
    // 5 (0x35)
    {0x00,0x00,0xF8,0x08,0x88,0x88,0x08,0x08,0x00,0x00,0x19,0x21,0x20,0x20,0x11,0x0E},
    // 6 (0x36)
    {0x00,0x00,0xE0,0x10,0x88,0x88,0x18,0x00,0x00,0x00,0x0F,0x11,0x20,0x20,0x11,0x0E},
    // 7 (0x37)
    {0x00,0x00,0x38,0x08,0x08,0xC8,0x38,0x08,0x00,0x00,0x00,0x00,0x3F,0x00,0x00,0x00},
    // 8 (0x38)
    {0x00,0x00,0x70,0x88,0x08,0x08,0x88,0x70,0x00,0x00,0x1C,0x22,0x21,0x21,0x22,0x1C},
    // 9 (0x39)
    {0x00,0x00,0xE0,0x10,0x08,0x08,0x10,0xE0,0x00,0x00,0x00,0x31,0x22,0x22,0x11,0x0F},
    // : (0x3A)
    {0x00,0x00,0x00,0x00,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00},
    // A (0x41)
    {0x00,0x00,0x00,0xC0,0x38,0xE0,0x00,0x00,0x00,0x20,0x3C,0x23,0x02,0x02,0x27,0x38},
    // C (0x43)
    {0x00,0x00,0xF0,0x08,0x08,0x08,0x08,0x10,0x00,0x00,0x0F,0x10,0x20,0x20,0x20,0x10},
    // D (0x44)
    {0x00,0x00,0x08,0xF8,0x08,0x08,0x08,0xF0,0x00,0x00,0x20,0x3F,0x20,0x20,0x20,0x1F},
    // M (0x4D)
    {0x00,0x08,0xF8,0x08,0xC0,0x00,0x08,0xF8,0x08,0x00,0x20,0x3F,0x20,0x00,0x07,0x18},
    // T (0x54)
    {0x00,0x18,0x08,0x08,0xF8,0x08,0x08,0x18,0x00,0x00,0x00,0x20,0x20,0x3F,0x20,0x20},
    // a (0x61)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x19,0x24,0x22,0x22,0x22,0x3F},
    // e (0x65)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x12,0x22,0x22,0x22,0x13},
    // i (0x69)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x36,0x21,0x00,0x00},
    // n (0x6E)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x3F,0x21,0x01,0x01,0x3E},
    // o (0x6F)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x11,0x21,0x21,0x11,0x0E},
    // r (0x72)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x3F,0x21,0x01,0x01,0x03},
    // s (0x73)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x19,0x22,0x22,0x22,0x22,0x0C},
    // t (0x74)
    {0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x3F,0x21,0x21,0x00},
    // x (0x78)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x21,0x16,0x08,0x14,0x22,0x21}
};

//==============================================================================
// 低级驱动函数
//==============================================================================

/**
  * @brief  I2C初始化
  * @note   使用I2C1, PB6=SCL, PB7=SDA
  */
static void OLED_I2C_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    I2C_InitTypeDef  I2C_InitStructure;

    /* 使能时钟 */
    RCC_AHB1PeriphClockCmd(OLED_SCL_GPIO_CLK | OLED_SDA_GPIO_CLK, ENABLE);
    OLED_I2C_CLK_INIT(OLED_I2C_CLK, ENABLE);

    /* 配置I2C引脚 */
    GPIO_PinAFConfig(OLED_SCL_GPIO_PORT, OLED_SCL_SOURCE, OLED_SCL_AF);
    GPIO_PinAFConfig(OLED_SDA_GPIO_PORT, OLED_SDA_SOURCE, OLED_SDA_AF);

    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;  // 开漏
    GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_UP;

    // SCL
    GPIO_InitStructure.GPIO_Pin = OLED_SCL_PIN;
    GPIO_Init(OLED_SCL_GPIO_PORT, &GPIO_InitStructure);
    // SDA
    GPIO_InitStructure.GPIO_Pin = OLED_SDA_PIN;
    GPIO_Init(OLED_SDA_GPIO_PORT, &GPIO_InitStructure);

    /* 复位I2C外设 */
    RCC_APB1PeriphResetCmd(OLED_I2C_CLK, ENABLE);
    RCC_APB1PeriphResetCmd(OLED_I2C_CLK, DISABLE);

    /* I2C配置 */
    I2C_DeInit(OLED_I2C);
    I2C_InitStructure.I2C_Mode = I2C_Mode_I2C;
    I2C_InitStructure.I2C_DutyCycle = I2C_DutyCycle_2;
    I2C_InitStructure.I2C_OwnAddress1 = 0x00;
    I2C_InitStructure.I2C_Ack = I2C_Ack_Enable;
    I2C_InitStructure.I2C_AcknowledgedAddress = I2C_AcknowledgedAddress_7bit;
    I2C_InitStructure.I2C_ClockSpeed = 400000; // 400kHz, 根据需要可改为100kHz
    I2C_Init(OLED_I2C, &I2C_InitStructure);

    I2C_Cmd(OLED_I2C, ENABLE);
}

/**
  * @brief  发送一个字节到OLED
  * @param  data: 要发送的数据
  * @param  cmd : 1-数据, 0-命令
  */
void OLED_WriteByte(uint8_t data, uint8_t cmd)
{
    uint8_t control = cmd ? 0x40 : 0x00; // 参考SSD1306手册

    // 等待总线空闲
    while (I2C_GetFlagStatus(OLED_I2C, I2C_FLAG_BUSY));

    I2C_GenerateSTART(OLED_I2C, ENABLE);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_MODE_SELECT));

    I2C_Send7bitAddress(OLED_I2C, OLED_I2C_ADDRESS << 1, I2C_Direction_Transmitter);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED));

    I2C_SendData(OLED_I2C, control);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED));

    I2C_SendData(OLED_I2C, data);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED));

    I2C_GenerateSTOP(OLED_I2C, ENABLE);
}

/**
  * @brief  写命令
  */
void OLED_WriteCmd(uint8_t cmd)
{
    OLED_WriteByte(cmd, 0);
}

/**
  * @brief  写数据
  */
void OLED_WriteData(uint8_t data)
{
    OLED_WriteByte(data, 1);
}

/**
  * @brief  延时函数 (简单阻塞)
  */
void OLED_Delay_ms(uint32_t ms)
{
    uint32_t tick = GetTick();
    while ((GetTick() - tick) < ms)
        __NOP();
}

//==============================================================================
// 高级驱动函数改动：将SPI初始化改为I2C初始化，移除片选等操作
//==============================================================================

/**
  * @brief  OLED初始化
  * @param  None
  * @retval None
  */
void OLED_Init(void)
{
    // 初始化I2C
    OLED_I2C_Init();

    // I2C版本的模块通常上电自动复位，这里可以保留软件复位为空操作
    OLED_Delay_ms(100);

    // 初始化序列与原SPI版本保持一致
    OLED_WriteCmd(0xAE); // 关闭显示
    OLED_WriteCmd(0x20); // Memory Addressing Mode
    OLED_WriteCmd(0x02); // 0x02: Page Addressing Mode (与OLED_Update页写一致)
    OLED_WriteCmd(0xB0);
    OLED_WriteCmd(0xC8);
    OLED_WriteCmd(0x00);
    OLED_WriteCmd(0x10);
    OLED_WriteCmd(0x40);
    OLED_WriteCmd(0x81);
    OLED_WriteCmd(0xFF);
    OLED_WriteCmd(0xA1);
    OLED_WriteCmd(0xA6);
    OLED_WriteCmd(0xA8);
    OLED_WriteCmd(0x3F);
    OLED_WriteCmd(0xA4);
    OLED_WriteCmd(0xD3);
    OLED_WriteCmd(0x00);
    OLED_WriteCmd(0xD5);
    OLED_WriteCmd(0x80);
    OLED_WriteCmd(0xD9);
    OLED_WriteCmd(0xF1);
    OLED_WriteCmd(0xDA);
    OLED_WriteCmd(0x12);
    OLED_WriteCmd(0xDB);
    OLED_WriteCmd(0x40);
    OLED_WriteCmd(0x8D);
    OLED_WriteCmd(0x14);
    OLED_WriteCmd(0xAF);

    OLED_Clear();
}

/**
  * @brief  清屏
  * @param  None
  * @retval None
  */
void OLED_Clear(void)
{
    uint8_t i, j;

    // 清空显存
    for (i = 0; i < 8; i++) {
        for (j = 0; j < 128; j++) {
            OLED_DisplayBuf[i][j] = 0x00;
        }
    }
}

/**
  * @brief  更新显示
  * @param  None
  * @retval None
  */
void OLED_Update(void)
{
    uint8_t i, j;

    for (i = 0; i < 8; i++) {
        OLED_WriteCmd(0xB0 + i);    // 设置页地址
        OLED_WriteCmd(0x02);        // 设置低列地址 (SH1106 左侧偏移2)
        OLED_WriteCmd(0x10);        // 设置高列地址

        for (j = 0; j < 128; j++) {
            OLED_WriteData(OLED_DisplayBuf[i][j]);
        }
        /* 额外写入2个0，清除 SH1106 132列中未用的最后2列，避免右侧竖条 */
        OLED_WriteData(0x00);
        OLED_WriteData(0x00);
    }
}

/**
  * @brief  显示字符
  * @param  X: X坐标
  * @param  Y: Y坐标
  * @param  Char: 字符
  * @param  FontSize: 字体大小
  * @retval None
  */
void OLED_ShowChar(int16_t X, int16_t Y, char Char, uint8_t FontSize)
{
    uint8_t i, j;
    uint8_t FontIndex;

    if (X > 127 || Y > 63) return;

    // 计算字符在字体数组中的索引
    if (Char >= 0x20 && Char <= 0x7F) {
        FontIndex = Char - 0x20;
    } else {
        FontIndex = 0; // 默认显示空格
    }

    if (FontSize == OLED_8X16) {
        // 8x16字体
        for (i = 0; i < 16; i++) {
            uint8_t temp = OLED_F8x16[FontIndex][i];
            for (j = 0; j < 8; j++) {
                if (temp & (0x80 >> j)) {
                    if (X + j < 128 && Y + i < 64) {
                        OLED_DisplayBuf[(Y + i) / 8][X + j] |= (1 << ((Y + i) % 8));
                    }
                } else {
                    if (X + j < 128 && Y + i < 64) {
                        OLED_DisplayBuf[(Y + i) / 8][X + j] &= ~(1 << ((Y + i) % 8));
                    }
                }
            }
        }
    }
}

/**
  * @brief  显示字符串
  * @param  X: X坐标
  * @param  Y: Y坐标
  * @param  String: 字符串
  * @param  FontSize: 字体大小
  * @retval None
  */
void OLED_ShowString(int16_t X, int16_t Y, char *String, uint8_t FontSize)
{
    uint8_t i = 0;

    while (String[i] != '\0') {
        OLED_ShowChar(X + i * 8, Y, String[i], FontSize);
        i++;
        if (X + i * 8 >= 128) break; // 防止超出屏幕
    }
}

/**
  * @brief  显示数字
  * @param  X: X坐标
  * @param  Y: Y坐标
  * @param  Number: 数字
  * @param  Length: 长度
  * @param  FontSize: 字体大小
  * @retval None
  */
void OLED_ShowNum(int16_t X, int16_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize)
{
    uint8_t i;
    uint32_t temp = Number;

    for (i = 0; i < Length; i++) {
        OLED_ShowChar(X + (Length - 1 - i) * 8, Y, temp % 10 + '0', FontSize);
        temp /= 10;
    }
}
