.\objects\spi.o: Hardware\SPI.c
.\objects\spi.o: .\User\stm32f4xx.h
.\objects\spi.o: .\Start\core_cm4.h
.\objects\spi.o: E:\Keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\spi.o: .\Start\core_cmInstr.h
.\objects\spi.o: .\Start\core_cmFunc.h
.\objects\spi.o: .\Start\core_cmSimd.h
.\objects\spi.o: .\User\system_stm32f4xx.h
.\objects\spi.o: .\User\stm32f4xx_conf.h
.\objects\spi.o: .\Library\inc\stm32f4xx_adc.h
.\objects\spi.o: .\User\stm32f4xx.h
.\objects\spi.o: .\Library\inc\stm32f4xx_crc.h
.\objects\spi.o: .\Library\inc\stm32f4xx_dbgmcu.h
.\objects\spi.o: .\Library\inc\stm32f4xx_dma.h
.\objects\spi.o: .\Library\inc\stm32f4xx_exti.h
.\objects\spi.o: .\Library\inc\stm32f4xx_flash.h
.\objects\spi.o: .\Library\inc\stm32f4xx_gpio.h
.\objects\spi.o: .\Library\inc\stm32f4xx_i2c.h
.\objects\spi.o: .\Library\inc\stm32f4xx_iwdg.h
.\objects\spi.o: .\Library\inc\stm32f4xx_pwr.h
.\objects\spi.o: .\Library\inc\stm32f4xx_rcc.h
.\objects\spi.o: .\Library\inc\stm32f4xx_rtc.h
.\objects\spi.o: .\Library\inc\stm32f4xx_sdio.h
.\objects\spi.o: .\Library\inc\stm32f4xx_spi.h
.\objects\spi.o: .\Library\inc\stm32f4xx_syscfg.h
.\objects\spi.o: .\Library\inc\stm32f4xx_tim.h
.\objects\spi.o: .\Library\inc\stm32f4xx_usart.h
.\objects\spi.o: .\Library\inc\stm32f4xx_wwdg.h
.\objects\spi.o: .\Library\inc\misc.h
.\objects\spi.o: .\Library\inc\stm32f4xx_cryp.h
.\objects\spi.o: .\Library\inc\stm32f4xx_hash.h
.\objects\spi.o: .\Library\inc\stm32f4xx_rng.h
.\objects\spi.o: .\Library\inc\stm32f4xx_can.h
.\objects\spi.o: .\Library\inc\stm32f4xx_dac.h
.\objects\spi.o: .\Library\inc\stm32f4xx_dcmi.h
.\objects\spi.o: .\Library\inc\stm32f4xx_fsmc.h
