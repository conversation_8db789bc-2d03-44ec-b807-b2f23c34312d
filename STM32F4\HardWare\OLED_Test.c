/**
  ******************************************************************************
  * @file    OLED_Test.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   0.96寸I2C OLED驱动测试版 - 基于spi_oled.c的硬件I2C实现
  ******************************************************************************
  */

#include "stm32f4xx.h"
#include "stm32f4xx_rcc.h"
#include "stm32f4xx_gpio.h"
#include "stm32f4xx_i2c.h"
#include "Delay.h"
#include "OLED_Test.h"

// 硬件I2C OLED配置 (完全参考spi_oled.c的成功配置)
#define OLED_I2C                    I2C1
#define OLED_I2C_CLK                RCC_APB1Periph_I2C1
#define OLED_I2C_CLK_INIT           RCC_APB1PeriphClockCmd

// I2C引脚定义（PB6=SCL, PB7=SDA）
#define OLED_SCL_PIN                GPIO_Pin_6      // PB6 - I2C1_SCL
#define OLED_SCL_GPIO_PORT          GPIOB
#define OLED_SCL_GPIO_CLK           RCC_AHB1Periph_GPIOB
#define OLED_SCL_SOURCE             GPIO_PinSource6
#define OLED_SCL_AF                 GPIO_AF_I2C1

#define OLED_SDA_PIN                GPIO_Pin_7      // PB7 - I2C1_SDA
#define OLED_SDA_GPIO_PORT          GPIOB
#define OLED_SDA_GPIO_CLK           RCC_AHB1Periph_GPIOB
#define OLED_SDA_SOURCE             GPIO_PinSource7
#define OLED_SDA_AF                 GPIO_AF_I2C1

// 设备I2C地址（SSD1306常用地址0x3C）
#define OLED_I2C_ADDRESS            0x3C

// OLED显存数组
uint8_t OLED_DisplayBuf[8][128];

//==============================================================================
// 硬件I2C通信函数 (完全复制spi_oled.c的成功实现)
//==============================================================================

/**
  * @brief  I2C初始化
  * @note   使用I2C1, PB6=SCL, PB7=SDA
  */
static void OLED_I2C_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    I2C_InitTypeDef  I2C_InitStructure;

    /* 使能时钟 */
    RCC_AHB1PeriphClockCmd(OLED_SCL_GPIO_CLK | OLED_SDA_GPIO_CLK, ENABLE);
    OLED_I2C_CLK_INIT(OLED_I2C_CLK, ENABLE);

    /* 配置I2C引脚 */
    GPIO_PinAFConfig(OLED_SCL_GPIO_PORT, OLED_SCL_SOURCE, OLED_SCL_AF);
    GPIO_PinAFConfig(OLED_SDA_GPIO_PORT, OLED_SDA_SOURCE, OLED_SDA_AF);

    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_OD;  // 开漏
    GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_UP;

    // SCL
    GPIO_InitStructure.GPIO_Pin = OLED_SCL_PIN;
    GPIO_Init(OLED_SCL_GPIO_PORT, &GPIO_InitStructure);
    // SDA
    GPIO_InitStructure.GPIO_Pin = OLED_SDA_PIN;
    GPIO_Init(OLED_SDA_GPIO_PORT, &GPIO_InitStructure);

    /* 复位I2C外设 */
    RCC_APB1PeriphResetCmd(OLED_I2C_CLK, ENABLE);
    RCC_APB1PeriphResetCmd(OLED_I2C_CLK, DISABLE);

    /* I2C配置 */
    I2C_DeInit(OLED_I2C);
    I2C_InitStructure.I2C_Mode = I2C_Mode_I2C;
    I2C_InitStructure.I2C_DutyCycle = I2C_DutyCycle_2;
    I2C_InitStructure.I2C_OwnAddress1 = 0x00;
    I2C_InitStructure.I2C_Ack = I2C_Ack_Enable;
    I2C_InitStructure.I2C_AcknowledgedAddress = I2C_AcknowledgedAddress_7bit;
    I2C_InitStructure.I2C_ClockSpeed = 400000; // 400kHz, 根据需要可改为100kHz
    I2C_Init(OLED_I2C, &I2C_InitStructure);

    I2C_Cmd(OLED_I2C, ENABLE);
}

/**
  * @brief  发送一个字节到OLED
  * @param  data: 要发送的数据
  * @param  cmd : 1-数据, 0-命令
  */
void OLED_WriteByte(uint8_t data, uint8_t cmd)
{
    uint8_t control = cmd ? 0x40 : 0x00; // 参考SSD1306手册

    // 等待总线空闲
    while (I2C_GetFlagStatus(OLED_I2C, I2C_FLAG_BUSY));

    I2C_GenerateSTART(OLED_I2C, ENABLE);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_MODE_SELECT));

    I2C_Send7bitAddress(OLED_I2C, OLED_I2C_ADDRESS << 1, I2C_Direction_Transmitter);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_TRANSMITTER_MODE_SELECTED));

    I2C_SendData(OLED_I2C, control);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED));

    I2C_SendData(OLED_I2C, data);
    while (!I2C_CheckEvent(OLED_I2C, I2C_EVENT_MASTER_BYTE_TRANSMITTED));

    I2C_GenerateSTOP(OLED_I2C, ENABLE);
}

/**
  * @brief  写命令
  */
void OLED_WriteCmd(uint8_t cmd)
{
    OLED_WriteByte(cmd, 0);
}

/**
  * @brief  写数据
  */
void OLED_WriteData(uint8_t data)
{
    OLED_WriteByte(data, 1);
}

/**
  * @brief  延时函数 (简单阻塞)
  */
void OLED_Delay_ms(uint32_t ms)
{
    Delay_us(ms * 1000);
}

//==============================================================================
// OLED驱动函数 (完全复制spi_oled.c的成功实现)
//==============================================================================

/**
  * @brief  OLED初始化
  * @param  None
  * @retval None
  */
void OLED_Init(void)
{
    // 初始化I2C
    OLED_I2C_Init();

    // I2C版本的模块通常上电自动复位，这里可以保留软件复位为空操作
    OLED_Delay_ms(100);

    // 初始化序列与原SPI版本保持一致
    OLED_WriteCmd(0xAE); // 关闭显示
    OLED_WriteCmd(0x20); // Memory Addressing Mode
    OLED_WriteCmd(0x02); // 0x02: Page Addressing Mode (与OLED_Update页写一致)
    OLED_WriteCmd(0xB0);
    OLED_WriteCmd(0xC8);
    OLED_WriteCmd(0x00);
    OLED_WriteCmd(0x10);
    OLED_WriteCmd(0x40);
    OLED_WriteCmd(0x81);
    OLED_WriteCmd(0xFF);
    OLED_WriteCmd(0xA1);
    OLED_WriteCmd(0xA6);
    OLED_WriteCmd(0xA8);
    OLED_WriteCmd(0x3F);
    OLED_WriteCmd(0xA4);
    OLED_WriteCmd(0xD3);
    OLED_WriteCmd(0x00);
    OLED_WriteCmd(0xD5);
    OLED_WriteCmd(0x80);
    OLED_WriteCmd(0xD9);
    OLED_WriteCmd(0xF1);
    OLED_WriteCmd(0xDA);
    OLED_WriteCmd(0x12);
    OLED_WriteCmd(0xDB);
    OLED_WriteCmd(0x40);
    OLED_WriteCmd(0x8D);
    OLED_WriteCmd(0x14);
    OLED_WriteCmd(0xAF);

    OLED_Clear();
}

/**
  * @brief  清屏
  * @param  None
  * @retval None
  */
void OLED_Clear(void)
{
    uint8_t i, j;

    // 清空显存
    for (i = 0; i < 8; i++) {
        for (j = 0; j < 128; j++) {
            OLED_DisplayBuf[i][j] = 0x00;
        }
    }
}

/**
  * @brief  更新显示
  * @param  None
  * @retval None
  */
void OLED_Update(void)
{
    uint8_t i, j;

    for (i = 0; i < 8; i++) {
        OLED_WriteCmd(0xB0 + i);    // 设置页地址
        OLED_WriteCmd(0x00);        // 设置低列地址 (SSD1306标准，不偏移)
        OLED_WriteCmd(0x10);        // 设置高列地址

        for (j = 0; j < 128; j++) {
            OLED_WriteData(OLED_DisplayBuf[i][j]);
        }
    }
}

/**
  * @brief  简单的显示测试函数
  * @param  None
  * @retval None
  */
void OLED_Test_Display(void)
{
    uint8_t i, j;
    
    // 清屏
    OLED_Clear();
    
    // 显示一些测试图案
    for (i = 0; i < 8; i++) {
        for (j = 0; j < 128; j++) {
            if ((i + j) % 2 == 0) {
                OLED_DisplayBuf[i][j] = 0xFF;
            }
        }
    }
    
    OLED_Update();
}
