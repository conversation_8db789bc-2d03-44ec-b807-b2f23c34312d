## 3. 硬件设计

### 3.1 核心控制单元 (STM32F103C8T6)

选择 STM32F103C8T6 作为系统的核心处理器，主要基于以下考虑：

1. **性能与资源匹配**：ARM Cortex-M3内核，72MHz主频，足以处理本系统的实时数据采集、处理和多传感器融合算法；64KB Flash和20KB SRAM能够满足程序和数据存储需求。

2. **丰富的外设资源**：
   - 2个SPI接口：用于连接OLED显示屏和可选的存储器
   - 2个I2C接口：用于连接VL53L0X和MPU9250传感器
   - 3个USART接口：用于调试输出和连接语音模块
   - 16个定时器通道：提供PWM输出控制LED和电机
   - 10个12位ADC通道：用于电池电量监测等
   - 多达37个GPIO：满足按键输入和各种控制信号需求

3. **低功耗特性**：支持多种低功耗模式（睡眠、停止、待机），可有效延长电池使用时间。

4. **开发生态成熟**：有丰富的开发资源、库函数和社区支持，加速开发进程。

5. **成本优势**：价格适中（约10元/片），性价比高。

**STM32F103C8T6最小系统设计**：

最小系统包括以下关键部分：

1. **时钟系统**：
   - 外部8MHz晶振，通过PLL倍频至72MHz系统时钟
   - 32.768KHz低速晶振，用于RTC和低功耗模式

2. **复位电路**：
   - 外部复位按钮连接至NRST引脚
   - RC延时电路确保稳定复位（10KΩ上拉电阻 + 100nF电容）

3. **电源管理**：
   - 3.3V稳压器供电（如AMS1117-3.3）
   - 电源滤波电容（10μF + 0.1μF并联）
   - 单独的VDDA模拟供电滤波

4. **调试接口**：
   - SWD接口（SWDIO和SWCLK）用于程序下载和调试
   - UART转USB接口（如CH340G）用于串口调试

5. **启动模式配置**：
   - BOOT0和BOOT1引脚配置为从主闪存启动（BOOT0接地）

**引脚分配表**：

| 引脚 | 功能 | 连接对象 | 备注 |
|------|------|---------|------|
| PA0-PA3 | GPIO | 步进电机驱动 | ULN2003控制信号 |
| PA4 | SPI1_NSS | OLED_CS | 片选信号 |
| PA5 | SPI1_SCK | OLED_SCK | 时钟信号 |
| PA7 | SPI1_MOSI | OLED_MOSI | 数据信号 |
| PA8 | GPIO | OLED_DC | 数据/命令选择 |
| PA9 | USART1_TX | 调试/语音模块 | 发送数据 |
| PA10 | USART1_RX | 调试/语音模块 | 接收数据 |
| PA11-PA12 | GPIO | RGB LED | 通过PWM控制 |
| PB0 | GPIO | OLED_RES | 复位信号 |
| PB6 | I2C1_SCL | VL53L0X_SCL | 时钟信号 |
| PB7 | I2C1_SDA | VL53L0X_SDA | 数据信号 |
| PB8 | I2C2_SCL | MPU9250_SCL | 时钟信号 |
| PB9 | I2C2_SDA | MPU9250_SDA | 数据信号 |
| PB10-PB15 | GPIO | 按键输入 | 内部上拉 |
| PC13-PC15 | GPIO | 状态指示 | 调试用LED |
| PA0 | ADC | 电池电压 | 分压后采样 |

**电路设计注意事项**：

1. **电源设计**：
   - 为数字电路和模拟电路提供单独的电源和地平面
   - 在每个IC电源引脚附近放置去耦电容
   - 考虑噪声敏感器件的供电滤波

2. **信号完整性**：
   - SPI和I2C总线上添加适当的上拉电阻
   - 关键信号走线尽量短而粗
   - 避免高速信号线和敏感模拟信号线的交叉

3. **EMC/EMI考虑**：
   - 为晶振提供屏蔽接地
   - 添加必要的ESD保护元件
   - 模拟信号和数字信号分开布线

4. **热设计**：
   - 考虑主要发热元件（如稳压器）的散热布局
   - 必要时为ULN2003驱动芯片添加散热措施

### 3.2 数据采集单元

#### 3.2.1 VL53L0X激光测距传感器

VL53L0X是一款基于Time-of-Flight (ToF)原理的激光测距传感器，通过测量激光信号往返时间计算距离。选择该传感器的主要理由：

1. **高精度**：±3%测量精度，在良好条件下可达毫米级分辨率
2. **适合的测量范围**：30mm-2000mm，覆盖关节弯曲测量所需范围
3. **快速响应**：最高50Hz采样率，满足实时性要求
4. **体积小巧**：4.4×2.4×1.0mm封装，易于集成到可穿戴设备
5. **低功耗**：<20mW典型功耗，支持低功耗模式

**接口设计**：

VL53L0X采用I2C接口通信，默认7位地址为0x29。连接到STM32的I2C1接口：
- VL53L0X_SCL → PB6 (I2C1_SCL)
- VL53L0X_SDA → PB7 (I2C1_SDA)
- VL53L0X_XSHUT → PB1 (GPIO输出，用于传感器复位)
- VL53L0X_GPIO1 → PB2 (GPIO输入，可用作数据就绪中断)

I2C总线需要上拉电阻（典型值4.7kΩ），并注意总线电容不要超过400pF。

**传感器配置**：

为达到最佳性能，VL53L0X支持多种工作模式配置：
1. **默认模式**：平衡精度和速度，适合大多数测量场景
2. **高精度模式**：提高测量精度，但降低更新率（约30Hz）
3. **高速模式**：提高更新率（约50Hz），但降低精度
4. **长距离模式**：优化远距离测量，牺牲近距离性能

在本系统中，建议采用高精度模式以获得最准确的关节角度测量。

**多传感器配置**：

为提高角度测量精度，系统可采用双VL53L0X配置：
- 传感器1：固定在关节一侧（如大腿），测量到参考点的距离
- 传感器2：固定在关节另一侧（如小腿），测量到同一参考点的距离

由于VL53L0X默认I2C地址相同，需要利用XSHUT引脚进行地址更改：
1. 将所有传感器XSHUT拉低，关闭所有传感器
2. 依次将每个传感器XSHUT拉高，并通过I2C命令设置唯一地址
3. 配置完成后，可同时与多个传感器通信

**安装与校准考虑**：

传感器的安装位置直接影响测量精度，需要考虑：
1. 传感器与目标表面尽量保持垂直，减少散射误差
2. 避免传感器被衣物等遮挡
3. 考虑目标表面材质对红外反射的影响
4. 安装固定结构应尽量减小传感器晃动或位移

校准过程包括：
1. 零位校准：在已知角度（如0°）时记录距离值
2. 多点校准：在不同角度（如30°、60°、90°）测量并记录，建立角度-距离映射关系
3. 环境光校准：在不同光照条件下测试并补偿

#### 3.2.2 MPU9250九轴IMU传感器模块

MPU9250是一款集成加速度计、陀螺仪和磁力计的九轴惯性测量单元，主要用于姿态测量和辅助角度计算。选择该传感器的理由：

1. **全面的运动数据**：提供完整的三维运动信息
2. **高精度**：陀螺仪灵敏度达±250dps、±500dps、±1000dps、±2000dps四档可选
3. **内置DMP**：数字运动处理器可直接输出姿态数据，减轻主控负担
4. **低功耗**：各传感器可独立开关，优化功耗
5. **丰富接口**：支持I2C和SPI双接口模式

**接口设计**：

MPU9250可通过I2C或SPI接口通信，考虑到数据传输速率要求，本系统选择I2C接口：
- MPU9250_SCL → PB8 (I2C2_SCL)
- MPU9250_SDA → PB9 (I2C2_SDA)
- MPU9250_INT → PB5 (GPIO输入，数据就绪中断)

同样需要I2C总线上拉电阻（4.7kΩ）。

**传感器配置**：

MPU9250提供多种配置选项以适应不同应用需求：
1. **陀螺仪量程**：选择±2000dps，确保能捕捉快速运动
2. **加速度计量程**：选择±8g，平衡灵敏度和测量范围
3. **数字低通滤波**：启用板载DLPF，设置为20Hz截止频率
4. **采样率**：设置为200Hz，满足步态分析需求
5. **DMP配置**：启用DMP，输出四元数或欧拉角

**数据融合策略**：

MPU9250内部的三个传感器数据需要融合以提供准确的姿态信息：
1. **加速度计**：提供重力方向参考，但受运动加速度影响
2. **陀螺仪**：提供角速度信息，积分后得到角度变化，但存在漂移
3. **磁力计**：提供地磁场方向参考，辅助航向角计算，但易受磁干扰

本系统采用以下融合策略：
- 短期内依赖陀螺仪积分得到角度变化
- 长期内依赖加速度计和磁力计校正漂移
- 使用互补滤波或卡尔曼滤波算法实现最优融合

**校准方法**：

IMU的精确测量依赖于良好的校准：
1. **加速度计校准**：
   - 静置传感器在六个不同方向（±X，±Y，±Z轴朝上）
   - 记录每个方向的原始值，计算零偏和缩放因子
   - 校准公式：Acal = (Araw - Abias) * Ascale

2. **陀螺仪校准**：
   - 静置传感器，记录静止状态下的输出值
   - 计算零偏并在后续测量中减去
   - 校准公式：Gcal = Graw - Gbias

3. **磁力计校准**：
   - 在空旷环境下进行"8"字形运动，收集全方位数据
   - 计算硬铁偏差（偏移）和软铁偏差（椭球到球的变换矩阵）
   - 校准公式：Mcal = A * (Mraw - Mbias)，其中A为变换矩阵

### 3.3 数据显示单元 (OLED)

选用0.96英寸OLED显示屏（SSD1306控制芯片）作为人机交互界面，主要优势：

1. **高对比度**：自发光显示，对比度高，适合各种光线环境
2. **低功耗**：仅在点亮像素时消耗电力，适合电池供电设备
3. **快速响应**：无视角限制，刷新率高，无残影
4. **接口简单**：支持I2C/SPI接口，易于与MCU连接
5. **尺寸合适**：0.96英寸（128×64像素）平衡了可视性和便携性

**接口设计**：

为了获得更高的刷新率，选择SPI接口连接OLED：
- OLED_CS → PA4 (SPI1_NSS)
- OLED_SCK → PA5 (SPI1_SCK)
- OLED_MOSI → PA7 (SPI1_MOSI)
- OLED_DC → PA8 (GPIO，数据/命令选择)
- OLED_RES → PB0 (GPIO，复位信号)

SPI配置为模式0（CPOL=0, CPHA=0），主频可达到10MHz，确保流畅的UI更新。

**显示内容设计**：

OLED显示器将呈现多个信息界面：

1. **主数据界面**：
   - 当前关节角度（大字体，醒目位置）
   - 简化的角度变化趋势图（近30个采样点）
   - 当前步态模式状态图标（正常/调整/警告）
   - 电池电量图标和百分比

2. **详细数据界面**：
   - 关节角度历史曲线（带最大/最小值标记）
   - 角速度数据
   - IMU姿态角（俯仰、滚转）
   - 原始传感器读数（调试用）

3. **系统设置界面**：
   - 参数调整选项（步态阈值、传感器灵敏度等）
   - 校准选项
   - 系统信息（版本、运行时间等）
   - 电源管理选项

**显示驱动优化**：

为提高OLED的显示效率和降低功耗：
1. **局部刷新**：只更新变化的区域，减少SPI通信量
2. **缓冲区管理**：维护显示缓冲区，避免频繁写入显示器
3. **显示休眠**：长时间无操作自动降低亮度或关闭显示
4. **字体优化**：使用经过优化的位图字体，减少存储占用
5. **绘图加速**：实现Bresenham算法等高效图形绘制函数

### 3.4 用户交互单元 (按键)

系统配备4个物理按键，提供直观的用户交互方式：

1. **确认键**：执行选择、确认操作、进入子菜单
2. **返回键**：返回上级菜单、取消操作、退出当前模式
3. **上键**：导航菜单项、增加参数值、上翻页
4. **下键**：导航菜单项、减少参数值、下翻页

**硬件设计**：

每个按键电路包括：
- 微动开关（轻触开关）
- 10kΩ上拉电阻
- 100nF去抖电容
- 可选的ESD保护二极管

按键连接到MCU的GPIO引脚：
- 确认键 → PB10
- 返回键 → PB11
- 上键 → PB12
- 下键 → PB13

所有按键引脚配置为：
- 输入模式
- 内部上拉（可省略外部上拉电阻）
- 使能下降沿中断（按下触发）

**软件去抖设计**：

为消除机械按键的抖动，采用以下策略：
1. **时间延迟法**：检测到按键按下后，等待10-20ms再次检查状态
2. **多次采样法**：连续多次采样（如5次，每次1ms），结果一致才认为有效
3. **状态机法**：维护按键状态机，处理按下、释放、长按等多种状态

**按键功能映射**：

按键功能根据当前系统状态动态映射：
1. **主界面模式**：
   - 确认键：进入菜单
   - 返回键：切换数据视图
   - 上/下键：调整显示参数范围

2. **菜单模式**：
   - 确认键：选择当前菜单项
   - 返回键：返回上级菜单
   - 上/下键：导航菜单项

3. **参数设置模式**：
   - 确认键：保存当前参数
   - 返回键：取消修改
   - 上/下键：增加/减少参数值

4. **校准模式**：
   - 确认键：确认当前校准步骤
   - 返回键：中止校准过程
   - 上/下键：根据提示调整位置

### 3.5 反馈执行单元

#### 3.5.1 LED视觉反馈

系统采用RGB全彩LED提供直观的视觉反馈：

**硬件设计**：
- LED类型：共阳极RGB LED（或WS2812集成RGB LED）
- 驱动方式：三路PWM信号控制三基色
- 连接引脚：
  - LED_R → PA11 (TIM1_CH4)
  - LED_G → PA12 (TIM1_CH1)
  - LED_B → PA15 (TIM2_CH1)
- 每路LED串接限流电阻（典型值150Ω）

**颜色编码**：
- **绿色**：步态正常（关节角度在目标范围内）
- **黄色**：需要微调（轻微偏离目标范围）
- **红色**：严重偏离（需要立即调整）
- **蓝色**：系统处于校准模式
- **紫色**：系统错误或异常状态
- **白色**：系统启动或重置中

**动态效果**：
1. **呼吸效果**：PWM占空比周期性变化，用于正常状态指示
2. **闪烁效果**：LED周期性开关，频率表示偏离程度（频率越高偏离越大）
3. **渐变效果**：颜色平滑过渡，用于状态转换提示
4. **脉冲效果**：短促亮起后渐暗，用于确认用户操作

#### 3.5.2 步进电机触觉反馈

采用小型五线四相步进电机（28BYJ-48）提供触觉反馈：

**硬件设计**：
- 电机规格：5V直流，步进角5.625°/64
- 驱动芯片：ULN2003达林顿晶体管阵列
- 连接引脚：
  - IN1 → PA0
  - IN2 → PA1
  - IN3 → PA2
  - IN4 → PA3
- 接口电路：各控制线路添加反向续流二极管保护

**驱动模式**：
1. **四相八拍模式**：提供最大转矩，适合强反馈场景
2. **四相四拍模式**：平衡转矩和效率
3. **微步进模式**：提供更平滑的振动效果

**反馈模式**：
1. **正常模式**：电机不运转或低速平稳运转，无明显震动
2. **调整模式**：周期性短脉冲转动，产生温和的触觉提醒
3. **警告模式**：高频率往复转动，产生明显的振动警告
4. **确认模式**：单次短促转动，确认用户操作

**功耗管理**：
- 非活动状态断开电机线圈电流
- 使用占空比调制减少持续运行时的功耗
- 设置最大连续工作时间，防止过热

#### 3.5.3 语音听觉反馈

集成WT588D或WTV020-SD语音模块提供听觉反馈：

**硬件设计**：
- 模块选型：WT588D-U（2MB闪存，UART控制）
- 接口连接：
  - TX → PA9 (USART1_TX)
  - RX → PA10 (USART1_RX)
  - BUSY → PB14 (GPIO输入，播放状态监测)
- 音频输出：小型扬声器（8Ω，0.5W）或压电蜂鸣器

**通信协议**：
- UART配置：9600bps，8数据位，无校验，1停止位
- 命令格式：帧头(0xFD) + 命令字 + 参数 + 校验

**语音内容设计**：
1. **系统提示**：
   - "系统启动完成"
   - "电池电量低，请及时充电"
   - "进入校准模式"
   - "校准完成"
   - "系统错误，请重启"

2. **步态反馈**：
   - "步态良好，请保持"
   - "请稍微调整膝盖角度"
   - "角度过大，请立即调整"
   - "速度过快，请放慢"

3. **操作指导**：
   - "请保持当前姿势5秒"
   - "请完全伸直关节"
   - "请弯曲关节至90度"
   - "操作已确认"

**优先级管理**：
- 实现语音消息队列，按重要性排序
- 允许高优先级消息打断当前播放
- 合并短时间内的重复提示，避免过度反馈

### 3.6 电源管理单元

电源管理是保证系统可靠性和续航能力的关键，采用以下设计：

**电池选型**：
- 类型：3.7V锂聚合物电池
- 容量：2000mAh，满足8小时以上工作需求
- 尺寸：根据PCB布局选择合适规格
- 保护：内置过充、过放、短路保护电路

**充电管理**：
- 充电IC：TP4056（1A线性充电管理芯片）
- 充电指示：双色LED（红色充电中，绿色充满）
- 充电接口：Type-C接口，支持5V/1A输入
- 充电保护：温度监测，过流保护

**电源转换**：
- 升压转换：MT3608升压模块（3.7V升至5V）
  - 输出电流能力：最大2A
  - 效率：>85%
  - 输出纹波：<50mV
- 降压转换：AMS1117-3.3（5V降至3.3V）
  - 输出电流：最大800mA
  - 低压差：<1.3V
  - 输出精度：±1%

**电量监测**：
- 采样电路：电阻分压网络（100kΩ+33kΩ）
- 采样点：电池正极
- ADC配置：12位分辨率，采样率10Hz
- 电量估算：基于电压曲线建立查找表

**低功耗设计**：
1. **硬件层面**：
   - 各模块独立电源控制，支持选择性关闭
   - 传感器支持低功耗模式
   - 选用低静态功耗的电源管理芯片

2. **软件层面**：
   - 实现多级功耗模式（全功率、标准、低功耗、超低功耗）
   - 非活动时间超过设定值自动降低功耗
   - 根据电池电量动态调整功耗策略

**电源管理状态机**：
- **正常状态**：电量>20%，所有功能正常运行
- **省电状态**：电量10-20%，降低采样率，关闭非必要功能
- **紧急状态**：电量<10%，最低功能运行，频繁提示充电
- **关机保护**：电量<5%，保存关键数据，安全关机

**电源滤波与去耦**：
- 电源轨配置多级滤波电容（10μF+1μF+0.1μF）
- 每个IC电源引脚附近放置0.1μF去耦电容
- 模拟部分使用独立的LC滤波电路
- 添加EMI抑制铁氧体磁珠

### 3.7 PCB设计与结构件

#### 3.7.1 PCB设计

PCB设计直接影响系统的可靠性、抗干扰能力和装配便利性：

**PCB规格**：
- 层数：四层板（信号/电源/地/信号）
- 尺寸：45mm×35mm（目标）
- 板厚：1.6mm
- 铜厚：1oz（外层），0.5oz（内层）
- 阻焊颜色：绿色（或黑色）
- 表面处理：沉金（ENIG）

**层叠结构**：
- 第1层（顶层）：主要信号走线、SMD元件
- 第2层（内层1）：地平面，提供低阻抗返回路径
- 第3层（内层2）：电源平面，分区布局不同电压域
- 第4层（底层）：次要信号走线、通孔元件

**布局考虑**：
1. **功能分区**：
   - 数字电路区：MCU及相关电路
   - 模拟电路区：传感器、ADC采样电路
   - 电源区：电池管理、电源转换
   - 接口区：按键、连接器、指示灯

2. **关键布局原则**：
   - 敏感模拟电路远离数字噪声源
   - 高频电路（如晶振）靠近相关IC
   - 热敏器件（如稳压器）放置在边缘便于散热
   - 按键和指示灯靠近外壳开口位置

**走线规则**：
- 电源/地线宽度：0.5-0.8mm
- 信号线宽度：0.2-0.3mm
- 最小线间距：0.2mm
- 过孔规格：0.6mm/0.3mm（外径/内径）
- 关键信号（如SPI时钟）考虑阻抗控制

**EMC/EMI设计**：
- 地平面分割最小化，减少共模噪声
- 关键信号线添加保护接地
- I/O端口添加ESD保护和滤波
- 电源入口添加共模扼流圈
- 考虑电磁屏蔽措施

**DFM/DFA考虑**：
- 避免使用过小的元件（0402以下）
- 为测试点预留足够空间
- 添加对齐标记便于组装
- 考虑焊接工艺兼容性
- 预留产品标识和版本号区域

#### 3.7.2 结构设计

结构设计需兼顾保护性、佩戴舒适性和美观：

**外壳设计**：
- 材料：ABS工程塑料或PC材料
- 工艺：注塑成型或3D打印（小批量）
- 尺寸：约50mm×40mm×15mm
- 防护等级：IP54（防尘、防溅）
- 开口：显示屏窗口、按键区、传感器窗口、充电接口

**固定方案**：
- 采用可调节的尼龙弹性带
- 硅胶内衬增加舒适度和防滑性
- 魔术贴调节机构适应不同体型
- 传感器部位采用精确定位结构

**散热考虑**：
- 主要发热元件（如稳压器）靠近外壳散热区
- 设计适当的通风孔促进空气流通
- 考虑添加散热硅胶片连接热源和外壳

**组装方案**：
- 采用卡扣或自攻螺丝固定外壳
- PCB通过支撑柱固定在外壳内
- 电池采用卡槽或双面胶固定
- 预留维修通道便于后期维护

**人体工学考虑**：
- 边缘圆角处理，避免尖锐边缘
- 贴合人体曲线的轮廓设计
- 重量平衡，避免使用时偏移
- 按键设计考虑触感和可操作性

**传感器安装优化**：
- VL53L0X传感器窗口采用透红外亚克力保护
- IMU安装位置考虑刚性连接，减少振动干扰
- 设计校准标记，便于用户确认传感器位置
- 考虑备用传感器接口，便于扩展或更换 