#ifndef __MOTOR_H
#define __MOTOR_H

#include "stm32f10x.h"
#include "ir_sensor.h"  // 包含TrainingMode_t定义

// 添加NULL宏定义，如果没有定义的话
#ifndef NULL
#define NULL ((void*)0)
#endif

// 添加电机引脚定义
#define MOTOR_PORT      GPIOA
#define MOTOR_PIN_A     GPIO_Pin_1
#define MOTOR_PIN_B     GPIO_Pin_2
#define MOTOR_PIN_C     GPIO_Pin_3
#define MOTOR_PIN_D     GPIO_Pin_4
#define MOTOR_ALL_PINS  (MOTOR_PIN_A | MOTOR_PIN_B | MOTOR_PIN_C | MOTOR_PIN_D)

// 步进电机步进模式定义
typedef enum {
    MOTOR_STEP_FULL,    // 全步进模式 (4拍)
    MOTOR_STEP_HALF,    // 半步进模式 (4拍)
    MOTOR_STEP_EIGHT    // 八拍步进模式 (8拍)
} MotorStepMode_t;

void Motor_Init(void);
void Motor_Release(void);
void Motor_One(uint16_t speed);
void Motor_two(uint16_t speed);
void Motor_one_two(uint16_t speed);

// 新增函数 - 基于五线四相步进电机例程
void Motor_Start(MotorStepMode_t mode, uint8_t speed, uint16_t steps);
void Motor_Stop(void);
uint8_t Motor_IsRunning(void);
void Motor_Direction(uint8_t dir, MotorStepMode_t mode, uint8_t speed);
void Motor_Direction_Angle(uint8_t dir, MotorStepMode_t mode, uint16_t angle, uint8_t speed);

// 获取电机状态函数 - 修改参数类型与实际一致
void Motor_GetStatus(MotorStepMode_t *mode, uint16_t *speed, uint16_t *steps_remaining);

// 测试函数 - 直接执行电机测试
void Motor_Test(void);

// 训练模式控制函数
void Motor_Control(TrainingMode_t mode);  // 修改为与 ir_sensor.h 中的 TrainingMode_t 类型一致

#endif
