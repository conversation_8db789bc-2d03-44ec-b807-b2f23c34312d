<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40"><head>








<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="File-List" href="Library_files/filelist.xml">
<link rel="Edit-Time-Data" href="Library_files/editdata.mso"><!--[if !mso]> <style> v\:* {behavior:url(#default#VML);} o\:* {behavior:url(#default#VML);} w\:* {behavior:url(#default#VML);} .shape {behavior:url(#default#VML);} </style> <![endif]--><title>Release Notes for STM32F4xx CMSIS</title><!--[if gte mso 9]><xml> <o:DocumentProperties> <o:Author>STMicroelectronics</o:Author> <o:LastAuthor>STMicroelectronics</o:LastAuthor> <o:Revision>37</o:Revision> <o:TotalTime>136</o:TotalTime> <o:Created>2009-02-27T19:26:00Z</o:Created> <o:LastSaved>2009-03-01T17:56:00Z</o:LastSaved> <o:Pages>1</o:Pages> <o:Words>522</o:Words> <o:Characters>2977</o:Characters> <o:Company>STMicroelectronics</o:Company> <o:Lines>24</o:Lines> <o:Paragraphs>6</o:Paragraphs> <o:CharactersWithSpaces>3493</o:CharactersWithSpaces> <o:Version>11.6568</o:Version> </o:DocumentProperties> </xml><![endif]--><!--[if gte mso 9]><xml> <w:WordDocument> <w:Zoom>110</w:Zoom> <w:ValidateAgainstSchemas/> <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid> <w:IgnoreMixedContent>false</w:IgnoreMixedContent> <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText> <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel> </w:WordDocument> </xml><![endif]--><!--[if gte mso 9]><xml> <w:LatentStyles DefLockedState="false" LatentStyleCount="156"> </w:LatentStyles> </xml><![endif]-->



<style>
<!--
/* Style Definitions */
p.MsoNormal, li.MsoNormal, div.MsoNormal
{mso-style-parent:"";
margin:0in;
margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
h2
{mso-style-next:Normal;
margin-top:12.0pt;
margin-right:0in;
margin-bottom:3.0pt;
margin-left:0in;
mso-pagination:widow-orphan;
page-break-after:avoid;
mso-outline-level:2;
font-size:14.0pt;
font-family:Arial;
font-weight:bold;
font-style:italic;}
a:link, span.MsoHyperlink
{color:blue;
text-decoration:underline;
text-underline:single;}
a:visited, span.MsoHyperlinkFollowed
{color:blue;
text-decoration:underline;
text-underline:single;}
p
{mso-margin-top-alt:auto;
margin-right:0in;
mso-margin-bottom-alt:auto;
margin-left:0in;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
@page Section1
{size:8.5in 11.0in;
margin:1.0in 1.25in 1.0in 1.25in;
mso-header-margin:.5in;
mso-footer-margin:.5in;
mso-paper-source:0;}
div.Section1
{page:Section1;}
-->
</style><!--[if gte mso 10]> <style> /* Style Definitions */ table.MsoNormalTable {mso-style-name:"Table Normal"; mso-tstyle-rowband-size:0; mso-tstyle-colband-size:0; mso-style-noshow:yes; mso-style-parent:""; mso-padding-alt:0in 5.4pt 0in 5.4pt; mso-para-margin:0in; mso-para-margin-bottom:.0001pt; mso-pagination:widow-orphan; font-size:10.0pt; font-family:"Times New Roman"; mso-ansi-language:#0400; mso-fareast-language:#0400; mso-bidi-language:#0400;} </style> <![endif]--><!--[if gte mso 9]><xml> <o:shapedefaults v:ext="edit" spidmax="5122"/> </xml><![endif]--><!--[if gte mso 9]><xml> <o:shapelayout v:ext="edit"> <o:idmap v:ext="edit" data="1"/> </o:shapelayout></xml><![endif]--></head><body style="" lang="EN-US" link="blue" vlink="blue">
<div class="Section1">
<p class="MsoNormal"><span style="font-family: Arial;"><o:p><br>
</o:p></span></p>
<div align="center">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
<tbody>
<tr style="">
<td style="padding: 0cm;" valign="top">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
<tbody>
          <tr>
            <td style="vertical-align: top;"><span style="font-size: 8pt; font-family: Arial; color: blue;"><a href="../../../../../Release_Notes.html">Back to Release page</a></span></td>
          </tr>
<tr style="">
<td style="padding: 1.5pt;">
<h1 style="margin-bottom: 18pt; text-align: center;" align="center"><span style="font-size: 20pt; font-family: Verdana; color: rgb(51, 102, 255);">Release
Notes for STM32F4xx CMSIS</span><span style="font-size: 20pt; font-family: Verdana;"><o:p></o:p></span></h1>
<p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;">Copyright 2016 STMicroelectronics</span><span style="color: black;"><u1:p></u1:p><o:p></o:p></span></p>
<p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;"><img alt="" id="_x0000_i1025" src="../../../../../_htmresc/logo.bmp" style="border: 0px solid ; width: 86px; height: 65px;"></span><span style="font-size: 10pt;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
<p class="MsoNormal"><span style="font-family: Arial; display: none;"><o:p>&nbsp;</o:p></span></p>
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" width="900">
<tbody>
<tr>
<td style="padding: 0cm;" valign="top">
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><span style="font-size: 12pt; color: white;">Contents<o:p></o:p></span></h2>
            <span style="font-size: 10pt; font-family: Verdana;"><a href="#License">STM32F4xx&nbsp;CMSIS
update History</a><o:p></o:p></span><ol style="margin-top: 0cm;" start="1" type="1">

</ol>
<span style="font-family: &quot;Times New Roman&quot;;"></span>
<h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial;"><a name="History"></a><span style="font-size: 12pt; color: white;">STM32F4xx CMSIS
update History</span></h2><span style="font-weight: bold;"></span>
            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.8.1 / 27-January-2022</span></h3>

            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b>

            <span style="font-size: 10pt; font-family: Verdana;"><br>
            </span></p>
            <ul>
              <li><span style="font-size: 10pt; font-family: Verdana;">All source files: update disclaimer to add reference to the&nbsp;new license agreement.</span></li>
              <li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: &quot;Times New Roman&quot;; color: black;"><span style="font-size: 10pt; font-family: Verdana;">stm32f4xx.h</span></li>
              <ul>
                <li class="MsoNormal" style="margin: 4.5pt 0cm; font-size: 12pt; font-family: &quot;Times New Roman&quot;; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Update to add "TIM8_CC_IRQn" definition for <span style="font-weight: bold;">STM32F446xx</span> devices.</span></li>
              </ul>
              <ul>
                <li class="MsoNormal" style="margin: 4.5pt 0cm; font-size: 12pt; font-family: &quot;Times New Roman&quot;; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Update to use BSSR Register instead of BSSRL/BSSRH.</span></li>
              </ul>
              <ul>
                <li class="MsoNormal" style="margin: 4.5pt 0cm; font-size: 12pt; font-family: &quot;Times New Roman&quot;; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Fix Wrong Bit definition: "DBGMCU_APB2_FZ" instead of </span><span style="font-size: 10pt; font-family: Verdana;">"DBGMCU_APB1_FZ" for TIM1, TIM8, TIM9, TIM10 and TIM11.</span></li>
              </ul>
              <ul>
                <li class="MsoNormal" style="margin: 4.5pt 0cm; font-size: 12pt; font-family: &quot;Times New Roman&quot;; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Remove "BKPSRAM" bit definition for <span style="font-weight: bold;">STM32F40_41xxx </span>&amp;&nbsp; <span style="font-weight: bold;">STM32F401xx</span> </span><span style="font-size: 10pt; font-family: Verdana;">devices</span><span style="font-size: 10pt; font-family: Verdana;">.</span></li>
              </ul>
              <li class="MsoNormal" style="margin: 4.5pt 0cm; font-size: 12pt; font-family: &quot;Times New Roman&quot;; color: black;"><span style="font-size: 10pt; font-family: Verdana;">system_stm32f4xx</span></li>
</ul>
            <ul>
              <ul>
                <li><span style="font-size: 10pt; font-family: Verdana;">&nbsp; Fix WaitState (WS) value to be aligned with the reference manual for <span style="font-weight: bold;">STM32F410xx</span> &amp; <span style="font-weight: bold;">STM32F411xE</span> devices.</span></li>
              </ul>
            </ul>
            <ul>

            
            </ul>


            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.8.0 / 09-November-2016</span></h3>

<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
support of&nbsp;<span style="font-weight: bold;"></span><span style="font-weight: bold;">STM32F413xx and STM32F423xx</span> devices</span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add startup files&nbsp;<span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f413_423xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;">for</span><span style="font-size: 10pt; font-family: Verdana;"> EWARM, MDK-ARM, TrueSTUDIO and SW4STM32 toolchains: Add DFSDM2 and CAN3 interrupts handler entry in the vector table</span></li></ul><ul style="color: rgb(0, 0, 0); font-family: 'Times New Roman'; font-size: medium; font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><p class="MsoNormal" style="margin: 0in 0in 0.0001pt; font-size: 12pt; font-family: 'Times New Roman';"><span style="font-size: 10pt; font-family: Verdana,sans-serif;">stm32f4xx.h</span></p></li><ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Rename DFSDM_TypeDef by DFSDM_Filter_TypeDef</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Correct SRAM2 and SRAM3 base addresses</span></li></ul></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.7.0 / 22-April-2016</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
support of&nbsp;<span style="font-weight: bold;"></span><span style="font-weight: bold;">STM32F412xG</span> devices</span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add startup files&nbsp;<span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f412xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;">for</span><span style="font-size: 10pt; font-family: Verdana;"> EWARM, MDK-ARM, TrueSTUDIO and SW4STM32 toolchains: Add DFSDM interrupts handler entry in the vector table</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Update to use CMSIS_Core V4.3_CM4 version<br></span></li></ul><ul style="color: rgb(0, 0, 0); font-family: 'Times New Roman'; font-size: medium; font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><p class="MsoNormal" style="margin: 0in 0in 0.0001pt; font-size: 12pt; font-family: 'Times New Roman';"><span style="font-size: 10pt; font-family: Verdana,sans-serif;">stm32f4xx.h</span></p></li><ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Correct some&nbsp;bits definition to be in line with naming used in the Reference Manual </span><span style="font-size: 10pt; font-family: Verdana;"> (RM0090)</span></li><ul><li class="MsoNormal" style="color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">DCMI_<span style="font-weight: bold;">RISR_OVF</span>_RIS changed to&nbsp;DCMI_<span style="font-weight: bold;">RIS_OVR</span>_RIS</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">DCMI_IER_<span style="font-weight: bold;">OVF</span>_IE changed to DCMI_IER_<span style="font-weight: bold;">OVR</span>_IE</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">DCMI_<span style="font-weight: bold;">MISR</span></span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">_x</span> changed to&nbsp;<span style="font-style: italic;"><span style="font-weight: bold;"></span></span></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">DCMI_<span style="font-weight: bold;">MIS</span></span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;"><span style="font-weight: bold;"></span>_x</span></span></li></ul><ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">DCMI_ICR_<span style="font-weight: bold;">OVF</span>_ISC changed to DCMI_ICR_<span style="font-weight: bold;">OVR</span>_ISC</span><span style="font-size: 12pt; font-family: &quot;Times New Roman&quot;,serif;" lang="EN-US"><o:p></o:p></span></li></ul><ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">SAI_xFRCR_<span style="font-weight: bold;">FSPO</span> changed to&nbsp;SAI_xFRCR_<span style="font-weight: bold;">FSPOL</span></span><span style="font-size: 12pt; font-family: &quot;Times New Roman&quot;,serif;" lang="EN-US"><o:p></o:p></span></li></ul><ul><li class="MsoNormal" style="color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">LTDC_GCR_<span style="font-weight: bold;">DTEN</span> changed to LTDC_GCR_<span style="font-weight: bold;">DEN</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US"><span style="font-weight: bold;"></span>ADC_CSR_<span style="font-weight: bold;">DOVR</span>x&nbsp;</span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">changed to&nbsp;</span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">ADC_CSR_<span style="font-weight: bold;">OVR</span>x</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">DMA2D_<span style="font-weight: bold;">IFSR</span></span><span style="font-size: 10pt; font-family: Verdana;">_x changed to&nbsp;<span style="font-style: italic;"><span style="font-weight: bold;"></span></span></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">DMA2D_<span style="font-weight: bold;"></span></span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">IFCR</span>_x</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">DMA2D_<span style="font-weight: bold;">IFSR_CCAEIF</span> changed to </span><span style="font-size: 10pt; font-family: Verdana;">DMA2D_<span style="font-weight: bold;">IFCR_CAECIF</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">WWDG_CR</span><span style="font-size: 10pt; font-family: Verdana;">_<span style="font-weight: bold;">Tx</span> changed to&nbsp;<span style="font-style: italic;"><span style="font-weight: bold;"></span></span></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">WWDG_CR</span><span style="font-size: 10pt; font-family: Verdana;">_<span style="font-weight: bold;">T_x</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">WWDG_CFR</span><span style="font-size: 10pt; font-family: Verdana;">_<span style="font-weight: bold;">Wx</span> changed to&nbsp;<span style="font-style: italic;"><span style="font-weight: bold;"></span></span></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">WWDG_CFR</span><span style="font-size: 10pt; font-family: Verdana;">_<span style="font-weight: bold;">W_x</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">WWDG_CFR</span><span style="font-size: 10pt; font-family: Verdana;">_<span style="font-weight: bold;">WDGTBx</span> changed to&nbsp;<span style="font-style: italic;"><span style="font-weight: bold;"></span></span></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">WWDG_CFR</span><span style="font-size: 10pt; font-family: Verdana;">_<span style="font-weight: bold;">WDGTB_x</span></span></li></ul><li class="MsoNormal" style="color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">Add
      missing bit definitions </span><span style="font-size: 10pt; font-family: Verdana;">in the following registers to be in line with&nbsp;Reference Manual </span><span style="font-size: 10pt; font-family: Verdana;"> (RM0090)</span></li><ul><li class="MsoNormal" style="color: black;"><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">&nbsp;DCMI_ESCR, DCMI_ESUR, DCMI_CWSTRT,
      DCMI_CWSIZE, DCMI_DR, FMC_BWTR1 and DAC_CR registers</span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US"><span style="font-weight: bold;"></span></span></li></ul><li class="MsoNormal" style="color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">Correct
      a wrong value of SAI_xCR2_CPL definition bit</span></li><li class="MsoNormal" style="color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">Correct
      a wrong value of QUADSPI_CR_SSHIFT and QUADSPI_CR_FTHRES bits definition</span></li><li class="MsoNormal" style="color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">Correct
      a wrong value of&nbsp;</span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">LPTIM_CR_SNGSTRT</span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US"> definition bit</span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US"></span></li></ul><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><span style="font-size: 10pt; font-family: Verdana,sans-serif;">system_stm32f4xx.c<br></span></li><ul><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><span style="font-family: 'Segoe UI'; color: rgb(0, 0, 0); font-size: 10pt; direction: ltr;"><span style="font-family: Verdana,sans-serif; font-size: 10pt;">U</span></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,sans-serif;" lang="EN-US">pdate
      SystemInit_ExtMemCtl() function implementation to allow the possibility
      of simultaneous use of DATA_IN_ExtSRAM and DATA_IN_ExtSDRAM</span></li></ul></ul>
<h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; color: rgb(0, 0, 0); font-family: 'Times New Roman'; font-style: normal; font-variant: normal; letter-spacing: normal; line-height: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.6.1 / 21-October-2015</span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt; font-size: medium; font-family: 'Times New Roman'; color: rgb(0, 0, 0); font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main Changes</span></u></b></p><ul style="color: rgb(0, 0, 0); font-family: 'Times New Roman'; font-size: medium; font-style: normal; font-variant: normal; font-weight: normal; letter-spacing: normal; line-height: normal; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: 1; word-spacing: 0px; margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><p class="MsoNormal" style="margin: 0in 0in 0.0001pt; font-size: 12pt; font-family: 'Times New Roman';"><span style="font-size: 10pt; font-family: Verdana,sans-serif;">stm32f4xx.h</span></p></li><ul><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><span style="font-family: 'Segoe UI'; color: rgb(0, 0, 0); font-size: 10pt; direction: ltr;" dir="ltr"><span style="font-family: Verdana,sans-serif; font-size: 10pt;">Update bits definition for DSI_WPCR and DSI_TCCR registers</span></span></li><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><span style="font-family: 'Segoe UI'; color: rgb(0, 0, 0); font-size: 10pt; direction: ltr;"><span style="font-family: Verdana,sans-serif; font-size: 10pt;">Remove the CLKDIV and DATLAT bits definition in BWTRx register for FMC/FSMC</span></span></li><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><span style="font-family: 'Segoe UI'; color: rgb(0, 0, 0); font-size: 10pt; direction: ltr;"><span style="font-family: Verdana,sans-serif; font-size: 10pt;">Add the BUSTURN bit definition in BWTRx registers for FMC</span></span></li></ul><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><span style="font-size: 10pt; font-family: Verdana,sans-serif;">system_stm32f4xx.c<br></span></li><ul><li class="MsoNormal" style="margin: 4.5pt 0in; font-size: 12pt; font-family: 'Times New Roman'; color: black;"><span style="font-family: 'Segoe UI'; color: rgb(0, 0, 0); font-size: 10pt; direction: ltr;"><span style="font-family: Verdana,sans-serif; font-size: 10pt;">Update PLL_N defined value for STM32F40xxx/41xxx devices</span></span></li></ul></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.6.0 / 10-July-2015</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
support of&nbsp;<span style="font-weight: bold;"></span><span style="font-weight: bold;">STM32F410xx</span>, <span style="font-weight: bold;">STM32F469xx</span> and <span style="font-weight: bold;">STM32F479xx</span> devices</span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add startup files&nbsp;<span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f410xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;">for</span><span style="font-size: 10pt; font-family: Verdana;"> EWARM, MDK-ARM, TrueSTUDIO toolchains: Add LPTIM and FMPI2C interrupts handler entry in the vector table</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add startup files&nbsp;<span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f469_479xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;">for</span><span style="font-size: 10pt; font-family: Verdana;"> EWARM, MDK-ARM, TrueSTUDIO toolchains: Add DSI and QSPI interrupts handler entry in the vector table</span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.5.0 / 06-March-2015</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
support of&nbsp;<span style="font-weight: bold;"></span><span style="font-weight: bold;">STM32F446xx</span> devices</span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add startup files&nbsp;<span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f446xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;">for</span><span style="font-size: 10pt; font-family: Verdana;"> EWARM, MDK-ARM, TrueSTUDIO toolchains: Add QPSI, SPDIFRX, FMPI2C and CEC&nbsp;interrupts handler entry in the vector table</span><br><span style="font-size: 10pt; color: white; font-family: Arial;"></span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.4.0 / 04-August-2014</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
support of&nbsp;<span style="font-weight: bold;"></span><span style="font-weight: bold;">STM32F411xExx</span> devices</span></p></li><ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;"></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">system_stm32F4xx.c updated to use <span style="font-weight: bold;">HSI</span> or <span style="font-weight: bold;">HSE Bypass</span></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;"></span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">new defines added in stm32f4xx.h <br></span></li></ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add startup files&nbsp;<span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f411xe.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;">for</span><span style="font-size: 10pt; font-family: Verdana;"> EWARM, MDK-ARM, TrueSTUDIO toolchains: Add SPI5 interrupt handler entry in the vector table</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;" lang="EN-US">Update EWARM startup files to be compliant with&nbsp;EVARM V7: add <span style="font-weight: bold;">NOROOT </span>keyword in all IRQ&nbsp;section names</span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;" lang="EN-US"></span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;"></span></p></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.3.0 / 08-November-2013</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
support of&nbsp;<span style="font-weight: bold;"></span><span style="font-weight: bold;">STM32F401xExx</span> devices</span><span style="font-size: 10pt; font-family: Verdana;"></span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Update startup files&nbsp;<span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f401xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;">for</span><span style="font-size: 10pt; font-family: Verdana;"> EWARM, MDK-ARM, TrueSTUDIO and Ride toolchains: Add SPI4 interrupt handler entry in the vector table</span>
</li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.2.1 / 
19-September-2013</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: Verdana;">system_stm32f4xx.c</span><span style="font-size: 10pt; font-family: Verdana;"> : Update FMC SDRAM configuration (RBURST mode activation)<br></span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Update startup files&nbsp;<span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f427_437xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"> and </span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f429_439xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;"></span></span><span style="font-size: 10pt; font-family: Verdana;"> </span><span style="font-size: 10pt; font-family: Verdana;">for TrueSTUDIO and Ride toolchains and maintain the old name of startup files for legacy purpose</span>
</li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 181px;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.2.0 / 
11-September-2013</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;">

<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Add
support of <span style="font-weight: bold;">STM32F429/439xx</span> and <span style="font-weight: bold;">STM32F401xCxx</span> devices</span></p></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Update definition of <span style="font-weight: bold;">STM32F427/437xx</span> devices : </span><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">extension
of the features to include system clock up to 180MHz, dual bank Flash, reduced
STOP Mode current, SAI, PCROP, SDRAM and DMA2D</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">stm32f4xx.h</span><span style="font-size: 10pt; font-family: Verdana;"><br></span><span style="font-size: 10pt; font-family: Verdana;"></span>
<ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add the following device defines :</span></li><ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">"#define STM32F40_41xxx" for all&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">STM32405/415/407/417xx</span></span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">&nbsp;</span>devices</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">"#define STM32F427_437xx" for all&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">STM32F427/437xx </span>devices</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">"#define STM32F429_439xx" for all&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">STM32F429/439xx </span>devices</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">"#define STM32F401xx" for all&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">STM32F401xx&nbsp;</span>devices</span></li></ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Maintain the old device define for legacy purpose</span></li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Update IRQ handler enumeration structure to support all STM32F4xx Family devices. &nbsp;</span></li></ul>
</li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add new startup files "<span style="font-weight: bold; font-style: italic;">startup_stm32f40_41xxx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;">,</span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f427_437xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;">,&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f429_439xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"> and&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f401xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"> </span><span style="font-size: 10pt; font-family: Verdana;">for all toolchains and maintain the old name for startup files for legacy purpose</span>
</li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">system_stm32f4xx.c</span>
<ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Update the&nbsp;system configuration to support all&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">STM32F4xx Family devices. &nbsp;</span>
</li></ul></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; width: 167px; margin-right: 500pt;"><span style="font-size: 10pt; color: white; font-family: Arial;">V1.1.0 / 
11-January-2013</span></h3>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b><u><span style="font-size: 10pt; color: black; font-family: Verdana;">Main 
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Official release for&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">STM32F427x/437x</span> devices.</span>
</li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">stm32f4xx.h</span><span style="font-size: 10pt; font-family: Verdana;"><br></span><span style="font-size: 10pt; font-family: Verdana;"></span>
<ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Update product define: replace 
"#define STM32F4XX" by "#define STM32F40XX" for STM32F40x/41x devices</span>
</li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">&nbsp;Add new product define: "#define 
STM32F427X" for&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">STM32F427x/437x</span><span style="font-size: 10pt; font-family: Verdana;"> devices.</span></li></ul>
</li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add new startup files "<span style="font-weight: bold; font-style: italic;">startup_stm32f427x.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"> </span><span style="font-size: 10pt; font-family: Verdana;">for all toolchains</span>
</li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">rename startup files "<span style="font-weight: bold; font-style: italic;">startup_stm32f4xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"> by </span><span style="font-size: 10pt; font-family: Verdana;">"<span style="font-weight: bold; font-style: italic;">startup_stm32f40xx.s</span>"</span><span style="font-size: 10pt; font-family: Verdana;"> </span><span style="font-size: 10pt; font-family: Verdana;">for all toolchains</span> 
</li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">system_stm32f4xx.c</span>
<ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Prefetch Buffer enabled</span>
</li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add reference to STM32F427x/437x 
devices and STM324x7I_EVAL board</span>
</li><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">SystemInit_ExtMemCtl() 
function<br></span>
<ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Add configuration of missing FSMC 
address and data lines <br></span></li></ul>
<ul><li class="MsoNormal" style="margin-top: 4.5pt; margin-bottom: 4.5pt; color: black;"><span style="font-size: 10pt; font-family: Verdana;">Change memory type to SRAM instead 
of PSRAM (PSRAM is available only on STM324xG-EVAL RevA) and update timing 
values</span></li></ul></li></ul></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 167px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.0.2 / 05-March-2012<o:p></o:p></span></h3>
            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>

            <ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">All source files:&nbsp;license disclaimer text update and add link to the License file on ST Internet.</span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 176px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.0.1 / 28-December-2011<o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">All source files: update disclaimer to add reference to the&nbsp;new license agreement</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">stm32f4xx.h</span></li><ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">Correct&nbsp;bit definition: </span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">RCC_AHB2RSTR_<span style="font-weight: bold;">HSAH</span>RST</span>&nbsp;changed to <span style="font-style: italic;">RCC_AHB2RSTR_<span style="font-weight: bold;">HASH</span>RST</span></span></li></ul></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 200px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.0.0 / 30-September-2011<o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">First official release for&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold; font-style: italic;">STM32F40x/41x</span> devices</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Add startup file for <span style="font-style: italic;">TASKING</span> toolchain</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">system_stm32f4xx.c: driver's&nbsp;comments update</span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 200px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.0.0RC2 / 26-September-2011<o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Official version (V1.0.0) Release Candidate2&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">for <span style="font-weight: bold; font-style: italic;">STM32F40x/41x</span> devices</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">stm32f4xx.h</span></li><ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Add define for Cortex-M4 revision&nbsp;<span style="font-style: italic;">__CM4_REV</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Correct <span style="font-style: italic;">RCC_CFGR_PPRE2_DIV16</span> bit&nbsp;(in&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">RCC_CFGR</span><span style="font-size: 10pt; font-family: Verdana;"> register) value to&nbsp;0x0000E000</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Correct some&nbsp;bits definition to be in line with naming used in the Reference Manual </span><span style="font-size: 10pt; font-family: Verdana;"> (RM0090)</span></li><ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">GPIO_<span style="font-weight: bold;">OTYPER</span>_IDR_x</span> changed to <span style="font-style: italic;">GPIO_<span style="font-weight: bold;">IDR</span>_IDR_x</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">GPIO_<span style="font-weight: bold;">OTYPER</span>_ODR_x</span> changed to <span style="font-style: italic;">GPIO_<span style="font-weight: bold;">ODR</span>_ODR_x</span></span><span style="font-size: 10pt; font-family: Verdana;"></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">SYSCFG_PMC_MII_RMII</span> changed to&nbsp;</span><span style="font-size: 10pt; font-family: Verdana; font-style: italic;">SYSCFG_PMC_MII_RMII<span style="font-weight: bold;">_SEL</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">RCC_APB2RSTR_SPI1</span> changed to&nbsp;<span style="font-style: italic;">RCC_APB2RSTR_SPI1<span style="font-weight: bold;">RST</span></span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">DBGMCU_APB1_FZ_DBG_IWD<span style="font-weight: bold;">E</span>G_STOP</span> changed to&nbsp;<span style="font-style: italic;">DBGMCU_APB1_FZ_DBG_IWDG_STOP</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">PWR_CR_PMODE</span> changed to&nbsp;<span style="font-style: italic;">PWR_CR_VOS</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">PWR_CSR_REGRDY</span> changed to&nbsp;<span style="font-style: italic;">PWR_CSR_VOSRDY</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Add new define <span style="font-style: italic;">RCC_AHB1ENR_CCMDATARAMEN</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Add new defines&nbsp;<span style="font-style: italic;">SRAM2_BASE, CCMDATARAM_BASE </span>and<span style="font-style: italic;"> BKPSRAM_BASE</span></span></li></ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">GPIO_TypeDef structure: in the comment change AFR[2] address mapping&nbsp;to <span style="font-style: italic;">0x20-0x24</span> instead of <span style="font-style: italic;">0x24-0x28</span></span></li></ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">system_stm32f4xx.c</span></li><ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">SystemInit()</span>: add code to enable the FPU</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">SetSysClock()</span>: change <span style="font-style: italic;">PWR_CR_PMODE</span> by&nbsp;<span style="font-style: italic;">PWR_CR_VOS</span></span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">SystemInit_ExtMemCtl()</span>: remove commented values</span></li></ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">startup (for all compilers)</span></li><ul><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Delete code used to enable the FPU (moved to system_stm32f4xx.c file)</span></li><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">File&#8217;s header updated</span></li></ul></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial; margin-right: 500pt; width: 176px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.0.0RC1 / 25-August-2011<o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>
<ul style="margin-top: 0cm;" type="square"><li class="MsoNormal" style="color: black; margin-top: 4.5pt; margin-bottom: 4.5pt;"><span style="font-size: 10pt; font-family: Verdana;">Official version (V1.0.0) Release Candidate1 for <span style="font-weight: bold; font-style: italic;">STM32F4xx devices</span></span></li></ul><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;"></span><span style="font-weight: bold; font-style: italic;"></span></span>

<ul style="margin-top: 0in;" type="disc">
</ul><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;; color: black;"></span>
<div class="MsoNormal" style="text-align: center;" align="center"><span style="color: black;">
<hr align="center" size="2" width="100%"></span></div>
<p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt; text-align: center;" align="center"><span style="font-size: 10pt; font-family: Verdana; color: black;">For
complete documentation on </span><span style="font-size: 10pt; font-family: Verdana;">STM32<span style="color: black;"> Microcontrollers
visit </span><u><span style="color: blue;"><a href="http://www.st.com/internet/mcu/family/141.jsp" target="_blank">www.st.com/STM32</a></span></u></span><span style="color: black;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
<p class="MsoNormal"><span style="font-size: 10pt;"><o:p></o:p></span></p>
</td>
</tr>
</tbody>
</table>
</div>
<p class="MsoNormal"><o:p>&nbsp;</o:p></p>
</div>
</body></html>