## 2. 系统架构设计

### 2.1 系统整体架构

系统采用典型的嵌入式分层架构设计，确保各模块的独立性、可维护性和可扩展性。

```
数据采集层 → 数据处理层 → 步态分析层 → 反馈控制层 → 反馈执行层 → 用户
     ↑                ↑           ↑           ↑                ↓
     |                |           |           |                |
     |                ↑           |           ↑                |
     |                |           |           |                |
用户交互层 ←---------|-----------|-----------|----------------|
     ↑                                        ↑
     |                                        |
     ↓                                        ↓
     |                                        |
电源管理层 →-------------------------------------→
```

*   **数据采集层**：负责原始传感器数据的获取，包括 VL53L0X 的距离数据和九轴 IMU（加速度、陀螺仪、磁力计）的原始数据。
*   **数据处理层**：对原始传感器数据进行预处理，如单位转换、校准、滤波、姿态解算（IMU）。将 VL53L0X 的距离数据结合 IMU 的姿态信息，通过几何模型和数据融合算法，精确计算出关节的弯曲角度。
*   **步态分析层**：基于处理后的关节角度和步态特征数据，采用设定的阈值或更复杂的算法（如状态机、简单模式识别），判断当前步态属于哪种预设模式（如正常、需调整、异常）。
*   **反馈控制层**：根据步态分析层输出的模式结果，生成相应的控制信号，包括 LED 的 PWM 参数、步进电机的脉冲序列、语音模块的播放指令。
*   **反馈执行层**：接收反馈控制层的信号，驱动 LED 发光、控制步进电机转动、触发语音模块播放语音。
*   **用户交互层**：处理按键输入，响应用户操作，如切换界面、进入校准模式、设置参数等。并将用户输入传递给数据处理层或反馈控制层。
*   **数据显示层**：接收数据处理层和步态分析层的数据，在 OLED 屏幕上进行可视化显示，如实时角度曲线、当前模式、电量信息等。
*   **电源管理层**：负责系统各模块的供电、电池充放电管理、电量监测，并根据系统状态（如低电量）调整供电策略，实现低功耗管理。

**系统数据流与控制流**：

1. **数据流向**：
   - 原始传感器数据 → 预处理 → 特征提取 → 模式识别 → 反馈生成
   - 用户操作 → 用户界面更新 → 系统参数调整
   - 系统状态 → 显示更新 → 用户感知

2. **控制流向**：
   - 系统状态机管理所有模块的工作状态
   - 用户交互事件触发状态转换
   - 异常事件触发应急处理流程
   - 低电量事件触发功耗管理策略

3. **时序设计**：
   - 高优先级：传感器数据采集、关键中断处理（定时器、外部事件）
   - 中优先级：数据处理、步态分析、反馈控制
   - 低优先级：显示更新、状态报告、非关键任务

系统架构设计充分考虑了实时性、可靠性和可扩展性要求，通过模块化和分层设计，使各功能单元职责明确、接口清晰，便于开发调试和未来功能扩展。

### 2.2 功能模块详细划分

1. **核心控制单元 (MCU)**: 基于 STM32F103C8T6，负责协调和管理整个系统的数据流和控制逻辑。
   - 处理器：ARM Cortex-M3内核，72MHz主频
   - 内存资源：64KB Flash，20KB SRAM
   - 外设控制：管理所有外设的初始化、配置和通信
   - 系统控制：维护系统状态机，协调各功能模块间的数据交换
   - 时钟管理：控制系统时钟和低功耗模式切换
   - 中断处理：处理外部中断和定时器中断

2. **距离传感单元**: 采用 VL53L0X，通过 I2C 接口提供高精度距离测量。
   - 传感器配置：设置测量模式、精度、采样率
   - 距离数据采集：定时获取距离数据
   - 异常检测：识别测量异常和传感器故障
   - 校准管理：执行零点校准和距离补偿
   - 数据预处理：滤波和初步转换

3. **惯性测量单元 (IMU)**: 采用 MPU9250 (或类似)，通过 I2C/SPI 接口提供三轴加速度、角速度、地磁场数据。
   - 传感器配置：设置量程、采样率、滤波
   - 原始数据采集：读取三轴加速度、角速度、磁场数据
   - 姿态解算：运行姿态估计算法，计算欧拉角或四元数
   - 校准管理：执行加速度计、陀螺仪、磁力计校准
   - 数据预处理：零偏补偿、滤波、单位转换

4. **数据显示单元**: 0.96寸 OLED 屏幕，通过 SPI/I2C 接口显示信息。
   - 显示驱动：OLED初始化和基本图形绘制
   - 界面管理：多界面设计和切换逻辑
   - 数据可视化：实时数据曲线、状态图标、文字显示
   - 菜单系统：设置菜单和参数调整界面
   - 动画效果：状态转换动画和提示效果

5. **按键输入单元**: 多个微动开关，连接至 MCU 的 GPIO，实现用户交互。
   - 按键扫描：定时检测按键状态
   - 按键消抖：软件消除机械抖动
   - 事件识别：短按、长按、连按等多种操作
   - 按键映射：根据当前界面将按键事件映射到功能
   - 组合按键：识别多按键组合操作

6. **LED 视觉反馈单元**: RGB LED，通过 MCU 的 PWM 输出控制颜色和亮度/闪烁。
   - 颜色控制：通过PWM调节RGB分量实现多彩显示
   - 亮度控制：PWM占空比调节实现亮度变化
   - 闪烁控制：定时器控制LED开关实现闪烁效果
   - 呼吸灯效果：渐变PWM实现平滑明暗变化
   - 状态指示：不同颜色和模式对应不同系统状态

7. **步进电机触觉反馈单元**: 28BYJ-48 步进电机及其驱动器 ULN2003，通过 MCU 的 GPIO 控制转动。
   - 步进序列控制：生成正确的四相驱动序列
   - 速度控制：调整脉冲频率控制转速
   - 振动模式：实现不同强度和节奏的振动
   - 能耗管理：非工作状态释放电机减少功耗
   - 安全保护：防止长时间连续工作导致过热

8. **语音听觉反馈单元**: 语音播放模块，通过 UART 或 GPIO 接口触发语音播放。
   - 语音指令发送：通过UART发送播放命令
   - 音量控制：调整语音提示音量
   - 多语言支持：预存多种语言的提示音
   - 提示管理：根据状态选择适当的语音提示
   - 优先级控制：管理语音提示的打断和排队

9. **电源管理单元**: 包含锂电池、充电电路 (如 TP4056)、升压电路 (如 MT3608)、电量检测电路。
   - 电池充电：管理充电过程和充电状态指示
   - 电压转换：3.7V电池电压升压至系统所需电压
   - 电量监测：通过ADC采样电池电压估算剩余电量
   - 低功耗控制：根据系统状态启用不同功耗策略
   - 安全保护：过流保护、过放保护、短路保护

10. **算法处理单元**：软件层面的功能模块，运行在MCU上。
    - 数据融合：结合多传感器数据计算关节角度
    - 步态分析：识别步态模式和异常情况
    - 校准算法：实现传感器自动校准
    - 滤波算法：卡尔曼滤波、互补滤波等
    - 参数自适应：动态调整算法参数适应不同用户

11. **数据存储单元**（可选扩展）：
    - 存储介质：SPI Flash或SD卡
    - 文件系统：简易文件系统实现
    - 数据记录：周期性保存关键数据
    - 数据检索：查询历史数据功能
    - 数据导出：通过串口或无线接口导出数据

每个功能模块都设计为相对独立的单元，通过标准化接口与其他模块交互，这种模块化设计有利于开发过程中的分工协作，也便于后期的维护和升级。同时，各模块之间的依赖关系被明确定义，确保系统的可靠性和可扩展性。 