#include "AFE_Test.h"
#include "stm32f10x_gpio.h"
#include "stm32f10x_rcc.h"
#include "stm32f10x_exti.h"
#include "misc.h"
#include "OLED.h"
#include "Delay.h"
#include <stdio.h>

//==============================================================================
// 全局变量定义
//==============================================================================

uint16_t g_adc_buffer[AFE_BUFFER_SIZE];      // ADC数据缓存
volatile uint16_t g_buffer_index = 0;        // 缓存索引
volatile uint8_t g_data_ready_flag = 0;      // 数据准备就绪标志

// 显示缓冲区
static char display_buffer[32];

//==============================================================================
// GPIO初始化
//==============================================================================

/**
 * @brief  初始化AFE板GPIO引脚
 * @param  None
 * @retval None
 */
void AFE_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能GPIO时钟
    RCC_APB2PeriphClockCmd(AFE_DATA_GPIO_CLK | AFE_CLK_GPIO_CLK, ENABLE);

    // 配置数据线为输入模式 (PC0-PC11)
    GPIO_InitStructure.GPIO_Pin = AFE_DATA_PINS;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(AFE_DATA_GPIO_PORT, &GPIO_InitStructure);

    // 配置时钟线为输入模式 (PA0)
    GPIO_InitStructure.GPIO_Pin = AFE_CLK_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(AFE_CLK_GPIO_PORT, &GPIO_InitStructure);
}

//==============================================================================
// 外部中断初始化
//==============================================================================

/**
 * @brief  初始化外部中断
 * @param  None
 * @retval None
 */
void AFE_EXTI_Init(void)
{
    EXTI_InitTypeDef EXTI_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // 使能AFIO时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);

    // 配置EXTI线
    GPIO_EXTILineConfig(AFE_CLK_EXTI_PORT_SOURCE, AFE_CLK_EXTI_PIN_SOURCE);

    // 配置EXTI
    EXTI_InitStructure.EXTI_Line = AFE_CLK_EXTI_LINE;
    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;
    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising;  // 上升沿触发
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_Init(&EXTI_InitStructure);

    // 配置NVIC
    NVIC_InitStructure.NVIC_IRQChannel = AFE_CLK_EXTI_IRQ;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

//==============================================================================
// AFE测试初始化
//==============================================================================

/**
 * @brief  AFE测试模块初始化
 * @param  None
 * @retval None
 */
void AFE_Test_Init(void)
{
    // 初始化GPIO
    AFE_GPIO_Init();

    // 初始化外部中断
    AFE_EXTI_Init();

    // 清空缓存
    AFE_ClearBuffer();
}

//==============================================================================
// 数据读取函数
//==============================================================================

/**
 * @brief  读取AFE板12位并行数据
 * @param  None
 * @retval 12位ADC数据
 */
uint16_t AFE_ReadADCData(void)
{
    uint16_t data = 0;
    uint16_t port_data = GPIO_ReadInputData(AFE_DATA_GPIO_PORT);

    // 提取PC0-PC11的数据 (12位)
    data = port_data & AFE_DATA_MASK;

    return data;
}

//==============================================================================
// 缓存管理函数
//==============================================================================

/**
 * @brief  清空ADC数据缓存
 * @param  None
 * @retval None
 */
void AFE_ClearBuffer(void)
{
    uint16_t i;

    // 禁用中断
    AFE_DisableInterrupt();

    // 清空缓存
    for(i = 0; i < AFE_BUFFER_SIZE; i++)
    {
        g_adc_buffer[i] = 0;
    }

    // 重置索引和标志
    g_buffer_index = 0;
    g_data_ready_flag = 0;

    // 重新使能中断
    AFE_EnableInterrupt();
}

//==============================================================================
// 中断控制函数
//==============================================================================

/**
 * @brief  使能AFE时钟中断
 * @param  None
 * @retval None
 */
void AFE_EnableInterrupt(void)
{
    EXTI_ClearITPendingBit(AFE_CLK_EXTI_LINE);
    NVIC_EnableIRQ(AFE_CLK_EXTI_IRQ);
}

/**
 * @brief  禁用AFE时钟中断
 * @param  None
 * @retval None
 */
void AFE_DisableInterrupt(void)
{
    NVIC_DisableIRQ(AFE_CLK_EXTI_IRQ);
}

//==============================================================================
// 数据处理函数
//==============================================================================

/**
 * @brief  计算ADC数据统计信息
 * @param  buffer: 数据缓存指针
 * @param  length: 数据长度
 * @param  stats: 统计结果指针
 * @retval None
 */
void AFE_CalculateStats(uint16_t *buffer, uint16_t length, ADC_Stats_t *stats)
{
    uint16_t i;
    uint32_t sum = 0;
    uint16_t max_val = 0;
    uint16_t min_val = 0xFFFF;

    if(buffer == NULL || stats == NULL || length == 0)
        return;

    // 计算最大值、最小值和总和
    for(i = 0; i < length; i++)
    {
        if(buffer[i] > max_val)
            max_val = buffer[i];
        if(buffer[i] < min_val)
            min_val = buffer[i];
        sum += buffer[i];
    }

    // 更新统计信息
    stats->max_value = max_val;
    stats->min_value = min_val;
    stats->average_value = sum / length;
    stats->sample_count += length;
    stats->current_value = buffer[length - 1];  // 最新值
}

/**
 * @brief  处理ADC数据
 * @param  stats: 统计结果指针
 * @retval None
 */
void AFE_ProcessData(ADC_Stats_t *stats)
{
    if(g_data_ready_flag)
    {
        // 禁用中断防止数据被修改
        AFE_DisableInterrupt();

        // 计算统计信息
        AFE_CalculateStats(g_adc_buffer, AFE_BUFFER_SIZE, stats);

        // 清除数据就绪标志
        g_data_ready_flag = 0;

        // 重置缓存索引
        g_buffer_index = 0;

        // 重新使能中断
        AFE_EnableInterrupt();
    }
}

//==============================================================================
// 显示函数
//==============================================================================

/**
 * @brief  显示ADC测试结果
 * @param  stats: 统计结果指针
 * @retval None
 */
void Display_ADC_Results(ADC_Stats_t *stats)
{
    if(stats == NULL)
        return;

    OLED_Clear();

    // 显示标题
    OLED_ShowString(0, 0, "AFE ADC Test", 6);

    // 显示当前值
    sprintf(display_buffer, "Cur:%4d", stats->current_value);
    OLED_ShowString(0, 16, (uint8_t*)display_buffer, 6);

    // 显示最大值
    sprintf(display_buffer, "Max:%4d", stats->max_value);
    OLED_ShowString(0, 32, (uint8_t*)display_buffer, 6);

    // 显示最小值
    sprintf(display_buffer, "Min:%4d", stats->min_value);
    OLED_ShowString(0, 48, (uint8_t*)display_buffer, 6);

    // 显示平均值
    sprintf(display_buffer, "Avg:%4d", (uint16_t)stats->average_value);
    OLED_ShowString(64, 16, (uint8_t*)display_buffer, 6);

    // 显示采样计数
    sprintf(display_buffer, "Cnt:%4d", (uint16_t)(stats->sample_count % 10000));
    OLED_ShowString(64, 32, (uint8_t*)display_buffer, 6);

    OLED_Update();
}

//==============================================================================
// 中断服务函数
//==============================================================================

/**
 * @brief  外部中断0服务函数 (AFE时钟中断)
 * @param  None
 * @retval None
 */
void EXTI0_IRQHandler(void)
{
    if(EXTI_GetITStatus(AFE_CLK_EXTI_LINE) != RESET)
    {
        // 读取AFE板12位并行数据
        uint16_t adc_data = AFE_ReadADCData();

        // 存储到缓存
        if(g_buffer_index < AFE_BUFFER_SIZE)
        {
            g_adc_buffer[g_buffer_index] = adc_data;
            g_buffer_index++;

            // 缓存满时设置数据就绪标志
            if(g_buffer_index >= AFE_BUFFER_SIZE)
            {
                g_data_ready_flag = 1;
            }
        }

        // 清除中断标志
        EXTI_ClearITPendingBit(AFE_CLK_EXTI_LINE);
    }
}


