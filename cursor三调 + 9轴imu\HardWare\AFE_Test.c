#include "AFE_Test.h"
#include "stm32f10x_adc.h"
#include "stm32f10x_gpio.h"
#include "stm32f10x_rcc.h"
#include "OLED.h"
#include "Delay.h"
#include <math.h>

// 全局变量定义
AFE_Config_t g_afe_config = {
    .test_mode = AFE_TEST_SINGLE_CHANNEL,
    .active_channels = 0x0F,        // 默认激活所有4个通道
    .sample_rate = AFE_SAMPLE_RATE,
    .gain_setting = 1,
    .reference_voltage = 3.3f       // STM32 参考电压
};

AFE_Data_t g_afe_data[AFE_ADC_CHANNELS];
AFE_TestResult_t g_afe_results[AFE_ADC_CHANNELS];

// 私有变量
static uint8_t afe_test_running = 0;
static uint32_t sample_counter = 0;

/**
 * @brief AFE板初始化
 */
void AFE_Init(void)
{
    // 初始化ADC
    AFE_ADC_Init();
    
    // 初始化数据结构
    for(int i = 0; i < AFE_ADC_CHANNELS; i++)
    {
        g_afe_data[i].channel = i;
        g_afe_data[i].valid = 0;
        
        g_afe_results[i].min_voltage = 999.0f;
        g_afe_results[i].max_voltage = 0.0f;
        g_afe_results[i].avg_voltage = 0.0f;
        g_afe_results[i].sample_count = 0;
    }
    
    afe_test_running = 0;
    sample_counter = 0;
}

/**
 * @brief ADC初始化配置
 */
void AFE_ADC_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    ADC_InitTypeDef ADC_InitStructure;
    
    // 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_ADC1, ENABLE);
    RCC_ADCCLKConfig(RCC_PCLK2_Div6);  // ADC时钟设置
    
    // 配置GPIO为模拟输入
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置ADC
    ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_InitStructure.ADC_ScanConvMode = ENABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_None;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfChannel = AFE_ADC_CHANNELS;
    ADC_Init(ADC1, &ADC_InitStructure);
    
    // 配置采样时间
    ADC_RegularChannelConfig(ADC1, ADC_Channel_0, 1, ADC_SampleTime_55Cycles5);
    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 2, ADC_SampleTime_55Cycles5);
    ADC_RegularChannelConfig(ADC1, ADC_Channel_2, 3, ADC_SampleTime_55Cycles5);
    ADC_RegularChannelConfig(ADC1, ADC_Channel_3, 4, ADC_SampleTime_55Cycles5);
    
    // 使能ADC
    ADC_Cmd(ADC1, ENABLE);
    
    // ADC校准
    ADC_ResetCalibration(ADC1);
    while(ADC_GetResetCalibrationStatus(ADC1));
    ADC_StartCalibration(ADC1);
    while(ADC_GetCalibrationStatus(ADC1));
}

/**
 * @brief 开始AFE测试
 */
void AFE_Start_Test(AFE_TestMode_t mode)
{
    g_afe_config.test_mode = mode;
    afe_test_running = 1;
    sample_counter = 0;
    
    // 重置测试结果
    for(int i = 0; i < AFE_ADC_CHANNELS; i++)
    {
        g_afe_results[i].min_voltage = 999.0f;
        g_afe_results[i].max_voltage = 0.0f;
        g_afe_results[i].avg_voltage = 0.0f;
        g_afe_results[i].sample_count = 0;
    }
}

/**
 * @brief 停止AFE测试
 */
void AFE_Stop_Test(void)
{
    afe_test_running = 0;
}

/**
 * @brief 读取单个通道数据
 */
uint8_t AFE_Read_Channel(uint8_t channel, AFE_Data_t* data)
{
    if(channel >= AFE_ADC_CHANNELS || data == NULL)
        return 0;
    
    uint8_t adc_channel;
    switch(channel)
    {
        case 0: adc_channel = ADC_Channel_0; break;
        case 1: adc_channel = ADC_Channel_1; break;
        case 2: adc_channel = ADC_Channel_2; break;
        case 3: adc_channel = ADC_Channel_3; break;
        default: return 0;
    }
    
    // 配置ADC通道
    ADC_RegularChannelConfig(ADC1, adc_channel, 1, ADC_SampleTime_55Cycles5);
    ADC_SoftwareStartConvCmd(ADC1, ENABLE);
    
    // 等待转换完成
    while(!ADC_GetFlagStatus(ADC1, ADC_FLAG_EOC));
    
    // 读取数据
    data->raw_value = ADC_GetConversionValue(ADC1);
    data->voltage = AFE_Convert_To_Voltage(data->raw_value);
    data->channel = channel;
    data->timestamp = sample_counter++;
    data->valid = 1;
    
    return 1;
}

/**
 * @brief 读取所有通道数据
 */
uint8_t AFE_Read_All_Channels(AFE_Data_t* data_array)
{
    if(data_array == NULL)
        return 0;
    
    for(int i = 0; i < AFE_ADC_CHANNELS; i++)
    {
        if(!AFE_Read_Channel(i, &data_array[i]))
            return 0;
        delay_ms(1);  // 通道间延时
    }
    
    return 1;
}

/**
 * @brief 将ADC值转换为电压
 */
float AFE_Convert_To_Voltage(uint16_t adc_value)
{
    return (float)adc_value * g_afe_config.reference_voltage / 4096.0f;
}

/**
 * @brief 处理AFE数据并计算统计结果
 */
void AFE_Process_Data(AFE_Data_t* data, AFE_TestResult_t* result)
{
    if(data == NULL || result == NULL || !data->valid)
        return;
    
    // 更新最小值和最大值
    if(data->voltage < result->min_voltage)
        result->min_voltage = data->voltage;
    if(data->voltage > result->max_voltage)
        result->max_voltage = data->voltage;
    
    // 计算平均值 (移动平均)
    result->avg_voltage = (result->avg_voltage * result->sample_count + data->voltage) / (result->sample_count + 1);
    result->sample_count++;
}

/**
 * @brief AFE测试主任务
 */
void AFE_Test_Task(void)
{
    if(!afe_test_running)
        return;
    
    // 根据测试模式执行不同的测试
    switch(g_afe_config.test_mode)
    {
        case AFE_TEST_SINGLE_CHANNEL:
            // 单通道测试 - 只测试通道0
            if(AFE_Read_Channel(0, &g_afe_data[0]))
            {
                AFE_Process_Data(&g_afe_data[0], &g_afe_results[0]);
            }
            break;
            
        case AFE_TEST_MULTI_CHANNEL:
            // 多通道测试
            if(AFE_Read_All_Channels(g_afe_data))
            {
                for(int i = 0; i < AFE_ADC_CHANNELS; i++)
                {
                    AFE_Process_Data(&g_afe_data[i], &g_afe_results[i]);
                }
            }
            break;
            
        case AFE_TEST_CONTINUOUS:
            // 连续采集测试
            if(AFE_Read_All_Channels(g_afe_data))
            {
                for(int i = 0; i < AFE_ADC_CHANNELS; i++)
                {
                    AFE_Process_Data(&g_afe_data[i], &g_afe_results[i]);
                }
            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief 显示AFE测试结果
 */
void AFE_Display_Results(AFE_TestResult_t* results)
{
    if(results == NULL)
        return;

    OLED_Clear();

    // 显示标题
    OLED_ShowString(0, 0, "AFE Test Results", 6);

    // 根据测试模式显示不同内容
    switch(g_afe_config.test_mode)
    {
        case AFE_TEST_SINGLE_CHANNEL:
            // 单通道详细显示
            OLED_ShowString(0, 16, "CH0:", 6);
            OLED_ShowFloatNum(30, 16, g_afe_data[0].voltage, 2, 3, 6);
            OLED_ShowString(80, 16, "V", 6);

            OLED_ShowString(0, 32, "Min:", 6);
            OLED_ShowFloatNum(30, 32, results[0].min_voltage, 2, 3, 6);
            OLED_ShowString(0, 48, "Max:", 6);
            OLED_ShowFloatNum(30, 48, results[0].max_voltage, 2, 3, 6);
            break;

        case AFE_TEST_MULTI_CHANNEL:
            // 多通道概览显示
            for(int i = 0; i < 4 && i < AFE_ADC_CHANNELS; i++)
            {
                OLED_ShowString(0, 16 + i*12, "CH", 6);
                OLED_ShowNum(12, 16 + i*12, i, 1, 6);
                OLED_ShowString(18, 16 + i*12, ":", 6);
                OLED_ShowFloatNum(24, 16 + i*12, g_afe_data[i].voltage, 1, 2, 6);
                OLED_ShowString(50, 16 + i*12, "V", 6);

                // 显示采样计数
                OLED_ShowNum(60, 16 + i*12, results[i].sample_count, 3, 6);
            }
            break;

        case AFE_TEST_CONTINUOUS:
            // 连续测试显示
            OLED_ShowString(0, 16, "Continuous Mode", 6);
            OLED_ShowString(0, 32, "Samples:", 6);
            OLED_ShowNum(60, 32, results[0].sample_count, 4, 6);
            OLED_ShowString(0, 48, "Rate:", 6);
            OLED_ShowNum(30, 48, g_afe_config.sample_rate, 4, 6);
            OLED_ShowString(70, 48, "Hz", 6);
            break;

        default:
            OLED_ShowString(0, 32, "Unknown Mode", 6);
            break;
    }

    OLED_Update();
}

/**
 * @brief AFE校准功能
 */
void AFE_Calibrate(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "AFE Calibration", 6);
    OLED_ShowString(0, 16, "Connect 0V to all", 6);
    OLED_ShowString(0, 32, "channels", 6);
    OLED_ShowString(0, 48, "Press key to start", 6);
    OLED_Update();

    // 等待用户操作...
    delay_ms(3000);

    // 执行零点校准
    float zero_offsets[AFE_ADC_CHANNELS] = {0};

    OLED_Clear();
    OLED_ShowString(0, 16, "Calibrating...", 6);
    OLED_Update();

    // 采集多次求平均作为零点偏移
    for(int sample = 0; sample < 100; sample++)
    {
        AFE_Data_t cal_data[AFE_ADC_CHANNELS];
        if(AFE_Read_All_Channels(cal_data))
        {
            for(int ch = 0; ch < AFE_ADC_CHANNELS; ch++)
            {
                zero_offsets[ch] += cal_data[ch].voltage;
            }
        }
        delay_ms(10);
    }

    // 计算平均偏移
    for(int ch = 0; ch < AFE_ADC_CHANNELS; ch++)
    {
        zero_offsets[ch] /= 100.0f;
    }

    // 显示校准结果
    OLED_Clear();
    OLED_ShowString(0, 0, "Calibration Done", 6);
    for(int i = 0; i < AFE_ADC_CHANNELS && i < 4; i++)
    {
        OLED_ShowString(0, 16 + i*12, "CH", 6);
        OLED_ShowNum(12, 16 + i*12, i, 1, 6);
        OLED_ShowString(18, 16 + i*12, ":", 6);
        OLED_ShowFloatNum(24, 16 + i*12, zero_offsets[i], 1, 3, 6);
        OLED_ShowString(60, 16 + i*12, "V", 6);
    }
    OLED_Update();

    delay_ms(3000);
}


