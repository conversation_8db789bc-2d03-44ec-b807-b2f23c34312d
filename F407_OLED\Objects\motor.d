.\objects\motor.o: Hardware\motor.c
.\objects\motor.o: Hardware\motor.h
.\objects\motor.o: .\User\stm32f4xx.h
.\objects\motor.o: .\Start\core_cm4.h
.\objects\motor.o: E:\Keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\motor.o: .\Start\core_cmInstr.h
.\objects\motor.o: .\Start\core_cmFunc.h
.\objects\motor.o: .\Start\core_cmSimd.h
.\objects\motor.o: .\User\system_stm32f4xx.h
.\objects\motor.o: .\User\stm32f4xx_conf.h
.\objects\motor.o: .\Library\inc\stm32f4xx_adc.h
.\objects\motor.o: .\User\stm32f4xx.h
.\objects\motor.o: .\Library\inc\stm32f4xx_crc.h
.\objects\motor.o: .\Library\inc\stm32f4xx_dbgmcu.h
.\objects\motor.o: .\Library\inc\stm32f4xx_dma.h
.\objects\motor.o: .\Library\inc\stm32f4xx_exti.h
.\objects\motor.o: .\Library\inc\stm32f4xx_flash.h
.\objects\motor.o: .\Library\inc\stm32f4xx_gpio.h
.\objects\motor.o: .\Library\inc\stm32f4xx_i2c.h
.\objects\motor.o: .\Library\inc\stm32f4xx_iwdg.h
.\objects\motor.o: .\Library\inc\stm32f4xx_pwr.h
.\objects\motor.o: .\Library\inc\stm32f4xx_rcc.h
.\objects\motor.o: .\Library\inc\stm32f4xx_rtc.h
.\objects\motor.o: .\Library\inc\stm32f4xx_sdio.h
.\objects\motor.o: .\Library\inc\stm32f4xx_spi.h
.\objects\motor.o: .\Library\inc\stm32f4xx_syscfg.h
.\objects\motor.o: .\Library\inc\stm32f4xx_tim.h
.\objects\motor.o: .\Library\inc\stm32f4xx_usart.h
.\objects\motor.o: .\Library\inc\stm32f4xx_wwdg.h
.\objects\motor.o: .\Library\inc\misc.h
.\objects\motor.o: .\Library\inc\stm32f4xx_cryp.h
.\objects\motor.o: .\Library\inc\stm32f4xx_hash.h
.\objects\motor.o: .\Library\inc\stm32f4xx_rng.h
.\objects\motor.o: .\Library\inc\stm32f4xx_can.h
.\objects\motor.o: .\Library\inc\stm32f4xx_dac.h
.\objects\motor.o: .\Library\inc\stm32f4xx_dcmi.h
.\objects\motor.o: .\Library\inc\stm32f4xx_fsmc.h
.\objects\motor.o: .\System\delay.h
