## 4. 软件设计

### 4.1 软件架构

本系统软件采用基于STM32F1的嵌入式裸机开发模式，结合多任务协作和状态机管理思想。选择裸机开发而非RTOS（实时操作系统）是基于项目对实时性、资源限制和开发复杂度的权衡。裸机开发可以最大程度地控制代码执行流程和资源开销，适合资源受限的微控制器应用。通过精心设计的软件架构，可以模拟多任务并行执行的效果。

**软件层次结构**：

1.  **硬件抽象层 (HAL)**：提供对底层硬件寄存器和外设的统一访问接口。封装了GPIO、定时器、UART、SPI、I2C、ADC等外设的初始化和基本操作函数。这一层向上屏蔽了具体的硬件细节，提高了软件的可移植性。

2.  **设备驱动层**：基于HAL层，为特定的硬件设备（如VL53L0X、MPU9250、OLED、按键、LED、步进电机、语音模块）提供功能驱动接口。例如，VL53L0X驱动负责通过I2C读写寄存器获取距离数据；OLED驱动提供画点、画线、显示字符等高级功能。

3.  **中间件层**：实现传感器数据的处理、融合、滤波算法，以及步态模式识别、反馈控制逻辑。这是系统的核心处理层。包括：
    *   数据处理模块：单位转换、校准补偿
    *   姿态解算模块：运行IMU融合算法（如Madgwick滤波）
    *   角度计算模块：基于VL53L0X和IMU数据计算关节角度
    *   滤波模块：对原始或计算数据进行平滑处理
    *   步态分析模块：根据角度数据识别步态模式
    *   反馈控制模块：根据步态模式生成反馈指令

4.  **应用层**：实现系统的顶层逻辑和用户交互界面。包括：
    *   系统状态机管理模块：控制系统在初始化、校准、运行、低功耗、错误等状态之间切换。
    *   任务调度模块：伪多任务调度，周期性调用各个功能模块的处理函数。
    *   用户界面管理模块：处理按键输入，管理OLED显示内容的切换和更新。
    *   电源管理控制模块：根据电量和系统状态控制功耗。
    *   数据存储管理模块（可选）：负责历史数据的记录和读取。

**多任务协作模型**：

虽然是裸机开发，但通过以下方式实现多任务的并发执行感：

1.  **基于时间的任务轮询**：利用MCU的定时器生成周期性中断，在中断服务程序(ISR)中设置任务标志位。主循环不断检查这些标志位，并调用对应的任务处理函数。例如，设置1ms的定时器中断，用于传感器数据采集和按键扫描；设置10ms或100ms的定时器中断，用于数据处理、步态分析和显示更新。

2.  **基于事件的任务触发**：通过外部中断（如按键按下、传感器数据就绪）或内部事件（如电量低于阈值）触发相应的事件处理函数或设置任务标志位。

3.  **主循环的低优先级任务**：主循环中执行优先级较低、对实时性要求不高的任务，如屏幕刷新、状态机转换、空闲时进入低功耗模式。

**系统状态机设计**：

系统通过状态机来管理复杂的运行流程，确保在不同状态下执行正确的操作并响应用户输入。主要状态包括：

*   **SYS_STATE_IDLE**：系统上电或重置后的初始状态，等待用户操作或进入初始化。
*   **SYS_STATE_INIT**：系统初始化状态，进行硬件初始化、外设配置、参数加载等。成功后进入校准状态。
*   **SYS_STATE_CALIBRATION**：传感器校准状态，引导用户完成校准流程。根据用户输入和传感器数据进行校准计算。校准成功后进入运行状态。
*   **SYS_STATE_RUNNING**：正常运行状态，周期性采集数据、处理、分析步态、执行反馈、更新显示。响应用户按键输入进行模式切换或参数调整。
*   **SYS_STATE_LOW_POWER**：低功耗模式，降低采样率、关闭部分外设，延长续航。根据电量变化或用户操作退出此状态。
*   **SYS_STATE_MENU**：菜单界面状态，响应按键导航菜单、进行参数设置。退出菜单后返回之前的状态（如运行状态）。
*   **SYS_STATE_ERROR**：错误状态，检测到严重异常（如传感器故障、算法崩溃）时进入。停止正常工作，给出错误指示，等待用户处理或尝试恢复。
*   **SYS_STATE_SHUTDOWN**：关机状态，保存关键数据，安全关闭系统。

状态之间的转换由特定事件触发，如初始化完成、校准成功、按键按下、电量阈值、错误发生等。

### 4.2 主要功能模块实现

#### 4.2.1 传感器数据采集与预处理

*   **VL53L0X驱动模块**：
    *   实现I2C通信接口，读写VL53L0X寄存器。
    *   初始化传感器，配置测量模式（高精度、高速等）。
    *   实现距离数据读取函数，处理原始数据，返回毫米或厘米为单位的距离。
    *   处理多VL53L0X传感器的地址配置和数据读取。
    *   实现基本的距离滤波（如中值滤波）去除毛刺。
    *   检测传感器错误标志（如数据无效、饱和）。

*   **MPU9250驱动模块**：
    *   实现I2C（或SPI）通信接口，读写MPU9250寄存器。
    *   初始化MPU9250，配置加速度计、陀螺仪、磁力计量程和采样率。
    *   配置和启用内置的DMP（如果使用）。
    *   实现原始加速度、角速度、磁力计数据读取函数。
    *   实现数据就绪中断处理。
    *   数据预处理：将原始ADC值转换为物理单位（g, °/s, μT）。

#### 4.2.2 姿态解算与数据融合

*   **姿态解算模块**：
    *   实现基于四元数的Madgwick或Mahoney滤波算法。
    *   输入：经过校准和滤波的加速度计、陀螺仪、磁力计数据。
    *   输出：设备的实时姿态，表示为四元数或欧拉角（俯仰Pitch、滚转Roll、航向Yaw）。
    *   需要处理欧拉角的万向锁问题（如果直接使用欧拉角）。
    *   初始化姿态：通常在系统静止时使用加速度计和磁力计数据进行初始姿态估计。

*   **角度计算模块**：
    *   实现基于VL53L0X距离和IMU姿态的关节角度计算几何模型。
    *   输入：VL53L0X测量的距离值、两个佩戴设备的IMU姿态（或者一个IMU+一个VL53L0X）。
    *   输出：计算出的关节弯曲角度（0-180°）。
    *   需要处理传感器安装位置和角度计算的标定参数。

*   **传感器数据融合模块 (IMU + VL53L0X)**：
    *   将IMU计算出的关节相对角度变化与VL53L0X计算的关节绝对角度进行融合。
    *   实现互补滤波或扩展卡尔曼滤波算法。
    *   输入：IMU融合姿态计算出的角度、VL53L0X距离计算出的角度。
    *   输出：平滑且准确的最终关节角度数据。
    *   需要根据传感器噪声特性调整滤波参数。

#### 4.2.3 步态模式识别算法

*   **特征提取模块**：
    *   输入：融合后的关节角度时间序列。
    *   输出：步态特征参数，如当前角度、角度变化速度、加速度、在一个步态周期内的角度极值、角度变化范围、步态周期长度（如果进行步态周期检测）。
    *   计算角度的一阶和二阶差分得到速度和加速度。

*   **模式判断模块**：
    *   实现基于阈值的步态模式分类。
    *   输入：提取的步态特征和用户设定的阈值参数。
    *   输出：当前的步态模式（正常、调整、警告）。
    *   判断逻辑：根据当前关节角度是否落在预设的正常范围内、角度变化速度是否异常、或者是否在特定步态阶段出现异常特征来分类。

*   **步态周期检测模块**（可选高级功能）：
    *   利用IMU加速度计或陀螺仪数据检测关键步态事件（触地、离地）。
    *   实现峰值检测、阈值交叉检测或更复杂的基于模板匹配的算法。
    *   输入：IMU原始或滤波数据。
    *   输出：步态事件标志和步态周期长度。
    *   用于更精确地分析步态各阶段特征。

#### 4.2.4 反馈控制逻辑

*   **反馈生成模块**：
    *   输入：当前的步态模式（正常、调整、警告）和系统状态。
    *   输出：控制指令集，包括LED颜色/模式、步进电机动作、语音播放ID。
    *   实现步态模式到反馈指令的映射逻辑。
    *   考虑反馈的强度和持续时间，避免过度刺激。

*   **LED控制模块**：
    *   通过调用HAL库配置和更新PWM寄存器，控制RGB LED的颜色和亮度。
    *   实现呼吸、闪烁、渐变等动态效果函数。
    *   根据反馈生成模块的指令更新LED状态。

*   **步进电机控制模块**：
    *   生成步进电机的驱动脉冲序列，通过GPIO输出到ULN2003。
    *   实现不同转速（脉冲频率）和转动模式（连续、间断、往复）的控制函数。
    *   根据反馈生成模块的指令调用相应的电机控制函数。
    *   实现电机功耗管理策略。

*   **语音播放控制模块**：
    *   通过UART向语音模块发送播放指令。
    *   实现语音消息队列和优先级管理。
    *   根据反馈生成模块的指令调用语音播放函数。
    *   监测语音模块的BUSY信号，避免指令冲突。

#### 4.2.5 用户界面管理

*   **OLED驱动模块**：
    *   初始化SSD1306控制器，配置显示参数。
    *   实现基本绘图函数：画点、画线、画矩形、填充、清屏。
    *   实现字符和字符串显示函数，支持不同字体大小。
    *   实现图形显示函数：绘制曲线图、图标。
    *   实现屏幕刷新和局部更新功能。

*   **UI逻辑管理模块**：
    *   维护当前显示的界面状态（实时数据、菜单、设置等）。
    *   根据按键事件和系统状态切换界面。
    *   在不同界面下调用相应的显示更新函数。
    *   管理菜单系统的导航逻辑。
    *   处理用户在设置界面的参数输入和修改。

*   **按键处理模块**：
    *   实现GPIO中断服务程序或定时器轮询，捕获按键按下事件。
    *   实现软件按键消抖。
    *   识别按键操作类型（短按、长按、组合键）。
    *   根据当前UI界面状态，将按键事件映射到相应的UI逻辑。

#### 4.2.6 系统状态管理

*   **系统状态机模块**：
    *   维护当前系统状态变量。
    *   实现状态切换函数，在进入和退出每个状态时执行相应的操作（如初始化硬件、启动/停止传感器、进入低功耗）。
    *   根据外部事件（按键、定时器、传感器数据、电量状态、错误标志）触发状态转换。

*   **错误处理模块**：
    *   定义错误类型和错误级别。
    *   实现错误检测函数，监测传感器通信异常、算法异常、电量异常等。
    *   根据错误级别触发不同的处理流程（如记录错误日志、切换到错误状态、尝试恢复、给出警告）。

*   **电源管理控制模块**：
    *   周期性读取电池电压，估算电量。
    *   根据电量阈值切换电源管理状态（正常、省电、紧急、关机保护）。
    *   调用HAL库或自定义函数控制外设电源和MCU低功耗模式。

### 4.3 数据存储（可选扩展）

为支持历史数据回放和离线分析，可增加数据存储功能：

*   **存储介质驱动**：
    *   如果使用SPI Flash，需要编写SPI Flash的读、写、擦除驱动。
    *   如果使用SD卡，需要移植或编写SD卡的SPI或SDIO接口驱动，并实现FatFs等文件系统库。

*   **数据记录模块**：
    *   定义数据记录格式（如CSV或二进制格式）。
    *   设计数据记录结构体，包含时间戳、关节角度、步态模式、原始传感器数据等。
    *   在运行状态下，周期性地将处理后的数据写入存储介质。
    *   实现数据缓冲区管理，减少写入次数，提高效率和延长存储介质寿命。
    *   实现文件管理功能（创建、打开、关闭文件）。

*   **数据读取模块**：
    *   实现从存储介质读取历史数据。
    *   提供按时间范围、数据类型等条件检索数据的接口。
    *   将读取的数据用于在OLED上显示历史曲线或通过串口导出。

*   **数据导出功能**：
    *   通过UART接口实现简单的数据导出协议。
    *   用户可以在菜单中选择导出数据，系统将历史数据通过串口发送给PC或APP。
    *   可以考虑使用蓝牙或Wi-Fi模块实现无线数据导出（见扩展功能）。

### 4.4 软件开发环境与工具

*   **IDE**: Keil MDK、STM32CubeIDE、IAR Embedded Workbench等。
*   **编译器**: ARM Compiler (Keil)、GCC (STM32CubeIDE)。
*   **调试器**: ST-Link/V2、J-Link等。
*   **库函数**: STM32 HAL库或标准外设库 (SPL)。推荐使用HAL库，更加现代化和易于移植。
*   **配置工具**: STM32CubeMX，用于可视化配置MCU外设、时钟、GPIO等，自动生成初始化代码。
*   **版本控制**: Git，用于代码管理和团队协作。
*   **代码规范**: 遵循一套良好的C语言代码规范，提高代码可读性和可维护性。
*   **串口调试工具**: 如SecureCRT、XCOM等，用于查看调试信息和导出数据。

### 4.5 软件测试策略

软件测试是确保系统功能正确、稳定和可靠的关键环节。

1.  **单元测试**：对各个独立的软件模块（如传感器驱动、滤波函数、角度计算函数）进行测试，验证其功能是否符合设计要求。
2.  **集成测试**：将相关的模块组合起来进行测试，验证模块之间的接口和协作是否正确。
3.  **系统测试**：对整个系统进行端到端测试，模拟实际使用场景，验证系统功能、性能和用户体验是否满足需求。
    *   **传感器数据验证**：采集原始传感器数据，与已知参考值对比，验证驱动和初步处理的正确性。
    *   **算法精度测试**：在已知关节角度下，测试系统计算出的角度精度。可以使用角度仪、步态实验室的光学捕捉系统或带有高精度IMU的参考设备进行比对。
    *   **实时性测试**：测量从传感器数据采集到反馈输出的延迟。
    *   **功耗测试**：使用电流表测量不同工作模式下的电流消耗，评估续航。
    *   **功能测试**：全面测试按键交互、界面切换、步态模式识别和反馈的各个功能点。
    *   **鲁棒性测试**：在不同光照、运动速度、佩戴位置变化等条件下测试系统稳定性。
    *   **异常处理测试**：模拟传感器故障、通信异常、电量不足等情况，测试系统的错误处理和恢复能力。
4.  **用户验收测试 (UAT)**：邀请潜在用户在真实使用环境中测试系统，收集反馈意见，评估易用性和实用性。

通过多层次、多维度的软件测试，确保系统在各种复杂环境下都能稳定、准确地工作。 