/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   AFE板测试主程序 - 嘉立创天空星STM32F407ZGT6
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "afe_test.h"
// #include "OLED.h"  // 注释掉原来的OLED驱动
// #include "OLED_Fixed.h"  // 注释掉复杂版本
#include "OLED_Test.h"  // 使用测试版OLED驱动
#include "Delay.h"
#include <stdio.h>
#include <stdint.h>

/* Private variables ---------------------------------------------------------*/
ADC_Stats_t adc_stats;
char display_buffer[32];

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void Display_ADC_Results(ADC_Stats_t *stats);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统时钟初始化 */
    SystemInit();

    /* 初始化SysTick定时器 (1ms中断) */
    if (SysTick_Config(SystemCoreClock / 1000))
    {
        /* 配置失败，进入死循环 */
        while (1);
    }

    /* 初始化AFE测试模块 */
    AFE_Test_Init();

    /* 初始化OLED显示屏 */
    OLED_Init();
    Delay(500);  // 等待OLED稳定

    /* 测试OLED显示 - 显示测试图案 */
    OLED_Test_Display();

    /* 延时2秒观察显示效果 */
    Delay(2000);

    /* 清屏准备显示ADC数据 */
    OLED_Clear();
    OLED_Update();

    /* 初始化统计数据 */
    adc_stats.current_value = 0;
    adc_stats.max_value = 0;
    adc_stats.min_value = 4095;
    adc_stats.average_value = 0;
    adc_stats.sample_count = 0;

    /* 主循环 */
    while (1)
    {
        /* 检查数据是否准备就绪 */
        if (g_data_ready_flag)
        {
            /* 处理ADC数据 */
            AFE_ProcessData(&adc_stats);

            /* 显示结果 */
            Display_ADC_Results(&adc_stats);
        }
        else
        {
            /* 显示等待状态 */
            static uint32_t last_update = 0;
            if (GetTick() - last_update > 500) // 每500ms更新一次
            {
                last_update = GetTick();

                // 显示采样计数 (暂时注释掉，等字符显示函数完成)
                // sprintf(display_buffer, "Samples:%d", (int)g_buffer_index);
                // OLED_ShowString(0, 16, display_buffer, OLED_8X16);
                // OLED_Update();
            }
        }

        /* 短暂延时，避免过度占用CPU */
        Delay(10);
    }
}

/**
  * @brief  显示ADC测试结果
  * @param  stats: 统计数据结构体指针
  * @retval None
  */
void Display_ADC_Results(ADC_Stats_t *stats)
{
    /* 暂时注释掉显示功能，等字符显示函数完成 */
    /*
    // 清除旧的等待信息
    OLED_ShowString(0, 16, "            ", OLED_8X16);

    // 显示当前值
    sprintf(display_buffer, "Real: %4d", stats->current_value);
    OLED_ShowString(0, 16, display_buffer, OLED_8X16);

    // 显示最大值
    sprintf(display_buffer, "Max:  %4d", stats->max_value);
    OLED_ShowString(0, 32, display_buffer, OLED_8X16);

    // 显示最小值
    sprintf(display_buffer, "Min:  %4d", stats->min_value);
    OLED_ShowString(0, 48, display_buffer, OLED_8X16);

    // 更新显示
    OLED_Update();
    */
}

/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 系统时钟已经在SystemInit()中配置 */
    /* 这里可以添加额外的时钟配置代码 */
}

/**
  * @brief  毫秒延时函数
  * @param  ms: 延时毫秒数
  * @retval None
  */
void Delay(uint32_t ms)
{
    uint32_t tickstart = 0;
    tickstart = GetTick();
    while((GetTick() - tickstart) < ms)
    {
        // 防止编译器优化掉这个循环
        __NOP();
    }
}

/**
  * @brief  获取系统滴答计数
  * @param  None
  * @retval 当前滴答计数值
  */
uint32_t GetTick(void)
{
    return uwTick;
}

/**
  * @brief  SysTick相关的空减计数函数，供模板代码引用
  */
void TimingDelay_Decrement(void)
{
    /* 已使用uwTick实现延时，此处留空即可满足链接 */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  assert_failed: 发生参数错误时进入此函数
  * @param  file: 源文件名
  * @param  line: 行号
  */
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    (void)file;
    (void)line;
    while (1) {}
}
#endif

/* SysTick中断处理函数中使用的全局变量 */
__IO uint32_t uwTick = 0;
