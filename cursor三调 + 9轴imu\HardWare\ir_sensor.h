#ifndef __IR_SENSOR_H
#define __IR_SENSOR_H

#include "stm32f10x.h"
#include <stdint.h>

// 训练模式定义
typedef enum {
    MODE_LOW_FORCE,      // 低阻力康复模式
    MODE_MEDIUM_FORCE,   // 中等阻力康复模式
    MODE_HIGH_FORCE,     // 高阻力康复模式
    MODE_ADAPTIVE        // 自适应阻力模式
} TrainingMode_t;

// 传感器数据结构 - 与示例代码保持一致
typedef struct {
    uint16_t distance;   // 测量距离值
    uint8_t mode;        // 传感器模式
    uint8_t temp;        // 温度值（如有）
} IR_Sensor_t;

// 卡尔曼滤波器结构体
typedef struct {
  float x;    // 状态
  float A;    // x(n)=A*x(n-1)+u(n),u(n)~N(0,Q)
  float H;    // z(n)=H*x(n)+w(n),w(n)~N(0,R)
  float q;    // 过程噪声方差
  float r;    // 测量噪声方差
  float p;    // 估计误差协方差
  float gain; // 卡尔曼增益
} KalmanFilter;

// 函数声明
void VL53L0X_Init(void);                              // 初始化VL53L0X传感器
uint8_t VL53L0X_SetMeasurementMode(uint8_t mode);     // 设置测量模式
uint8_t VL53L0X_ReadDistance(uint16_t *distance);     // 读取测量距离
uint8_t Process_VL53L0X_Data(IR_Sensor_t *sensor_data); // 处理传感器数据
float ConvertDistanceToAngle(uint16_t distance);      // 距离转换为角度
TrainingMode_t DetermineTrainingMode(float joint_angle); // 确定训练模式

// 卡尔曼滤波器函数声明
void KalmanFilter_Init(KalmanFilter* filter, float q, float r, float p, float initial_value);
float KalmanFilter_Update(KalmanFilter* filter, float measurement);

#endif 
