#ifndef __USART_H
#define __USART_H
#include "stm32f10x.h"
#include <stdio.h>

// GY-56传感器使用USART2
void Usart_Int(uint32_t BaudRatePrescaler);
void USART1_send_byte(uint8_t byte);
void USART_Send_bytes(uint8_t *Buffer, uint8_t Length);
void USART_Send(uint8_t *Buffer, uint8_t Length);
void send_out(int16_t *data,uint8_t length,uint8_t send);
void send_3out(uint8_t *data,uint8_t length,uint8_t send);
void display(int16_t *num,u8 send,u8 count);
void CHeck(uint8_t *re_data);

// ESP8266模块函数声明 - 使用USART1(PA9/PA10)，但为保持兼容性保留函数名
void ESP8266_Usart_Init(uint32_t BaudRatePrescaler);
void USART2_send_byte(uint8_t byte);      // 实际使用USART1
void USART2_Send_bytes(uint8_t *Buffer, uint8_t Length); // 实际使用USART1

// IMU命令行相关常量
extern const char* IMU_CMD_PREFIX;
extern const char* IMU_OUTPUT_EULER;
extern const char* IMU_OUTPUT_QUATERNION;
extern const char* IMU_OUTPUT_EARTH_A;
extern const char* IMU_OUTPUT_ACC;
extern const char* IMU_OUTPUT_GYRO;
extern const char* IMU_OUTPUT_MAG;
extern const char* IMU_OUTPUT_ALL;
extern const char* IMU_CALI_AG;
extern const char* IMU_CALI_MAG;
extern const char* IMU_RESTORE;
extern const char* IMU_FREQ_PREFIX;

// IMU串口通信相关函数
void IMU_Usart_Init(uint32_t BaudRatePrescaler);
void USART3_send_byte(uint8_t byte);
void USART3_Send_bytes(uint8_t *Buffer, uint8_t Length);
void Process_IMU_Data(void);

// IMU命令行函数
void IMU_Send_Command(const char* command);
void IMU_Calibrate_AccGyro(void);
void IMU_Calibrate_Mag(void);
void IMU_Set_Output_Mode(const char* mode);
void IMU_Restore_Default(void);
void IMU_Set_Output_Frequency(uint8_t freq);

// 外部变量声明
extern u8 re_Buf_Data[50];
extern u8 Receive_ok;
extern u8 imu_Buf_Data[100];
extern u8 imu_Receive_ok;
extern uint16_t imu_rx_len;
#endif
