/**
  ******************************************************************************
  * @file    OLED_Test.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   0.96寸I2C OLED驱动测试版头文件
  ******************************************************************************
  */

#ifndef __OLED_TEST_H
#define __OLED_TEST_H

#include <stdint.h>

//==============================================================================
// 函数声明
//==============================================================================

// 基础驱动函数
void OLED_Init(void);
void OLED_Clear(void);
void OLED_Update(void);

// 低级驱动函数
void OLED_WriteCmd(uint8_t cmd);
void OLED_WriteData(uint8_t data);
void OLED_WriteByte(uint8_t data, uint8_t cmd);

// 延时函数
void OLED_Delay_ms(uint32_t ms);

// 测试函数
void OLED_Test_Display(void);

#endif /* __OLED_TEST_H */
