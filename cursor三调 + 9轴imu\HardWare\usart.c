#include "usart.h"
#include "string.h"
#include "stdio.h"
#include "sensor_fusion.h" // 添加传感器融合模块头文件

u8 re_Buf_Data[50]={0},Receive_ok=0;

// 添加NVIC_Configuration函数声明
void NVIC_Configuration(void);

// IMU数据接收缓冲区和状态标志
u8 imu_Buf_Data[100]={0};
u8 imu_Receive_ok=0;
uint16_t imu_rx_len = 0;

// IMU命令行相关
const char* IMU_CMD_PREFIX = "cmd ";
const char* IMU_OUTPUT_EULER = "output euler";
const char* IMU_OUTPUT_QUATERNION = "output quaternion";
const char* IMU_OUTPUT_EARTH_A = "output earth_a";
const char* IMU_OUTPUT_ACC = "output acc";
const char* IMU_OUTPUT_GYRO = "output gyro";
const char* IMU_OUTPUT_MAG = "output mag";
const char* IMU_OUTPUT_ALL = "output acc_gyro_mag";
const char* IMU_CALI_AG = "cali a+g";
const char* IMU_CALI_MAG = "cali mag";
const char* IMU_RESTORE = "restore";
const char* IMU_FREQ_PREFIX = "freq ";

// 修改GY-56传感器初始化函数，使用USART2(PA2/PA3)
void Usart_Int(uint32_t BaudRatePrescaler)
{
	GPIO_InitTypeDef GPIO_usartx;
	USART_InitTypeDef Usart_X;
	NVIC_InitTypeDef NVIC_InitStructure;
	/////////////////////////////////
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	
	// USART2_TX   PA2
	GPIO_usartx.GPIO_Pin = GPIO_Pin_2;
	GPIO_usartx.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_usartx.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_Init(GPIOA, &GPIO_usartx);
	
	// USART2_RX   PA3
	GPIO_usartx.GPIO_Pin = GPIO_Pin_3;
	GPIO_usartx.GPIO_Mode = GPIO_Mode_IN_FLOATING;
	GPIO_Init(GPIOA, &GPIO_usartx);
	
	Usart_X.USART_BaudRate=BaudRatePrescaler;
	Usart_X.USART_WordLength=USART_WordLength_8b;//8位数据格式
	Usart_X.USART_StopBits=USART_StopBits_1;//1位停止位
	Usart_X.USART_Parity=USART_Parity_No;
	Usart_X.USART_HardwareFlowControl=USART_HardwareFlowControl_None;
	Usart_X.USART_Mode= USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART2, &Usart_X);
  
	// 配置USART2中断
	NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);
	
	// 使能USART2接收中断
	USART_ITConfig(USART2, USART_IT_RXNE, ENABLE);
  
	// 使能USART2
	USART_Cmd(USART2, ENABLE);
	/////////////////////////////////
}

// IMU串口初始化函数（使用USART3，PB13/PB14）
void IMU_Usart_Init(uint32_t BaudRatePrescaler)
{
	GPIO_InitTypeDef GPIO_usartx;
	USART_InitTypeDef Usart_X;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	// 使能USART3、GPIOB和AFIO时钟
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB | RCC_APB2Periph_AFIO, ENABLE);
	
	// 设置USART3重映射，使用PB13/PB14
	GPIO_PinRemapConfig(GPIO_PartialRemap_USART3, ENABLE);
	
	// USART3_TX   PB13
	GPIO_usartx.GPIO_Pin = GPIO_Pin_13;
	GPIO_usartx.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_usartx.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_Init(GPIOB, &GPIO_usartx);
	
	// USART3_RX   PB14
	GPIO_usartx.GPIO_Pin = GPIO_Pin_14;
	GPIO_usartx.GPIO_Mode = GPIO_Mode_IN_FLOATING;
	GPIO_Init(GPIOB, &GPIO_usartx);
	
	// USART3参数配置
	Usart_X.USART_BaudRate = BaudRatePrescaler;
	Usart_X.USART_WordLength = USART_WordLength_8b;
	Usart_X.USART_StopBits = USART_StopBits_1;
	Usart_X.USART_Parity = USART_Parity_No;
	Usart_X.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	Usart_X.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART3, &Usart_X);
	
	// 配置USART3中断
	NVIC_InitStructure.NVIC_IRQChannel = USART3_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);
	
	// 使能USART3接收中断
	USART_ITConfig(USART3, USART_IT_RXNE, ENABLE);
	
	// 使能USART3
	USART_Cmd(USART3, ENABLE);
}

//重定向fputc函数 
int fputc(int ch, FILE *f)
{      
	while(USART_GetFlagStatus(USART1,USART_FLAG_TC)==RESET);  
	USART1->DR = (uint8_t) ch;      
	return ch;
}
//发送一个字节数据
//input:byte,要发送的数据
void USART1_send_byte(uint8_t byte)
{
	while(USART_GetFlagStatus(USART1,USART_FLAG_TC)==RESET);//等等发送结束
	USART1->DR=byte;	
}

// 发送一个字节数据到USART3
void USART3_send_byte(uint8_t byte)
{
	while(USART_GetFlagStatus(USART3, USART_FLAG_TC)==RESET);
	USART3->DR = byte;
}

//发送多字节数据
void USART_Send_bytes(uint8_t *Buffer, uint8_t Length)
{
	uint8_t i=0;
	while(i<Length)
	{
		USART1_send_byte(Buffer[i++]);
	}
}

// 从USART3发送多字节数据
void USART3_Send_bytes(uint8_t *Buffer, uint8_t Length)
{
	uint8_t i=0;
	while(i<Length)
	{
		USART3_send_byte(Buffer[i++]);
	}
}

//发送多字节数据+校验和
void USART_Send(uint8_t *Buffer, uint8_t Length)
{
	uint8_t i=0;
	while(i<Length)
	{
		if(i<(Length-1))
		Buffer[Length-1]+=Buffer[i];//累加Length-1前所有数
		USART1_send_byte(Buffer[i++]);
	}
}
void send_3out(uint8_t *data,uint8_t length,uint8_t send)
{
	uint8_t TX_DATA[30]={0},i=0,k=0;
	TX_DATA[i++]=0X5A;//帧头
	TX_DATA[i++]=0X5A;//帧头
	TX_DATA[i++]=send;//功能字节
	TX_DATA[i++]=length;//数据个数
	for(k=0;k<length;k++)//填充数据到缓存TX_DATA
	{
		TX_DATA[i++]=(uint8_t)data[k];
	}
	USART_Send(TX_DATA,length+5);	
}
//发送一帧数据
void send_out(int16_t *data,uint8_t length,uint8_t send)
{
	uint8_t TX_DATA[30],i=0,k=0;
	memset(TX_DATA,0,(2*length+5));//清空缓存TX_DATA
	TX_DATA[i++]=0X5A;//帧头
	TX_DATA[i++]=0X5A;//帧头
	TX_DATA[i++]=send;//功能字节
	TX_DATA[i++]=2*length;//数据个数
	for(k=0;k<length;k++)//填充数据到缓存TX_DATA
	{
		TX_DATA[i++]=(uint16_t)data[k]>>8;
		TX_DATA[i++]=(uint16_t)data[k];
	}
	USART_Send(TX_DATA,2*length+5);	
}
	void display(int16_t *num,u8 send,u8 count)
{
	  u8 i=0;
		USART1_send_byte(0X0d);
		USART1_send_byte(0X0a);
		USART1_send_byte(send);
	  USART1_send_byte(':');
		for(i=0;i<count;i++)
		{
			if(num[i]<0)
			{
				num[i]=-num[i];
				USART1_send_byte('-');
			}
			else
				USART1_send_byte('+');
		
			USART1_send_byte(0x30|(num[i]/10000));	
			USART1_send_byte(0x30|(num[i]%10000/1000));
			USART1_send_byte(0x30|(num[i]%1000/100));
			//USART_send_byte(0x2e);
			USART1_send_byte(0x30|(num[i]%100/10));
			USART1_send_byte(0x30|(num[i]%10));
			USART1_send_byte(',');	
	}
}
// NVIC中断配置函数
void NVIC_Configuration(void)
{
  NVIC_InitTypeDef NVIC_X;
  
  /* 4个抢占优先级，4个响应优先级 */
  NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
  /*抢占优先级可打断中断级别低的中断*/
  /*响应优先级按等级执行*/
  NVIC_X.NVIC_IRQChannel = USART1_IRQn;//中断向量
  NVIC_X.NVIC_IRQChannelPreemptionPriority = 0;//抢占优先级
  NVIC_X.NVIC_IRQChannelSubPriority = 0;//响应优先级
  NVIC_X.NVIC_IRQChannelCmd = ENABLE;//使能中断响应
  NVIC_Init(&NVIC_X);
}

// 发送IMU命令行指令 - 优化版本
void IMU_Send_Command(const char* command)
{
    uint8_t i = 0;
    // 直接发送命令
    while(command[i] != '\0')
    {
        USART3_send_byte(command[i++]);
    }
    
    // 添加回车换行
    USART3_send_byte('\r');
    USART3_send_byte('\n');
}

// 校准加速度计和陀螺仪
void IMU_Calibrate_AccGyro(void)
{
    // 直接发送固定命令
    IMU_Send_Command("cmd cali a+g");
}

// 校准磁力计
void IMU_Calibrate_Mag(void)
{
    // 直接发送固定命令
    IMU_Send_Command("cmd cali mag");
}

// 设置输出模式
void IMU_Set_Output_Mode(const char* mode)
{
    // 直接使用字符常量，减少内存操作
    USART3_send_byte('c');
    USART3_send_byte('m');
    USART3_send_byte('d');
    USART3_send_byte(' ');
    
    // 发送mode参数
    uint8_t i = 0;
    while(mode[i] != '\0')
    {
        USART3_send_byte(mode[i++]);
    }
    
    // 添加回车换行
    USART3_send_byte('\r');
    USART3_send_byte('\n');
}

// 恢复默认设置
void IMU_Restore_Default(void)
{
    // 直接发送固定命令
    IMU_Send_Command("cmd restore");
}

// 设置输出频率 - 优化版本
void IMU_Set_Output_Frequency(uint8_t freq)
{
    // 使用固定字符串+数字
    USART3_send_byte('c');
    USART3_send_byte('m');
    USART3_send_byte('d');
    USART3_send_byte(' ');
    USART3_send_byte('f');
    USART3_send_byte('r');
    USART3_send_byte('e');
    USART3_send_byte('q');
    USART3_send_byte(' ');
    
    // 转换数字到ASCII (最多支持0-99)
    if(freq >= 10)
    {
        USART3_send_byte('0' + (freq / 10));
        USART3_send_byte('0' + (freq % 10));
    }
    else
    {
        USART3_send_byte('0' + freq);
    }
    
    // 添加回车换行
    USART3_send_byte('\r');
    USART3_send_byte('\n');
}

// 处理IMU接收到的数据
void Process_IMU_Data(void)
{
    if(imu_Receive_ok)
    {
        imu_Receive_ok = 0; // 清除接收标志
        
        // 确保字符串以NULL结尾
        if(imu_rx_len < sizeof(imu_Buf_Data))
        {
            imu_Buf_Data[imu_rx_len] = '\0';
            
            // 解析IMU数据
            Parse_IMU_Data((char*)imu_Buf_Data, &imu_data);
        }
        
        // 重置接收计数，准备接收下一条数据
        imu_rx_len = 0;
    }
}

// ESP8266模块串口初始化函数（修改为使用USART1，PA9/PA10）
void ESP8266_Usart_Init(uint32_t BaudRatePrescaler)
{
	GPIO_InitTypeDef GPIO_usartx;
	USART_InitTypeDef Usart_X;
	NVIC_InitTypeDef NVIC_InitStructure;
	
	// 使能USART1、GPIOA时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1 | RCC_APB2Periph_GPIOA, ENABLE);
	
	// USART1_TX   PA9
	GPIO_usartx.GPIO_Pin = GPIO_Pin_9;
	GPIO_usartx.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_usartx.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_Init(GPIOA, &GPIO_usartx);
	
	// USART1_RX   PA10
	GPIO_usartx.GPIO_Pin = GPIO_Pin_10;
	GPIO_usartx.GPIO_Mode = GPIO_Mode_IN_FLOATING;
	GPIO_Init(GPIOA, &GPIO_usartx);
	
	// USART1参数配置
	Usart_X.USART_BaudRate = BaudRatePrescaler;
	Usart_X.USART_WordLength = USART_WordLength_8b;
	Usart_X.USART_StopBits = USART_StopBits_1;
	Usart_X.USART_Parity = USART_Parity_No;
	Usart_X.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	Usart_X.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART1, &Usart_X);
	
	// 配置USART1中断
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);
	
	// 使能USART1接收中断
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
	
	// 使能USART1
	USART_Cmd(USART1, ENABLE);
}

// USART2发送一个字节数据(修改为USART1)
void USART2_send_byte(uint8_t byte)
{
	while(USART_GetFlagStatus(USART1, USART_FLAG_TC)==RESET);
	USART1->DR = byte;
}

// USART2发送多字节数据(修改为USART1)
void USART2_Send_bytes(uint8_t *Buffer, uint8_t Length)
{
	uint8_t i=0;
	while(i<Length)
	{
		USART2_send_byte(Buffer[i++]);
	}
}
