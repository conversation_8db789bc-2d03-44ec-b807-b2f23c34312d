.\objects\serial.o: Hardware\Serial.c
.\objects\serial.o: .\User\stm32f4xx.h
.\objects\serial.o: .\Start\core_cm4.h
.\objects\serial.o: E:\Keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\serial.o: .\Start\core_cmInstr.h
.\objects\serial.o: .\Start\core_cmFunc.h
.\objects\serial.o: .\Start\core_cmSimd.h
.\objects\serial.o: .\User\system_stm32f4xx.h
.\objects\serial.o: .\User\stm32f4xx_conf.h
.\objects\serial.o: .\Library\inc\stm32f4xx_adc.h
.\objects\serial.o: .\User\stm32f4xx.h
.\objects\serial.o: .\Library\inc\stm32f4xx_crc.h
.\objects\serial.o: .\Library\inc\stm32f4xx_dbgmcu.h
.\objects\serial.o: .\Library\inc\stm32f4xx_dma.h
.\objects\serial.o: .\Library\inc\stm32f4xx_exti.h
.\objects\serial.o: .\Library\inc\stm32f4xx_flash.h
.\objects\serial.o: .\Library\inc\stm32f4xx_gpio.h
.\objects\serial.o: .\Library\inc\stm32f4xx_i2c.h
.\objects\serial.o: .\Library\inc\stm32f4xx_iwdg.h
.\objects\serial.o: .\Library\inc\stm32f4xx_pwr.h
.\objects\serial.o: .\Library\inc\stm32f4xx_rcc.h
.\objects\serial.o: .\Library\inc\stm32f4xx_rtc.h
.\objects\serial.o: .\Library\inc\stm32f4xx_sdio.h
.\objects\serial.o: .\Library\inc\stm32f4xx_spi.h
.\objects\serial.o: .\Library\inc\stm32f4xx_syscfg.h
.\objects\serial.o: .\Library\inc\stm32f4xx_tim.h
.\objects\serial.o: .\Library\inc\stm32f4xx_usart.h
.\objects\serial.o: .\Library\inc\stm32f4xx_wwdg.h
.\objects\serial.o: .\Library\inc\misc.h
.\objects\serial.o: .\Library\inc\stm32f4xx_cryp.h
.\objects\serial.o: .\Library\inc\stm32f4xx_hash.h
.\objects\serial.o: .\Library\inc\stm32f4xx_rng.h
.\objects\serial.o: .\Library\inc\stm32f4xx_can.h
.\objects\serial.o: .\Library\inc\stm32f4xx_dac.h
.\objects\serial.o: .\Library\inc\stm32f4xx_dcmi.h
.\objects\serial.o: .\Library\inc\stm32f4xx_fsmc.h
.\objects\serial.o: E:\Keil5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\serial.o: E:\Keil5\ARM\ARMCC\Bin\..\include\stdarg.h
